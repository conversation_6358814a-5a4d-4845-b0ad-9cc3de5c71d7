import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, forwardRef, Input, Output, ViewChild, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { TranslateModule } from '@ngx-translate/core';

/**
 * Component ChipFormField
 * Cho phép nhập nhiều giá trị, hỗ trợ autocomplete, và tạo giá trị mới khi nhấn Enter
 */
@Component({
  selector: 'app-chip-form-field',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatChipsModule,
    MatIconModule,
    MatInputModule,
    MatAutocompleteModule,
    TranslateModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ChipFormFieldComponent),
      multi: true
    }
  ],
  templateUrl: './chip-form-field.component.html',
  styleUrls: ['./chip-form-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChipFormFieldComponent implements ControlValueAccessor {
  /**
   * Danh sách các giá trị chip đã chọn
   */
  values = signal<string[]>([]);

  /**
   * Danh sách gợi ý đã lọc
   */
  filteredSuggestions = signal<string[]>([]);

  /**
   * Danh sách gợi ý ban đầu
   */
  private _suggestions: string[] = [];

  /**
   * Getter cho suggestions
   */
  get suggestions(): string[] {
    return this._suggestions;
  }

  /**
   * Setter cho suggestions
   */
  @Input() set suggestions(value: string[]) {
    this._suggestions = value || [];
    this.filteredSuggestions.set(this._suggestions);
  }

  /**
   * Label hiển thị cho trường input
   */
  @Input() label = 'Chọn giá trị';

  /**
   * Placeholder hiển thị cho trường input
   */
  @Input() placeholder = 'Nhập giá trị';

  /**
   * Thông báo lỗi khi trường input không hợp lệ
   */
  @Input() errorMsg = '';

  /**
   * Sự kiện phát ra khi danh sách giá trị thay đổi
   */
  @Output() valuesChange = new EventEmitter<string[]>();

  /**
   * Reference đến trường input
   */
  @ViewChild('chipInput') chipInput!: ElementRef<HTMLInputElement>;

  /**
   * Danh sách các phím tắt để thêm chip
   */
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  /**
   * Giá trị hiện tại của trường input
   */
  inputValue = '';

  /**
   * Hàm callback khi giá trị thay đổi
   */
  private onChange: (value: string[]) => void = () => {};

  /**
   * Hàm callback khi trường input được chạm vào
   */
  private onTouched: () => void = () => {};

  /**
   * Thêm một chip mới từ sự kiện input
   * @param event Sự kiện từ MatChipInput
   */
  add(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();

    if (value) {
      this.addValue(value);
    }

    // Reset input value
    event.chipInput!.clear();
    this.inputValue = '';
  }

  /**
   * Xóa một chip
   * @param value Giá trị cần xóa
   */
  remove(value: string): void {
    const currentValues = this.values();
    const index = currentValues.indexOf(value);

    if (index >= 0) {
      const newValues = [...currentValues];
      newValues.splice(index, 1);
      this.values.set(newValues);
      this.onChange(this.values());
      this.valuesChange.emit(this.values());

      // Cập nhật lại danh sách gợi ý khi xóa chip
      this.filter();
    }
  }

  /**
   * Xử lý sự kiện khi chọn một giá trị từ autocomplete
   * @param event Sự kiện từ MatAutocomplete
   */
  selected(event: MatAutocompleteSelectedEvent): void {
    this.addValue(event.option.viewValue);
    this.chipInput.nativeElement.value = '';
    this.inputValue = '';

    // Cập nhật lại danh sách gợi ý sau khi chọn
    this.filter();
  }

  /**
   * Lọc danh sách gợi ý dựa trên giá trị input
   */
  filter(): void {
    const filterValue = this.inputValue.toLowerCase();

    // Chỉ hiển thị các suggestion chưa được chọn
    const availableSuggestions = this._suggestions.filter(suggestion =>
      !this.values().includes(suggestion)
    );

    // Lọc theo giá trị input từ các suggestion còn lại
    const filtered = availableSuggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(filterValue)
    );

    this.filteredSuggestions.set(filtered);
  }

  /**
   * Thêm một giá trị mới vào danh sách chip
   * @param value Giá trị cần thêm
   */
  private addValue(value: string): void {
    // Kiểm tra xem giá trị đã tồn tại chưa
    if (!this.values().includes(value)) {
      const newValues = [...this.values(), value];
      this.values.set(newValues);
      this.onChange(newValues);
      this.valuesChange.emit(newValues);
    }
  }

  /**
   * Thiết lập giá trị từ bên ngoài
   * @param value Danh sách giá trị
   */
  writeValue(value: string[]): void {
    this.values.set(value || []);
  }

  /**
   * Đăng ký hàm callback khi giá trị thay đổi
   * @param fn Hàm callback
   */
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  /**
   * Đăng ký hàm callback khi trường input được chạm vào
   * @param fn Hàm callback
   */
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
}
