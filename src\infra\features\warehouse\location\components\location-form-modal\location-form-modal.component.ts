import { Component, OnInit, Inject, Optional, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { LocationFormComponent } from '../location-form/location-form.component';
import { LocationFormData, Location } from '../../models/api/location.dto';
import { LocationService } from '../../services/location.service';

export interface LocationFormModalData {
  editMode: boolean;
  locationId?: string;
  parentId?: string | null;
  currentLevel?: number;
}

@Component({
  selector: 'app-location-form-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatButtonModule,
    TranslateModule,
    LocationFormComponent
  ],
  templateUrl: './location-form-modal.component.html',
  styleUrls: ['./location-form-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationFormModalComponent implements OnInit {
  dialogTitle = '';

  private dialogRef?: MatDialogRef<LocationFormModalComponent>;
  private bottomSheetRef?: MatBottomSheetRef<LocationFormModalComponent>;
  data: LocationFormModalData;

  constructor(
    private locationService: LocationService,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: LocationFormModalData | null,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: LocationFormModalData | null,
    @Optional() dialogRefInject?: MatDialogRef<LocationFormModalComponent>,
    @Optional() bottomSheetRefInject?: MatBottomSheetRef<LocationFormModalComponent>
  ) {
    this.dialogRef = dialogRefInject;
    this.bottomSheetRef = bottomSheetRefInject;
    this.data = this.dialogData || this.bottomSheetData || { editMode: false };
  }

  ngOnInit(): void {
    this.dialogTitle = this.data.editMode
      ? 'WAREHOUSE.LOCATION.EDIT'
      : 'WAREHOUSE.LOCATION.CREATE';
  }

  onFormSubmit(formData: LocationFormData): void {
    if (this.data.editMode && this.data.locationId) {
      this.locationService.updateLocation(this.data.locationId, formData).subscribe({
        next: (location) => {
          this.closeModal(location);
        },
        error: (error) => {
          console.error('Error updating location:', error);
          this.closeModal(null);
        }
      });
    } else {
      this.locationService.createLocation(formData).subscribe({
        next: (location) => {
          this.closeModal(location);
        },
        error: (error) => {
          console.error('Error creating location:', error);
          this.closeModal(null);
        }
      });
    }
  }

  onFormCancel(): void {
    this.closeModal(null);
  }

  private closeModal(result: Location | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
