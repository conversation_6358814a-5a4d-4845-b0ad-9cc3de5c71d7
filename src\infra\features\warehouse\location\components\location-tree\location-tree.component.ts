import { Component, Output, EventEmitter, ChangeDetectionStrategy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeModule } from 'primeng/tree';
import { TreeNode } from 'primeng/api';
import { TranslateModule } from '@ngx-translate/core';
import { LocationNode } from '../../models/view/location-view.model';
import { LocationService } from '../../services/location.service';

@Component({
  selector: 'app-location-tree',
  standalone: true,
  imports: [CommonModule, TreeModule, TranslateModule],
  templateUrl: './location-tree.component.html',
  styleUrls: ['./location-tree.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationTreeComponent {
  @Output() selectLocation = new EventEmitter<LocationNode>();

  locationNodes = signal<TreeNode[]>([]);
  selectedLocation = signal<TreeNode | null>(null);

  constructor(private locationService: LocationService) {
    this.loadLocations();
  }

  private loadLocations(): void {
    this.locationService.getLocations().subscribe(locations => {
      this.locationNodes.set(this.locationService.convertToTreeNodes(locations));
    });
  }

  onNodeSelect(event: any): void {
    this.selectLocation.emit(event.node.data);
  }

  refreshTree(): void {
    this.loadLocations();
  }
}
