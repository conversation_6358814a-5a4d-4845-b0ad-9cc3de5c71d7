import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// Angular Material imports
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { Field } from '@domain/entities/field.entity';
import { 
  BaseFilterInput, 
  NumberFilterValue,
  RangeValue 
} from '../base-filter-input.interface';

/**
 * Specialized component cho number-based filter inputs
 * Handles: number, decimal, currency, percent field types
 * Operators: equals, not_equals, less_than, less_than_or_equal, greater_than, greater_than_or_equal, between, not_between, is_empty, is_not_empty
 */
@Component({
  selector: 'app-number-filter-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule
  ],
  templateUrl: './number-filter-input.component.html',
  styleUrls: ['./number-filter-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NumberFilterInputComponent implements BaseFilterInput, OnInit, OnDestroy {
  @Input() field!: Field;
  @Input() operator!: string;
  @Input() value?: number | RangeValue;
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<number | RangeValue>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Signals cho reactive state management
  readonly singleValue = signal<number | null>(null);
  readonly minValue = signal<number | null>(null);
  readonly maxValue = signal<number | null>(null);
  readonly isValid = signal<boolean>(true);

  // Computed properties
  readonly requiresInput = computed(() => {
    return !['is_empty', 'is_not_empty'].includes(this.operator);
  });

  readonly isRangeOperator = computed(() => {
    return ['between', 'not_between'].includes(this.operator);
  });

  readonly inputStep = computed(() => {
    switch (this.field?.type) {
      case 'decimal':
      case 'currency':
      case 'percent':
        return 0.01;
      default:
        return 1;
    }
  });

  readonly currencySymbol = computed(() => {
    return this.field?.type === 'currency' ? 'VND' : '';
  });

  readonly percentSymbol = computed(() => {
    return this.field?.type === 'percent' ? '%' : '';
  });

  constructor() {
    // Effect để sync với external value
    effect(() => {
      if (this.value !== undefined) {
        if (this.isRangeOperator()) {
          const rangeValue = this.value as RangeValue;
          this.minValue.set(rangeValue.minValue || null);
          this.maxValue.set(rangeValue.maxValue || null);
        } else {
          this.singleValue.set(this.value as number || null);
        }
      }
    });

    // Effect để emit changes
    effect(() => {
      const valid = this.validate();
      this.isValid.set(valid);
      
      let currentValue: number | RangeValue;
      if (this.isRangeOperator()) {
        currentValue = {
          minValue: this.minValue(),
          maxValue: this.maxValue()
        };
      } else {
        currentValue = this.singleValue() || 0;
      }
      
      this.valueChange.emit(currentValue);
      this.validationChange.emit(valid);
    });
  }

  ngOnInit(): void {
    // Initialize với value nếu có
    if (this.value !== undefined) {
      if (this.isRangeOperator()) {
        const rangeValue = this.value as RangeValue;
        this.minValue.set(rangeValue.minValue || null);
        this.maxValue.set(rangeValue.maxValue || null);
      } else {
        this.singleValue.set(this.value as number || null);
      }
    }
  }

  ngOnDestroy(): void {
    // Cleanup nếu cần
  }

  /**
   * Handle single value input change
   */
  onSingleValueChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value ? parseFloat(target.value) : null;
    this.singleValue.set(value);
  }

  /**
   * Handle min value change cho range operators
   */
  onMinValueChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value ? parseFloat(target.value) : null;
    this.minValue.set(value);
  }

  /**
   * Handle max value change cho range operators
   */
  onMaxValueChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value ? parseFloat(target.value) : null;
    this.maxValue.set(value);
  }

  /**
   * Validate current input values
   */
  validate(): boolean {
    if (!this.requiresInput()) {
      return true;
    }

    if (this.isRangeOperator()) {
      const min = this.minValue();
      const max = this.maxValue();
      
      // Both values required cho range operators
      if (min === null || max === null) {
        return false;
      }
      
      // Min phải <= Max
      return min <= max;
    } else {
      // Single value required
      return this.singleValue() !== null;
    }
  }

  /**
   * Reset input values
   */
  reset(): void {
    this.singleValue.set(null);
    this.minValue.set(null);
    this.maxValue.set(null);
  }

  /**
   * Get current filter value trong format chuẩn
   */
  getCurrentFilterValue(): NumberFilterValue {
    if (this.isRangeOperator()) {
      return {
        operator: this.operator,
        minValue: this.minValue() || 0,
        maxValue: this.maxValue() || 0
      };
    } else {
      return {
        operator: this.operator,
        value: this.singleValue() || 0
      };
    }
  }
}
