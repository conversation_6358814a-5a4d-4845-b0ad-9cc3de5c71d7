import { Template } from "@/shared/models/view/dynamic-layout-builder.model";

export const mockProductLayoutTemplate: Template[] = [
  {
    name: 'Thời trang cơ bản',
    sections: [
      {
        id: 'personal-info',
        title: 'Thông tin cá nhân',
        fields: [
          { id: 1, type: 'text', label: 'Họ và tên', required: true, order: 1 },
          { id: 2, type: 'phone', label: 'Số điện thoại', required: true, order: 2 },
          { id: 3, type: 'email', label: 'Email', required: false, order: 3 }
        ]
      },
      {
        id: 'fashion-preferences',
        title: 'Sở thích thời trang',
        fields: [
          { id: 4, type: 'size', label: '<PERSON>ze thường mặc', required: true, order: 1 },
          { id: 5, type: 'color', label: '<PERSON><PERSON><PERSON> sắc yêu thích', required: false, order: 2 },
          { id: 6, type: 'select', label: '<PERSON>ong cách', required: false, order: 3 }
        ]
      }
    ]
  },
  {
    name: '<PERSON><PERSON> <PERSON>h<PERSON><PERSON> cơ bản',
    sections: [
      {
        id: 'personal-info',
        title: 'Thông tin cá nhân',
        fields: [
          { id: 1, type: 'text', label: 'Họ và tên', required: true, order: 1 },
          { id: 2, type: 'phone', label: 'Số điện thoại', required: true, order: 2 },
          { id: 3, type: 'date', label: 'Ngày sinh', required: false, order: 3 }
        ]
      },
      {
        id: 'skin-info',
        title: 'Thông tin da',
        fields: [
          { id: 4, type: 'select', label: 'Loại da', required: true, order: 1 },
          { id: 5, type: 'checkbox', label: 'Vấn đề da', required: false, order: 2 },
          { id: 6, type: 'textarea', label: 'Ghi chú đặc biệt', required: false, order: 3 }
        ]
      }
    ]
  },
  {
    name: 'Thực phẩm cơ bản',
    sections: [
      {
        id: 'personal-info',
        title: 'Thông tin cá nhân',
        fields: [
          { id: 1, type: 'text', label: 'Họ và tên', required: true, order: 1 },
          { id: 2, type: 'phone', label: 'Số điện thoại', required: true, order: 2 },
          { id: 3, type: 'text', label: 'Địa chỉ giao hàng', required: true, order: 3 }
        ]
      },
      {
        id: 'food-preferences',
        title: 'Sở thích ẩm thực',
        fields: [
          { id: 4, type: 'checkbox', label: 'Dị ứng thực phẩm', required: false, order: 1 },
          { id: 5, type: 'select', label: 'Khẩu vị', required: false, order: 2 },
          { id: 6, type: 'textarea', label: 'Ghi chú đặc biệt', required: false, order: 3 }
        ]
      }
    ]
  }
];