import { ApplicationConfig, importProviders<PERSON>rom, inject, Injector, provideAppInitializer, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, Router, withHashLocation, withInMemoryScrolling } from '@angular/router';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getMessaging, provideMessaging } from '@angular/fire/messaging';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { AuthInitializer, initializeAuth } from '@core/initializers/auth.initializer';
import { provideToastr } from 'ngx-toastr';
import { provideHttpClient, withInterceptors, HttpClient } from '@angular/common/http';
import { MAT_DATE_LOCALE, MatNativeDateModule } from '@angular/material/core';
import { provideEnvironmentNgxMask } from 'ngx-mask';
import { provideServiceWorker } from '@angular/service-worker';
import { providePrimeNG } from 'primeng/config';
import Aura from '@primeng/themes/aura';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { mockInterceptor } from '@core/interceptors/mock.interceptor';
import { register } from 'swiper/element/bundle';
import { routes } from './app.routes';
import { FIREBASE_CONFIGS } from './config/firebase.config';
import { authInterceptor } from './core/interceptors/auth.interceptor';
import { errorInterceptor } from './core/interceptors/error.interceptor';
import { loaderInterceptor } from './core/interceptors/loader.interceptor';
import { createTranslateLoader } from './core/services/translate-http-loader';
import { I18nService } from './core/services/i18n.service';
import { Location } from '@angular/common';
import { RouterEventService } from './core/services/router_events.service';

// Đăng ký Swiper custom elements - chạy một lần duy nhất
// Lý tưởng nhất là gọi hàm này trong main.ts hoặc app.config.ts
register();

export const appConfig: ApplicationConfig = {
  providers: [
    provideAppInitializer(() => initializeAuth(inject(AuthInitializer))),
    provideRouter(routes, withHashLocation()),
    // scroll top on change router
    provideRouter(routes, withInMemoryScrolling({
      scrollPositionRestoration: 'top'
    })),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideFirebaseApp(
      () => initializeApp(FIREBASE_CONFIGS)
    ),
    provideMessaging(() => getMessaging()),
    provideAnimationsAsync(),
    provideToastr({
      maxOpened: 5
    }),
    provideHttpClient(),
    provideEnvironmentNgxMask(),
    importProvidersFrom(MatNativeDateModule),
    importProvidersFrom(
      TranslateModule.forRoot({
        defaultLanguage: 'vi',
        loader: {
          provide: TranslateLoader,
          useFactory: createTranslateLoader,
          deps: [HttpClient,  Location, RouterEventService, Injector]
        }
      })
    ),
    // Đăng ký I18nService để khởi tạo ngôn ngữ từ localStorage
    {
      provide: I18nService,
      useClass: I18nService
    },
    {
      provide: MAT_DATE_LOCALE,
      useValue: 'vi-VN'
    },

    provideServiceWorker('ngsw-worker.js', {
      // enabled: !isDevMode(),
      enabled: true,
      // Register the ServiceWorker as soon as the application is stable
      // or after 30 seconds (whichever comes first).
      // registrationStrategy: 'registerWhenStable:30000'
      registrationStrategy: 'registerImmediately'
    }),
    provideHttpClient(
      withInterceptors([
        authInterceptor,
        errorInterceptor,
        loaderInterceptor,
        mockInterceptor
      ])
    ),
    providePrimeNG({
      theme: {
        preset: Aura,
        options: {
          darkModeSelector: false || 'none',
          ripple: true
        }
      }
    })
  ]
};
