import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@core/services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    FormsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  input = {
    email: '',
    password: ''
  };

  err!: string;

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  resetError() {
    if(this.err) {
      this.err = '';
    }
  }

  submit() {
    this.authService.login(this.input)
      .subscribe({
        next: async () => {
          let redirectTo = this.route.snapshot.queryParams['redirect'] || '/';
          if(redirectTo === '/logout') {
            redirectTo = '/';
          }

          this.router.navigateByUrl(redirectTo);
        },
        error: (e) => {
          console.log(e);
          this.err = e.error?.message ?? e.error?.code ?? e.message ?? `Lỗi máy chủ: ${e.status}`;
        }
      });
  }
}
