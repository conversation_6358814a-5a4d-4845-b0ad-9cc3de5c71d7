import { Injectable, signal, computed, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { delay, tap, map } from 'rxjs/operators';

import { GoldenLayoutConfig, Section, LayoutState, LayoutContentItem } from '@shared/models/view/dynamic-layout-builder.model';

/**
 * GoldenLayoutService - Service quản lý cấu hình và trạng thái Golden Layout
 *
 * Chức năng chính:
 * - Quản lý cấu hình Golden Layout
 * - X<PERSON> lý các sự kiện của Golden Layout
 * - Đồng bộ trạng thái với DynamicLayoutBuilderComponent
 * - Lưu/khôi phục layout configuration
 * - Quản lý panels và tabs
 */
@Injectable({
  providedIn: 'root'
})
export class GoldenLayoutService implements OnDestroy {

  // BehaviorSubjects để quản lý trạng thái
  private configSubject = new BehaviorSubject<GoldenLayoutConfig | null>(null);
  private isInitializedSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);
  private layoutStateSubject = new BehaviorSubject<LayoutState | null>(null);

  // Public observables
  public config$ = this.configSubject.asObservable();
  public isInitialized$ = this.isInitializedSubject.asObservable();
  public error$ = this.errorSubject.asObservable();
  public layoutState$ = this.layoutStateSubject.asObservable();

  // Signals cho reactive state management
  public currentConfig = signal<GoldenLayoutConfig | null>(null);
  public isInitialized = signal<boolean>(false);
  public isLoading = signal<boolean>(false);
  public panelCount = signal<number>(0);

  // Computed signals
  public hasConfig = computed(() => this.currentConfig() !== null);
  public configSummary = computed(() => {
    const config = this.currentConfig();
    if (!config) return null;

    return {
      totalPanels: this.panelCount(),
      hasMainPanel: config.content?.some(item => item.type === 'component'),
      hasTabStack: config.content?.some(item => item.type === 'stack')
    };
  });

  // Key cho localStorage
  private readonly GOLDEN_LAYOUT_STORAGE_KEY = 'golden-layout-config';

  // Subscription management
  private subscriptions = new Subscription();

  constructor() {
    // Subscribe to BehaviorSubject để cập nhật signals
    this.subscriptions.add(
      this.config$.subscribe(config => {
        this.currentConfig.set(config);
      })
    );

    this.subscriptions.add(
      this.isInitialized$.subscribe(initialized => {
        this.isInitialized.set(initialized);
      })
    );

    // Load cấu hình từ localStorage khi khởi tạo
    this.loadConfigFromStorage();
  }

  /**
   * Cleanup subscriptions khi service bị destroy
   */
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();

    // Complete BehaviorSubjects để tránh memory leaks
    this.configSubject.complete();
    this.isInitializedSubject.complete();
    this.errorSubject.complete();
    this.layoutStateSubject.complete();
  }

  /**
   * Khởi tạo Golden Layout với cấu hình mặc định
   */
  initializeLayout(sections: Section[]): Observable<GoldenLayoutConfig> {
    this.isLoading.set(true);

    return of(this.createDefaultConfig(sections)).pipe(
      delay(300), // Mô phỏng initialization time
      tap(config => {
        this.configSubject.next(config);
        this.isInitializedSubject.next(true);
        this.isLoading.set(false);
        this.updatePanelCount(config);

        console.log('Golden Layout initialized with config:', config);
      })
    );
  }

  /**
   * Tạo cấu hình Golden Layout mặc định
   */
  private createDefaultConfig(sections: Section[]): GoldenLayoutConfig {
    const config: GoldenLayoutConfig = {
      settings: {
        hasHeaders: true,
        constrainDragToContainer: true,
        reorderEnabled: true,
        selectionEnabled: false,
        popoutWholeStack: false,
        blockedPopoutsThrowError: true,
        closePopoutsOnUnload: true,
        showPopoutIcon: false,
        showMaximiseIcon: true,
        showCloseIcon: true
      },
      dimensions: {
        borderWidth: 5,
        minItemHeight: 10,
        minItemWidth: 10,
        headerHeight: 20,
        dragProxyWidth: 300,
        dragProxyHeight: 200
      },
      labels: {
        close: 'Đóng',
        maximise: 'Phóng to',
        minimise: 'Thu nhỏ',
        popout: 'Mở cửa sổ mới'
      },
      content: this.createContentFromSections(sections)
    };

    return config;
  }

  /**
   * Tạo content cho Golden Layout từ sections
   */
  private createContentFromSections(sections: Section[]): LayoutContentItem[] {
    if (sections.length === 0) {
      return [{
        type: 'component',
        componentName: 'empty-layout',
        title: 'Layout trống',
        componentState: { message: 'Chưa có section nào được tạo' }
      }];
    }

    if (sections.length === 1) {
      // Nếu chỉ có 1 section, tạo component đơn
      return [{
        type: 'component',
        componentName: 'section-panel',
        title: sections[0].title,
        componentState: {
          section: sections[0],
          fieldCount: sections[0].fields.length
        }
      }];
    }

    // Nếu có nhiều section, tạo tab stack
    return [{
      type: 'stack',
      content: sections.map(section => ({
        type: 'component',
        componentName: 'section-panel',
        title: section.title,
        componentState: {
          section: section,
          fieldCount: section.fields.length
        }
      }))
    }];
  }

  /**
   * Cập nhật cấu hình Golden Layout
   */
  updateConfig(newConfig: GoldenLayoutConfig): Observable<boolean> {
    this.isLoading.set(true);

    return of(true).pipe(
      delay(200),
      tap(() => {
        this.configSubject.next(newConfig);
        this.updatePanelCount(newConfig);
        this.saveConfigToStorage(newConfig);
        this.isLoading.set(false);

        console.log('Golden Layout config updated:', newConfig);
      })
    );
  }

  /**
   * Cập nhật layout khi sections thay đổi
   */
  updateLayoutFromSections(sections: Section[]): Observable<GoldenLayoutConfig> {
    const currentConfig = this.currentConfig();
    if (!currentConfig) {
      return this.initializeLayout(sections);
    }

    const updatedConfig: GoldenLayoutConfig = {
      ...currentConfig,
      content: this.createContentFromSections(sections)
    };

    return this.updateConfig(updatedConfig).pipe(
      tap(() => {}), // Convert Observable<boolean> to Observable<GoldenLayoutConfig>
      map(() => updatedConfig)
    );
  }

  /**
   * Thêm panel mới vào layout
   */
  addPanel(title: string, componentName: string, componentState?: Record<string, unknown>): Observable<boolean> {
    const currentConfig = this.currentConfig();
    if (!currentConfig) {
      this.errorSubject.next('Layout chưa được khởi tạo');
      return of(false);
    }

    const newPanel: LayoutContentItem = {
      type: 'component',
      componentName,
      title,
      componentState: componentState || {}
    };

    // Nếu content hiện tại là array rỗng hoặc chỉ có empty component
    if (!currentConfig.content || currentConfig.content.length === 0 ||
        (currentConfig.content.length === 1 && currentConfig.content[0].componentName === 'empty-layout')) {
      currentConfig.content = [newPanel];
    } else if (currentConfig.content.length === 1 && currentConfig.content[0].type === 'component') {
      // Nếu chỉ có 1 component, chuyển thành stack
      currentConfig.content = [{
        type: 'stack',
        content: [currentConfig.content[0], newPanel]
      }];
    } else if (currentConfig.content[0]?.type === 'stack') {
      // Nếu đã có stack, thêm vào stack
      currentConfig.content[0].content.push(newPanel);
    }

    return this.updateConfig(currentConfig);
  }

  /**
   * Xóa panel khỏi layout
   */
  removePanel(title: string): Observable<boolean> {
    const currentConfig = this.currentConfig();
    if (!currentConfig) {
      this.errorSubject.next('Layout chưa được khởi tạo');
      return of(false);
    }

    // Tìm và xóa panel
    if (currentConfig.content && currentConfig.content[0]?.type === 'stack') {
      const stackItem = currentConfig.content[0];
      if (stackItem.content) {
        stackItem.content = stackItem.content.filter(
          (item: LayoutContentItem) => item.title !== title
        );

        // Nếu stack chỉ còn 1 item, chuyển về component đơn
        if (stackItem.content.length === 1) {
          currentConfig.content = [stackItem.content[0]];
        } else if (stackItem.content.length === 0) {
          // Nếu không còn item nào, tạo empty layout
          currentConfig.content = [{
            type: 'component',
            componentName: 'empty-layout',
            title: 'Layout trống',
            componentState: { message: 'Chưa có section nào được tạo' }
          }];
        }
      }
    } else if (currentConfig.content && currentConfig.content[0]?.title === title) {
      // Nếu là component đơn và title khớp, tạo empty layout
      currentConfig.content = [{
        type: 'component',
        componentName: 'empty-layout',
        title: 'Layout trống',
        componentState: { message: 'Chưa có section nào được tạo' }
      }];
    }

    return this.updateConfig(currentConfig);
  }

  /**
   * Lưu trạng thái layout hiện tại
   */
  saveLayoutState(state: LayoutState): void {
    this.layoutStateSubject.next(state);
    localStorage.setItem(this.GOLDEN_LAYOUT_STORAGE_KEY + '_state', JSON.stringify(state));
    console.log('Golden Layout state saved:', state);
  }

  /**
   * Khôi phục trạng thái layout
   */
  restoreLayoutState(): Observable<LayoutState | null> {
    return of(null).pipe(
      delay(100),
      tap(() => {
        try {
          const savedState = localStorage.getItem(this.GOLDEN_LAYOUT_STORAGE_KEY + '_state');
          if (savedState) {
            const state = JSON.parse(savedState);
            this.layoutStateSubject.next(state);
            console.log('Golden Layout state restored:', state);
          }
        } catch (error) {
          console.error('Error restoring layout state:', error);
          this.errorSubject.next('Không thể khôi phục trạng thái layout');
        }
      })
    );
  }

  /**
   * Reset layout về trạng thái mặc định
   */
  resetLayout(): Observable<boolean> {
    return of(true).pipe(
      delay(200),
      tap(() => {
        this.configSubject.next(null);
        this.isInitializedSubject.next(false);
        this.layoutStateSubject.next(null);
        this.panelCount.set(0);

        // Xóa dữ liệu đã lưu
        localStorage.removeItem(this.GOLDEN_LAYOUT_STORAGE_KEY);
        localStorage.removeItem(this.GOLDEN_LAYOUT_STORAGE_KEY + '_state');

        console.log('Golden Layout reset to default');
      })
    );
  }

  /**
   * Lưu cấu hình vào localStorage
   */
  private saveConfigToStorage(config: GoldenLayoutConfig): void {
    try {
      localStorage.setItem(this.GOLDEN_LAYOUT_STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      console.error('Error saving Golden Layout config:', error);
    }
  }

  /**
   * Tải cấu hình từ localStorage
   */
  private loadConfigFromStorage(): void {
    try {
      const savedConfig = localStorage.getItem(this.GOLDEN_LAYOUT_STORAGE_KEY);
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        this.configSubject.next(config);
        this.isInitializedSubject.next(true);
        this.updatePanelCount(config);
        console.log('Golden Layout config loaded from storage:', config);
      }
    } catch (error) {
      console.error('Error loading Golden Layout config:', error);
    }
  }

  /**
   * Cập nhật số lượng panels
   */
  private updatePanelCount(config: GoldenLayoutConfig): void {
    let count = 0;

    if (config.content) {
      config.content.forEach(item => {
        if (item.type === 'component') {
          count++;
        } else if (item.type === 'stack' && item.content) {
          count += item.content.length;
        }
      });
    }

    this.panelCount.set(count);
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.errorSubject.next(null);
  }

  /**
   * Get current error
   */
  getCurrentError(): string | null {
    return this.errorSubject.value;
  }
}
