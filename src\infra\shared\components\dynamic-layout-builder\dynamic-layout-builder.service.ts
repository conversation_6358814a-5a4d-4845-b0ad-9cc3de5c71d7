import { Injectable, signal, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { delay, tap } from 'rxjs/operators';

import { Section, Template, LayoutField, SubmitLayoutResponse } from '@shared/models/view/dynamic-layout-builder.model';

/**
 * DynamicLayoutBuilderService - Service quản lý logic nghiệp vụ cho Dynamic Layout Builder
 *
 * Chức năng chính:
 * - Quản lý trạng thái sections và fields
 * - Lưu/khôi phục layout từ localStorage
 * - Áp dụng templates ngành hàng
 * - Xử lý thêm/xóa/cập nhật sections và fields
 * - Gọi API (mô phỏng) để lưu layout
 */
@Injectable({
  providedIn: 'root'
})
export class DynamicLayoutBuilderService implements OnDestroy {
  private readonly STORAGE_KEY = 'dynamic-layout-builder-config';

  // BehaviorSubjects để quản lý trạng thái
  private sectionsSubject = new BehaviorSubject<Section[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  // Public observables
  public layoutChanged$ = this.sectionsSubject.asObservable();
  public isLoading$ = this.loadingSubject.asObservable();
  public error$ = this.errorSubject.asObservable();

  // Signals cho reactive state management
  public currentSections = signal<Section[]>([]);
  public isLoading = signal<boolean>(false);
  public lastSavedAt = signal<Date | null>(null);

  // Subscription management
  private subscriptions = new Subscription();

  constructor() {
    // Subscribe to BehaviorSubject để cập nhật signals
    this.subscriptions.add(
      this.layoutChanged$.subscribe(sections => {
        this.currentSections.set(sections);
      })
    );

    this.subscriptions.add(
      this.isLoading$.subscribe(loading => {
        this.isLoading.set(loading);
      })
    );
  }

  /**
   * Cleanup subscriptions khi service bị destroy
   */
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();

    // Complete BehaviorSubjects để tránh memory leaks
    this.sectionsSubject.complete();
    this.loadingSubject.complete();
    this.errorSubject.complete();
  }

  /**
   * Cập nhật danh sách sections
   */
  updateSections(sections: Section[]): void {
    this.sectionsSubject.next(sections);
  }

  /**
   * Thêm field vào section
   */
  addFieldToSection(sectionId: string, fieldType: LayoutField): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

    if (sectionIndex === -1) {
      console.error(`Section with id ${sectionId} not found`);
      return;
    }

    // Tạo field mới với ID duy nhất
    const newField: LayoutField = {
      ...fieldType,
      id: this.generateFieldId(),
      label: fieldType.label || `New ${fieldType.type}`,
      isRequired: false,
      isPublic: true,
      order: currentSections[sectionIndex].fields.length + 1
    };

    // Cập nhật section với field mới
    const updatedSections = [...currentSections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: [...updatedSections[sectionIndex].fields, newField]
    };

    this.sectionsSubject.next(updatedSections);
  }

  /**
   * Xóa field khỏi section
   */
  removeFieldFromSection(sectionId: string, fieldId: number): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

    if (sectionIndex === -1) {
      console.error(`Section with id ${sectionId} not found`);
      return;
    }

    // Xóa field khỏi section
    const updatedSections = [...currentSections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: updatedSections[sectionIndex].fields.filter(f => f.id !== fieldId)
    };

    this.sectionsSubject.next(updatedSections);
  }

  /**
   * Cập nhật field trong section
   */
  updateFieldInSection(sectionId: string, fieldId: number, updatedField: Partial<LayoutField>): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

    if (sectionIndex === -1) {
      console.error(`Section with id ${sectionId} not found`);
      return;
    }

    const fieldIndex = currentSections[sectionIndex].fields.findIndex(f => f.id === fieldId);
    if (fieldIndex === -1) {
      console.error(`Field with id ${fieldId} not found in section ${sectionId}`);
      return;
    }

    // Cập nhật field
    const updatedSections = [...currentSections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: [
        ...updatedSections[sectionIndex].fields.slice(0, fieldIndex),
        { ...updatedSections[sectionIndex].fields[fieldIndex], ...updatedField },
        ...updatedSections[sectionIndex].fields.slice(fieldIndex + 1)
      ]
    };

    this.sectionsSubject.next(updatedSections);
  }

  /**
   * Áp dụng template ngành hàng
   */
  applyTemplate(template: Template): void {
    this.loadingSubject.next(true);

    // Mô phỏng delay khi áp dụng template
    of(template.sections).pipe(
      delay(500),
      tap(() => this.loadingSubject.next(false))
    ).subscribe(sections => {
      // Tạo sections mới với IDs duy nhất
      const newSections: Section[] = sections.map(section => ({
        ...section,
        id: this.generateSectionId(),
        fields: section.fields.map(field => ({
          ...field,
          id: this.generateFieldId()
        }))
      }));

      this.sectionsSubject.next(newSections);
    });
  }

  /**
   * Lưu layout vào localStorage
   */
  saveLayout(sections: Section[]): Observable<boolean> {
    this.loadingSubject.next(true);

    return of(true).pipe(
      delay(1000), // Mô phỏng API call
      tap(() => {
        try {
          const layoutData = {
            sections,
            savedAt: new Date().toISOString(),
            version: '1.0'
          };

          localStorage.setItem(this.STORAGE_KEY, JSON.stringify(layoutData));
          this.lastSavedAt.set(new Date());
          this.loadingSubject.next(false);

          console.log('Layout saved successfully', layoutData);
        } catch (error) {
          console.error('Error saving layout:', error);
          this.errorSubject.next('Failed to save layout');
          this.loadingSubject.next(false);
        }
      })
    );
  }

  /**
   * Khôi phục layout từ localStorage
   */
  restoreLayout(): Observable<Section[]> {
    this.loadingSubject.next(true);

    return of([]).pipe(
      delay(500), // Mô phỏng API call
      tap(() => {
        try {
          const savedData = localStorage.getItem(this.STORAGE_KEY);
          if (savedData) {
            const layoutData = JSON.parse(savedData);
            this.sectionsSubject.next(layoutData.sections);
            this.lastSavedAt.set(new Date(layoutData.savedAt));

            console.log('Layout restored successfully', layoutData);
          } else {
            console.log('No saved layout found');
            this.errorSubject.next('No saved layout found');
          }

          this.loadingSubject.next(false);
        } catch (error) {
          console.error('Error restoring layout:', error);
          this.errorSubject.next('Failed to restore layout');
          this.loadingSubject.next(false);
        }
      })
    );
  }



  /**
   * Gửi layout qua API (mô phỏng)
   */
  submitLayout(sections: Section[]): Observable<SubmitLayoutResponse> {
    this.loadingSubject.next(true);

    const payload = {
      layout: {
        sections,
        metadata: {
          createdAt: new Date().toISOString(),
          version: '1.0',
          totalFields: sections.reduce((total, section) => total + section.fields.length, 0)
        }
      }
    };

    return of({ success: true, id: this.generateId() }).pipe(
      delay(2000), // Mô phỏng API call
      tap(response => {
        console.log('Layout submitted successfully:', payload);
        console.log('API Response:', response);
        this.loadingSubject.next(false);
      })
    );
  }

  /**
   * Lấy templates ngành hàng mặc định
   */
  getDefaultTemplates(): Template[] {
    return [
      {
        name: 'Thời trang',
        sections: [
          {
            id: this.generateSectionId(),
            title: 'Thông tin cơ bản',
            fields: [
              { id: this.generateFieldId(), type: 'text', label: 'Tên khách hàng', value: '', isRequired: true, order: 1 },
              { id: this.generateFieldId(), type: 'email', label: 'Email', value: '', isRequired: true, order: 2 },
              { id: this.generateFieldId(), type: 'phone', label: 'Số điện thoại', value: '', isRequired: false, order: 3 }
            ]
          },
          {
            id: this.generateSectionId(),
            title: 'Thông tin thời trang',
            fields: [
              { id: this.generateFieldId(), type: 'picklist', label: 'Kích cỡ', value: '', constraints: { picklistValues: ['S', 'M', 'L', 'XL'] }, order: 1 },
              { id: this.generateFieldId(), type: 'picklist', label: 'Màu sắc', value: '', constraints: { picklistValues: ['Đỏ', 'Xanh', 'Vàng', 'Đen', 'Trắng'] }, order: 2 }
            ]
          }
        ]
      },
      {
        name: 'Mỹ phẩm',
        sections: [
          {
            id: this.generateSectionId(),
            title: 'Thông tin cơ bản',
            fields: [
              { id: this.generateFieldId(), type: 'text', label: 'Tên khách hàng', value: '', isRequired: true, order: 1 },
              { id: this.generateFieldId(), type: 'email', label: 'Email', value: '', isRequired: true, order: 2 }
            ]
          },
          {
            id: this.generateSectionId(),
            title: 'Thông tin làn da',
            fields: [
              { id: this.generateFieldId(), type: 'picklist', label: 'Loại da', value: '', constraints: { picklistValues: ['Da khô', 'Da dầu', 'Da hỗn hợp', 'Da nhạy cảm'] }, order: 1 },
              { id: this.generateFieldId(), type: 'multi-picklist', label: 'Sản phẩm yêu thích', value: [], constraints: { picklistValues: ['Kem dưỡng', 'Serum', 'Toner', 'Cleanser'] }, order: 2 }
            ]
          }
        ]
      }
    ];
  }

  /**
   * Lấy field types mặc định
   */
  getDefaultFieldTypes(): LayoutField[] {
    return [
      { id: 1, type: 'text', label: 'Text', value: '', order: 1, description: 'Trường văn bản đơn giản' },
      { id: 2, type: 'number', label: 'Number', value: '', order: 2, description: 'Trường số nguyên' },
      { id: 3, type: 'email', label: 'Email', value: '', order: 3, description: 'Trường email với validation' },
      { id: 4, type: 'phone', label: 'Phone', value: '', order: 4, description: 'Trường số điện thoại' },
      { id: 5, type: 'textarea', label: 'Textarea', value: '', order: 5, description: 'Trường văn bản nhiều dòng' },
      { id: 6, type: 'date', label: 'Date', value: '', order: 6, description: 'Trường chọn ngày' },
      { id: 7, type: 'datetime', label: 'DateTime', value: '', order: 7, description: 'Trường chọn ngày giờ' },
      { id: 8, type: 'file', label: 'File', value: '', order: 8, description: 'Trường upload file' },
      { id: 9, type: 'image', label: 'Image', value: '', order: 9, description: 'Trường upload hình ảnh' },
      { id: 10, type: 'checkbox', label: 'Checkbox', value: false, order: 10, description: 'Hộp kiểm đúng/sai' },
      { id: 11, type: 'radio', label: 'Radio', value: '', order: 11, description: 'Nút radio chọn một' },
      { id: 12, type: 'select', label: 'Select', value: '', order: 12, description: 'Dropdown chọn một' },
      { id: 13, type: 'size', label: 'Size', value: '', order: 13, description: 'Trường chọn kích cỡ (S, M, L, XL)' },
      { id: 14, type: 'color', label: 'Color', value: '', order: 14, description: 'Trường chọn màu sắc' },
      { id: 15, type: 'brand', label: 'Brand', value: '', order: 15, description: 'Trường chọn thương hiệu' },
      { id: 16, type: 'category', label: 'Category', value: '', order: 16, description: 'Trường chọn danh mục' }
    ];
  }

  /**
   * Tạo ID duy nhất cho section
   */
  private generateSectionId(): string {
    return 'section_' + Date.now().toString() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Tạo ID duy nhất cho field
   */
  private generateFieldId(): number {
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  /**
   * Tạo ID duy nhất chung
   */
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.errorSubject.next(null);
  }

  /**
   * Tạo section mặc định
   */
  createDefaultSection(): Section {
    return {
      id: this.generateSectionId(),
      title: 'Thông tin cơ bản',
      fields: []
    };
  }

  /**
   * Lấy template cho field type
   */
  getFieldTypeTemplate(fieldType: string): LayoutField | null {
    const fieldTypeMap: { [key: string]: Partial<LayoutField> } = {
      // Trường Cơ Bản
      'text': {
        type: 'text',
        label: 'Text Field',
        placeholder: 'Enter text...',
        isRequired: false,
        constraints: { maxLength: 255 }
      },
      'number': {
        type: 'number',
        label: 'Number Field',
        placeholder: 'Enter number...',
        isRequired: false,
        constraints: { maxDigits: 9 }
      },
      'email': {
        type: 'email',
        label: 'Email Field',
        placeholder: 'Enter email...',
        isRequired: false,
        constraints: { unique: false }
      },
      'phone': {
        type: 'phone',
        label: 'Phone Field',
        placeholder: 'Enter phone number...',
        isRequired: false,
        constraints: { maxLength: 20, maxDigits: 15 }
      },
      'textarea': {
        type: 'textarea',
        label: 'Textarea Field',
        placeholder: 'Enter text...',
        isRequired: false,
        constraints: { textType: 'small', maxLength: 2000 }
      },

      // Trường Nâng Cao
      'date': {
        type: 'date',
        label: 'Date Field',
        placeholder: 'Select date...',
        isRequired: false,
        constraints: {}
      },
      'datetime': {
        type: 'datetime',
        label: 'DateTime Field',
        placeholder: 'Select date and time...',
        isRequired: false,
        constraints: {}
      },
      'file': {
        type: 'file',
        label: 'File Field',
        placeholder: 'Upload file...',
        isRequired: false,
        constraints: { maxSize: 10485760, allowedTypes: ['pdf', 'doc', 'docx', 'txt'] }
      },
      'image': {
        type: 'image',
        label: 'Image Field',
        placeholder: 'Upload image...',
        isRequired: false,
        constraints: { maxSize: 5242880, allowedTypes: ['jpg', 'jpeg', 'png', 'gif'] }
      },
      'checkbox': {
        type: 'checkbox',
        label: 'Checkbox Field',
        placeholder: '',
        isRequired: false,
        constraints: { defaultValue: false }
      },
      'radio': {
        type: 'radio',
        label: 'Radio Field',
        placeholder: 'Select option...',
        isRequired: false,
        constraints: { options: ['Option 1', 'Option 2', 'Option 3'] }
      },
      'select': {
        type: 'select',
        label: 'Select Field',
        placeholder: 'Choose option...',
        isRequired: false,
        constraints: { options: ['Option 1', 'Option 2', 'Option 3'], multiple: false }
      },

      // Trường Ngành Hàng
      'size': {
        type: 'size',
        label: 'Size Field',
        placeholder: 'Select size...',
        isRequired: false,
        constraints: { options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] }
      },
      'color': {
        type: 'color',
        label: 'Color Field',
        placeholder: 'Select color...',
        isRequired: false,
        constraints: { format: 'hex' }
      },
      'brand': {
        type: 'brand',
        label: 'Brand Field',
        placeholder: 'Select brand...',
        isRequired: false,
        constraints: { searchable: true }
      },
      'category': {
        type: 'category',
        label: 'Category Field',
        placeholder: 'Select category...',
        isRequired: false,
        constraints: { hierarchical: true }
      }
    };

    const template = fieldTypeMap[fieldType];
    if (template) {
      return {
        id: 0, // Will be set by caller
        order: 0, // Will be set by caller
        ...template
      } as LayoutField;
    }

    return null;
  }

  /**
   * Tạo field mới từ field type với ID và order
   */
  createFieldFromType(fieldType: string, order: number): LayoutField | null {
    const template = this.getFieldTypeTemplate(fieldType);
    if (template) {
      return {
        ...template,
        id: this.generateFieldId(),
        order
      };
    }
    return null;
  }

  /**
   * Áp dụng template và trả về sections mới
   */
  applyTemplateAndGetSections(template: Template): Section[] {
    // Tạo sections mới với IDs duy nhất
    const newSections: Section[] = template.sections.map(section => ({
      ...section,
      id: this.generateSectionId(),
      fields: section.fields.map(field => ({
        ...field,
        id: this.generateFieldId()
      }))
    }));

    return newSections;
  }

  /**
   * Xử lý quick add field logic
   */
  handleQuickAddField(sectionId: string, fieldType: string, currentSections: Section[]): Section[] {
    const sectionIndex = currentSections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) {
      console.error(`Section with id ${sectionId} not found`);
      return currentSections;
    }

    const fieldTemplate = this.getFieldTypeTemplate(fieldType);
    if (!fieldTemplate) {
      console.error(`Field type template for ${fieldType} not found`);
      return currentSections;
    }

    const newField: LayoutField = {
      ...fieldTemplate,
      id: this.generateFieldId(),
      order: currentSections[sectionIndex].fields.length + 1
    };

    const updatedSections = [...currentSections];
    updatedSections[sectionIndex] = {
      ...updatedSections[sectionIndex],
      fields: [...updatedSections[sectionIndex].fields, newField]
    };

    return updatedSections;
  }

  /**
   * Xử lý section title change logic
   */
  handleSectionTitleChange(sectionId: string, newTitle: string, currentSections: Section[]): Section[] {
    const sections = [...currentSections];
    const index = sections.findIndex(s => s.id === sectionId);
    if (index !== -1) {
      sections[index] = { ...sections[index], title: newTitle };
    }
    return sections;
  }

  /**
   * Xử lý field reorder logic
   */
  handleFieldReorder(sectionId: string, fields: LayoutField[], currentSections: Section[]): Section[] {
    const sections = [...currentSections];
    const index = sections.findIndex(s => s.id === sectionId);
    if (index !== -1) {
      sections[index] = { ...sections[index], fields };
    }
    return sections;
  }

  /**
   * Xử lý field properties update logic
   */
  handleFieldPropertiesUpdate(sectionId: string, fieldId: number, updatedField: LayoutField, currentSections: Section[]): Section[] {
    const sections = [...currentSections];
    const sectionIndex = sections.findIndex(s => s.id === sectionId);

    if (sectionIndex !== -1) {
      const fields = [...sections[sectionIndex].fields];
      const fieldIndex = fields.findIndex(f => f.id === fieldId);

      if (fieldIndex !== -1) {
        fields[fieldIndex] = { ...updatedField };
        sections[sectionIndex] = { ...sections[sectionIndex], fields };
      }
    }

    return sections;
  }

  /**
   * Xử lý field deletion logic
   */
  handleFieldDeletion(sectionId: string, fieldId: number, currentSections: Section[]): Section[] {
    const sections = [...currentSections];
    const sectionIndex = sections.findIndex(s => s.id === sectionId);

    if (sectionIndex !== -1) {
      const fields = sections[sectionIndex].fields.filter(f => f.id !== fieldId);
      sections[sectionIndex] = { ...sections[sectionIndex], fields };
    }

    return sections;
  }

  /**
   * Xử lý section deletion logic
   */
  handleSectionDeletion(sectionId: string, currentSections: Section[]): Section[] {
    return currentSections.filter(s => s.id !== sectionId);
  }
}
