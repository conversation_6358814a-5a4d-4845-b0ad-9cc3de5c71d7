import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';

@Component({
  selector: 'app-supplier-form-modal',
  templateUrl: './supplier-form-modal.component.html',
  styleUrls: ['./supplier-form-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule
  ]
})
export class SupplierFormModalComponent implements OnInit {
  supplierForm: FormGroup;
  dialogTitle = 'Thêm nhà cung cấp mới';
  data: { supplier?: Supplier };

  constructor(
    private fb: FormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: { supplier?: Supplier },
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: { supplier?: Supplier },
    @Optional() private dialogRef?: MatDialogRef<SupplierFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SupplierFormModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {};

    this.supplierForm = this.fb.group({
      _id: ['', []],
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      contactPerson: ['', []],
      phone: ['', []],
      email: ['', [Validators.email]],
      address: ['', []],
      taxCode: ['', []],
      paymentTerm: ['', []],
      isActive: [true, []]
    });
  }

  ngOnInit(): void {
    if (this.data && this.data.supplier) {
      this.dialogTitle = 'Chỉnh sửa nhà cung cấp';
      this.supplierForm.patchValue(this.data.supplier);
    }
  }

  onNoClick(): void {
    this.close();
  }

  close(result?: any): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  onSubmit(): void {
    if (this.supplierForm.valid) {
      const formValue = this.supplierForm.value;

      // Tạo ID mới nếu là thêm mới
      if (!formValue._id) {
        formValue._id = `supplier_${Date.now().toString()}`;
      }

      this.close(formValue);
    } else {
      // Đánh dấu tất cả các trường là đã chạm để hiển thị lỗi
      Object.keys(this.supplierForm.controls).forEach(key => {
        const control = this.supplierForm.get(key);
        control?.markAsTouched();
      });
    }
  }
}
