import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// Angular Material imports
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { Field } from '@domain/entities/field.entity';
import {
  BaseFilterInput,
  TextFilterValue,
  FilterInputChangeEvent
} from '../base-filter-input.interface';

/**
 * Specialized component cho text-based filter inputs
 * Handles: text, email, phone, url, textarea field types
 * Operators: is, isnt, contains, doesnt_contain, starts_with, ends_with, is_empty, is_not_empty
 */
@Component({
  selector: 'app-text-filter-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule
  ],
  templateUrl: './text-filter-input.component.html',
  styleUrls: ['./text-filter-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextFilterInputComponent implements BaseFilterInput, OnInit, OnDestroy {
  @Input() field!: Field;
  @Input() operator!: string;
  @Input() value?: string;
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<string>();
  @Output() validationChange = new EventEmitter<boolean>();
  @Output() filterChange = new EventEmitter<TextFilterValue>();

  // Signals cho reactive state management
  readonly inputValue = signal<string>('');
  readonly isValid = signal<boolean>(true);

  // Flag để track user interaction
  private hasUserInteraction = false;

  // Computed properties
  readonly requiresInput = computed(() => {
    return !['is_empty', 'is_not_empty'].includes(this.operator);
  });

  readonly inputType = computed(() => {
    switch (this.field?.type) {
      case 'email':
        return 'email';
      case 'phone':
        return 'tel';
      case 'url':
        return 'url';
      default:
        return 'text';
    }
  });

  readonly isTextarea = computed(() => {
    return this.field?.type === 'textarea';
  });

  readonly placeholder = computed(() => {
    const operatorKey = this.getOperatorPlaceholderKey();
    return `FIELD_FILTERS.PLACEHOLDERS.${operatorKey}`;
  });

  constructor() {
    // Effect để sync input value với external value
    effect(() => {
      if (this.value !== undefined && this.value !== this.inputValue()) {
        this.inputValue.set(this.value);
      }
    });

    // Effect để emit changes - chỉ emit khi đã có user interaction
    effect(() => {
      const currentValue = this.inputValue();
      const valid = this.validate();

      this.isValid.set(valid);

      // Chỉ emit khi đã có user interaction thực sự
      if (this.hasUserInteraction) {
        this.valueChange.emit(currentValue);
        this.validationChange.emit(valid);

        // Emit filter change với format đúng
        const filterValue = this.getCurrentFilterValue();
        this.filterChange.emit(filterValue);
      }
    });
  }

  ngOnInit(): void {
    // Initialize với value nếu có
    if (this.value) {
      this.inputValue.set(this.value);
    }
  }

  ngOnDestroy(): void {
    // Cleanup nếu cần
  }

  /**
   * Handle input value change từ user interaction
   */
  onInputChange(event: Event): void {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement;
    const newValue = target.value;

    // Đánh dấu đã có user interaction
    this.hasUserInteraction = true;

    // Cập nhật giá trị
    this.inputValue.set(newValue);

    // Emit ngay lập tức khi có user input
    const valid = this.validate();
    this.isValid.set(valid);
    this.valueChange.emit(newValue);
    this.validationChange.emit(valid);

    // Emit filter change với format đúng
    const filterValue = this.getCurrentFilterValue();
    this.filterChange.emit(filterValue);
  }

  /**
   * Validate current input value
   */
  validate(): boolean {
    if (!this.requiresInput()) {
      return true; // Operators như is_empty không cần input
    }

    const value = this.inputValue().trim();

    // Basic validation: required input cho operators cần value
    if (this.requiresInput() && value.length === 0) {
      return false;
    }

    // Email validation
    if (this.field?.type === 'email' && value.length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    }

    // URL validation
    if (this.field?.type === 'url' && value.length > 0) {
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }

    return true;
  }

  /**
   * Reset input value và các flags
   */
  reset(): void {
    this.inputValue.set('');
    this.hasUserInteraction = false;
  }

  /**
   * Get placeholder key dựa trên operator
   */
  private getOperatorPlaceholderKey(): string {
    switch (this.operator) {
      case 'is':
        return 'ENTER_EXACT_VALUE';
      case 'isnt':
        return 'ENTER_VALUE_TO_EXCLUDE';
      case 'contains':
        return 'ENTER_TEXT_TO_SEARCH';
      case 'doesnt_contain':
        return 'ENTER_TEXT_TO_EXCLUDE';
      case 'starts_with':
        return 'ENTER_STARTING_TEXT';
      case 'ends_with':
        return 'ENTER_ENDING_TEXT';
      default:
        return 'ENTER_VALUE';
    }
  }

  /**
   * Get current filter value trong format chuẩn
   */
  getCurrentFilterValue(): TextFilterValue {
    return {
      operator: this.operator,
      value: this.inputValue()
    };
  }
}
