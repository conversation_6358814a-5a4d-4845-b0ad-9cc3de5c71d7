import { Component, OnInit, OnDestroy, inject, signal, ChangeDetectionStrategy, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule, NavigationEnd } from '@angular/router';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { Subject, Subscription, filter } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';

// Import cấu hình navigation và các kiểu dữ liệu cần thiết
// Lấy kiểu từ file config thay vì shared_contracts
import { Navigation, NavigationItem } from '@config/navigation.config';

// Import component cho bottom sheet (sẽ tạo ở bước sau)
import { NavigationService } from '../../navigation.service';
import { AppNavigationDrawerComponent } from '../../navigation-drawer/app-navigation-drawer.component';
import { AppNavigationDrawerService } from '../../navigation-drawer/app-navigation-drawer.service';
import { RouterEventService } from '@/core/services/router_events.service';
/**
 * Kiểu dữ liệu mở rộng cho mục "more"
 */
type BottomNavigationDisplayItem = Navigation & { isMore?: boolean };

@Component({
  selector: 'app-bottom-navigation',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatBottomSheetModule
  ],
  templateUrl: './bottom-navigation.component.html',
  styleUrls: ['./bottom-navigation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BottomNavigationComponent  implements OnInit {
  // Signal lưu trữ các mục hiển thị trên thanh bottom
  bottomTopLevelItems: WritableSignal<BottomNavigationDisplayItem[]> = signal([]);
  iconAnimating = signal<string>(''); // Track if icon is currently animating

  constructor(
    private navigationService: NavigationService,
    private navigationDrawerService: AppNavigationDrawerService
  ) {
  }

  ngOnInit(): void {
    this.setupNavigationItems();
  }




  /**
   * Chuẩn bị danh sách các mục hiển thị trên thanh bottom.
   * Lấy 4 mục đầu từ NAVIGATIONS và thêm mục "more".
   */
  private setupNavigationItems(): void {
    // Lấy 4 mục đầu tiên từ cấu hình
    const mainItems = this.navigationService.getNavigation().slice(0, 4);

    // Tạo mục "more"
    const moreItem: BottomNavigationDisplayItem = {
      isMore: true,
      // Không cần routerLink cho mục "more"
      routerLink: '', // Hoặc giá trị phù hợp khác nếu cần
      icon: { set: 'keenicons-filled', code: 'dots-vertical' },
      moduleFullName: 'ROUTER_TEXT.MORE', // Key cho i18n
      moduleShortName: 'ROUTER_TEXT.MORE', // Key cho i18n
      moduleTooltip: 'ROUTER_TEXT.MORE', // Key cho i18n
      module: 'more', // Block riêng cho "more"
      items: [] // Mục "more" không có items con trực tiếp hiển thị ở đây
    };

    // Kết hợp thành danh sách cuối cùng
    this.bottomTopLevelItems.set([...mainItems, moreItem]);
  }



  /**
   * Kiểm tra xem một mục navigation có đang active hay không.
   * @param item Mục navigation cần kiểm tra.
   * @returns True nếu mục đang active, ngược lại là false.
   */
  isActive(item: BottomNavigationDisplayItem): boolean {
    // Mục "more" không bao giờ active
    if (item.isMore) {
      return false;
    }

    return this.navigationService.isUrlInNavigationTree(this.navigationService.currentUrl(), item);
  }

  /**
   * Mở bottom sheet để hiển thị toàn bộ cây navigation.
   */
  openMoreSheet(): void {
    this.navigationDrawerService.openDrawer(true);
  }

  /**
   * Mở bottom sheet hiển thị các sub-navigation
   * @param items Danh sách các item con
   * @param event Sự kiện click
   */
  openSubSheet(nav: BottomNavigationDisplayItem, event: MouseEvent): void {
    event.preventDefault();
    this.setIconAnimating(nav)
    this.navigationService.openSubNavigationSheet(nav.items);
  }


  trackByFn(index: number, item: BottomNavigationDisplayItem): string {
    return this.navigationService.trackByFn(index, item);
  }

  setIconAnimating(item: BottomNavigationDisplayItem): void {
    this.iconAnimating.set(item.routerLink);

    setTimeout(() => {
      this.iconAnimating.set('');
    }, 400);
  }
}
