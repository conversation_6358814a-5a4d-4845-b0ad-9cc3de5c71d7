import { Injectable, Type, InjectionToken, Injector, Component } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatBottomSheet, MatBottomSheetConfig } from '@angular/material/bottom-sheet';
import { firstValueFrom } from 'rxjs';
import { ViewportService } from './viewport.service';

@Injectable({
  providedIn: 'root'
})
export class ResponsiveModalService {
  constructor(
    private dialog: MatDialog,
    private bottomSheet: MatBottomSheet,
    private viewportService: ViewportService
  ) {}

  async open<T, D = any, R = any>(
    component: Type<T>,
    modalConfig: MatDialogConfig<D> | MatBottomSheetConfig<D> = {},
    forceMode?: 'dialog' | 'bottom-sheet'
  ): Promise<R | undefined> {
    const useBottomSheet =
      forceMode === 'bottom-sheet' || (forceMode !== 'dialog' && this.viewportService.isMobile());

    if (useBottomSheet) {
      // Mở bottom sheet
      const bottomSheetRef = this.bottomSheet.open(component, modalConfig as MatBottomSheetConfig<D>);
      return firstValueFrom(bottomSheetRef.afterDismissed());
    } else {
      // Mở dialog với injector tùy chỉnh
      const dialogRef = this.dialog.open(component, modalConfig as MatDialogConfig<D>);
      return firstValueFrom(dialogRef.afterClosed());
    }
  }
}
