// NewSectionComponent styles
.new-section-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  
  // Nút tạo section mới
  .new-section-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-width: 160px;
    justify-content: center;
    
    // Icon trong nút
    .btn-icon {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
    }
    
    // Text trong nút
    .btn-text {
      font-size: 0.875rem;
      font-weight: 500;
    }
    
    // Loading icon
    .loading-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      animation: spin 1s linear infinite;
      margin-left: 0.25rem;
    }
    
    // Hover effect
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    // Disabled state
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  // Mô tả
  .new-section-description {
    font-size: 0.75rem;
    color: var(--bs-text-muted);
    text-align: center;
    margin: 0;
    max-width: 200px;
    line-height: 1.4;
  }
}

// Animation cho loading icon
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .new-section-container {
    padding: 0.75rem;
    
    .new-section-btn {
      min-width: 140px;
      padding: 0.625rem 1.25rem;
      
      .btn-text {
        font-size: 0.8125rem;
      }
    }
    
    .new-section-description {
      font-size: 0.6875rem;
      max-width: 180px;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .new-section-container {
    .new-section-description {
      color: var(--bs-text-muted);
    }
  }
}
