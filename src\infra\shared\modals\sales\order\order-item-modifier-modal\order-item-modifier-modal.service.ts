import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { OrderItemModifierModalComponent } from './order-item-modifier-modal.component';
import { ModifierGroup, OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { ProductModifierGroup } from 'salehub_shared_contracts/requests/shared/product';

export interface ProductModifierFormModalData {
  list: ProductModifierGroup[];
  data: OrderItemBaseDetails;
}

/**
 * Service để mở modal chọn modifier cho sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class OrderItemModifierModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal chọn modifier cho sản phẩm
   * @param data Dữ liệu cho modal
   * @returns Promise<ModifierGroup[] | null> Danh sách modifier đã chọn hoặc null nếu hủy
   */
  async open(data: ProductModifierFormModalData): Promise<ModifierGroup[] | null> {
    try {
      const modalConfig = {
        data,
        panelClass: 'product-modifiers-modal'
      };

      const result = await this.responsiveModalService.open<
        OrderItemModifierModalComponent,
        ProductModifierFormModalData,
        ModifierGroup[]
      >(OrderItemModifierModalComponent, modalConfig);

      return result || null;
    } catch (error) {
      console.error('Lỗi khi mở modal chọn modifier:', error);
      return null;
    }
  }
}
