{"FIELD_FILTERS": {"TITLE": "Field Filters", "NO_FIELDS": "No fields available for filtering", "CLEAR_ALL": "Clear All", "APPLY_FILTERS": "Apply Filters", "APPLY": "Apply", "CANCEL": "Cancel", "RESET_FILTERS": "Reset Filters", "OPERATORS": {"IS": "is", "ISNT": "isn't", "CONTAINS": "contains", "DOESNT_CONTAIN": "doesn't contain", "STARTS_WITH": "starts with", "ENDS_WITH": "ends with", "IS_EMPTY": "is empty", "IS_NOT_EMPTY": "is not empty", "EQUALS": "equals", "NOT_EQUALS": "not equals", "LESS_THAN": "less than", "LESS_THAN_OR_EQUAL": "less than or equal", "GREATER_THAN": "greater than", "GREATER_THAN_OR_EQUAL": "greater than or equal", "BETWEEN": "between", "NOT_BETWEEN": "not between", "AGE_IN": "age in", "DUE_IN": "due in", "PREVIOUS": "previous", "NEXT": "next", "ON": "on", "BEFORE": "before", "AFTER": "after", "TODAY": "today", "TOMORROW": "tomorrow", "TILL_YESTERDAY": "till yesterday", "STARTING_TOMORROW": "starting tomorrow", "YESTERDAY": "yesterday", "THIS_WEEK": "this week", "THIS_MONTH": "this month", "PREVIOUS_WEEK": "previous week", "PREVIOUS_MONTH": "previous month", "THIS_YEAR": "this year", "CURRENT_FY": "current fiscal year", "CURRENT_FQ": "current fiscal quarter", "PREVIOUS_YEAR": "previous year", "PREVIOUS_FY": "previous fiscal year", "PREVIOUS_FQ": "previous fiscal quarter", "NEXT_YEAR": "next year", "NEXT_FQ": "next fiscal quarter", "IS_NOT": "is not"}, "TIME_UNITS": {"DAYS": "days", "WEEKS": "weeks", "MONTHS": "months", "YEARS": "years"}, "CHECKBOX_VALUES": {"SELECTED": "selected", "NOT_SELECTED": "not selected"}, "PLACEHOLDERS": {"ENTER_VALUE": "Enter value", "ENTER_MIN_VALUE": "Enter minimum value", "ENTER_MAX_VALUE": "Enter maximum value", "SELECT_DATE": "Select date", "SELECT_START_DATE": "Select start date", "SELECT_END_DATE": "Select end date", "START_DATE": "Start date", "END_DATE": "End date", "MIN_VALUE": "Minimum value", "MAX_VALUE": "Maximum value", "ENTER_NUMBER": "Enter number", "SELECT_VALUES": "Select values", "SELECT_OPERATOR": "Select operator", "SELECT_TIME_UNIT": "Select time unit", "SELECT_CHECKBOX_VALUE": "Select value", "ENTER_EXACT_VALUE": "Enter exact value", "ENTER_VALUE_TO_EXCLUDE": "Enter value to exclude", "ENTER_TEXT_TO_SEARCH": "Enter text to search", "ENTER_TEXT_TO_EXCLUDE": "Enter text to exclude", "ENTER_STARTING_TEXT": "Enter starting text", "ENTER_ENDING_TEXT": "Enter ending text"}, "LABELS": {"AND": "and", "FROM": "from", "TO": "to", "VALUE": "Value", "MIN_VALUE": "Min Value", "MAX_VALUE": "Max Value", "TIME_VALUE": "Time Value", "TIME_UNIT": "Time Unit", "OPERATOR": "Operator", "DATE": "Date", "START_DATE": "Start Date", "END_DATE": "End Date", "CHECKBOX_STATE": "Checkbox State"}, "VALIDATION": {"REQUIRED": "This field is required", "INVALID_NUMBER": "Please enter a valid number", "INVALID_DATE": "Please enter a valid date", "MIN_GREATER_THAN_MAX": "Minimum value cannot be greater than maximum value", "REQUIRED_VALUE": "Please enter a value", "REQUIRED_NUMBER": "Please enter a number", "INVALID_EMAIL": "Invalid email address", "INVALID_URL": "Invalid URL", "BOTH_VALUES_REQUIRED": "Both values are required", "MIN_LESS_THAN_MAX": "Minimum value must be less than maximum value", "DATE_REQUIRED": "Please select a date", "BOTH_DATES_REQUIRED": "Both dates are required", "START_DATE_BEFORE_END_DATE": "Start date must be before end date", "POSITIVE_NUMBER_REQUIRED": "Please enter a positive number", "SELECT_AT_LEAST_ONE": "Please select at least one item"}, "MESSAGES": {"NO_FILTERS_ACTIVE": "No filters are active", "FILTERS_ACTIVE": "{{count}} filter(s) active", "VALIDATION_FAILED": "Please check your filters", "NO_INPUT_REQUIRED": "No input required", "NO_OPTIONS_FOUND": "No options found", "SELECTED_COUNT": "{{count}} item(s) selected"}, "EXPLANATIONS": {"CHECKBOX_SELECTED": "Show records with checkbox selected", "CHECKBOX_NOT_SELECTED": "Show records with checkbox not selected"}}}