{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "version": 1,
  "newProjectRoot": "projects",
  "projects": {
    "salehub": {
      "projectType": "application",
      "schematics": {
        "@schematics/angular:component": {
          "style": "scss"
        }
      },
      "root": "",
      "sourceRoot": "src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:application",
          "options": {
            "allowedCommonJsDependencies": [
              "salehub_shared_contracts",
              "html2canvas"
            ],
            "outputPath": {
              "base":"dist",
              "browser": ""
            },
            "index": "src/index.html",
            "browser": "src/main.ts",
            "polyfills": [
              "zone.js"
            ],
            "tsConfig": "tsconfig.app.json",
            "inlineStyleLanguage": "scss",
            "assets": [
              {
                "glob": "**/*",
                "input": "public"
              }
            ],
            "styles": [
              // "node_modules/@cds/core/global.min.css",
              // "node_modules/@cds/core/styles/theme.dark.min.css",
              // "node_modules/@clr/ui/clr-ui.min.css",

              // "node_modules/bootstrap/dist/css/bootstrap-reboot.min.css",
              // "@angular/material/prebuilt-themes/deeppurple-amber.css",
              // "node_modules/ngx-toastr/toastr.css",

              "src/infra/shared/styles/styles.scss"
            ],
            "scripts": [],
            "serviceWorker": "ngsw-config.json"
          },
          "configurations": {
            "production": {
              "optimization": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "500kB",
                  "maximumError": "1MB"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "4kB",
                  "maximumError": "8kB"
                }
              ],
              "outputHashing": "all"
            },
            "development": {
              "optimization": false,
              "extractLicenses": false,
              "sourceMap": true
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "configurations": {
            "production": {
              "buildTarget": "salehub:build:production"
            },
            "development": {
              "buildTarget": "salehub:build:development"
            }
          },
          "options": {
            "host": "0.0.0.0",
            "port": 4200,
            "proxyConfig": "proxy.conf.json"
          },
          "defaultConfiguration": "development"
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n"
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "polyfills": [
              "zone.js",
              "zone.js/testing"
            ],
            "tsConfig": "tsconfig.spec.json",
            "inlineStyleLanguage": "scss",
            "assets": [
              {
                "glob": "**/*",
                "input": "public"
              }
            ],
            "styles": [
              "@angular/material/prebuilt-themes/deeppurple-amber.css",
              "src/styles/styles.scss"
            ],
            "scripts": []
          }
        }
      }
    }
  }
}
