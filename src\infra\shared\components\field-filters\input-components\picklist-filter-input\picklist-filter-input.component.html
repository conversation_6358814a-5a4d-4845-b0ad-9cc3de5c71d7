<!-- Picklist Filter Input Component -->
<div class="picklist-filter-input" *ngIf="requiresInput()">
  <mat-form-field appearance="outline" class="w-100">
    <mat-label>{{ placeholder() | translate }}</mat-label>
    
    <!-- Chip Grid cho selected values -->
    <mat-chip-grid #chipGrid>
      <mat-chip-row
        *ngFor="let chip of selectedValues()"
        (removed)="onChipRemoved(chip)"
        [removable]="!disabled">
        {{ chip }}
        <button matChipRemove *ngIf="!disabled">
          <mat-icon>cancel</mat-icon>
        </button>
      </mat-chip-row>
    </mat-chip-grid>

    <!-- Input với autocomplete -->
    <input
      matInput
      [value]="inputValue()"
      [disabled]="disabled"
      (input)="onInputChange($event)"
      [matAutocomplete]="auto"
      [matChipInputFor]="chipGrid"
      (matChipInputTokenEnd)="onChipInputEvent($event)"
      [placeholder]="selectedValues().length === 0 ? (placeholder() | translate) : ''"
      class="picklist-input">

    <!-- Autocomplete dropdown -->
    <mat-autocomplete 
      #auto="matAutocomplete" 
      (optionSelected)="onChipSelected($event)"
      class="picklist-autocomplete">
      <mat-option
        *ngFor="let option of filteredOptions()"
        [value]="option"
        class="picklist-option">
        {{ option }}
      </mat-option>
      
      <!-- No options message -->
      <mat-option 
        *ngIf="filteredOptions().length === 0 && inputValue().length > 0"
        disabled
        class="no-options-message">
        {{ 'FIELD_FILTERS.MESSAGES.NO_OPTIONS_FOUND' | translate }}
      </mat-option>
    </mat-autocomplete>

    <!-- Error message -->
    <mat-error *ngIf="!isValid()">
      {{ 'FIELD_FILTERS.VALIDATION.SELECT_AT_LEAST_ONE' | translate }}
    </mat-error>
  </mat-form-field>

  <!-- Selection summary -->
  <div class="selection-summary" *ngIf="selectedValues().length > 0">
    <small class="text-muted">
      {{ 'FIELD_FILTERS.MESSAGES.SELECTED_COUNT' | translate: {count: selectedValues().length} }}
    </small>
  </div>
</div>

<!-- No input required message cho is_empty/is_not_empty operators -->
<div class="no-input-message" *ngIf="!requiresInput()">
  <small class="text-muted">
    {{ 'FIELD_FILTERS.MESSAGES.NO_INPUT_REQUIRED' | translate }}
  </small>
</div>
