$backgroundColor: #fff;

.list-layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.list-layout-header {
  gap: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 1em;
}

.list-container {
  position: relative;
  display: flex;
  width: 100%;

  font-size: .95em;
  letter-spacing: -.01em;
  font-weight: 400;
  border-radius: 4px;
  // overflow: hidden;
  // overflow-y: auto;
  min-height: 300px;
}

.field-filters {
  position: absolute;
  top: 0;
  left: 0;
  width: 300px;
  height: 100%;

  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 1rem;

}



.table-wrapper {
  flex: 1;
  width: calc(100% - 300px);
  display: flex;
  position: relative;
  overflow: hidden;
  overflow-y: auto;
}

.fixed-columns {
  background: $backgroundColor;
  position: sticky;
  left: 0;
  z-index: 3;
}

.fixed-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 4;
  font-weight: 600;
  background-color: $backgroundColor;
}

.fixed-body {
  display: flex;
  flex-direction: column;
}

.fixed-row {
  display: flex;
}

.fixed-column {
  width: 150px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 40px;
}

.scrollable-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scrollable-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 2;
  font-weight: 600;
  will-change: transform;
  transition: transform 0.2s ease-out; /* Khớp với easing của Swiper */
}

.scrollable-section {
  width: 100%;
}

.scrollable-table {
  min-width: 100%;
}

.scrollable-row {
  display: flex;
}

.column-header,
.column-data {
  width: 200px;
  padding: 12px;
  border-bottom: 1px solid #e5e9f5;
  flex-shrink: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 40px;
  background-color: $backgroundColor;
}

.actions-column {
  // width: 200px;
  // padding: 12px;
  // border-left: 1px solid #ddd;
  // border-bottom: 1px solid #ddd;
  // text-align: center;
  // display: flex;
  // gap: 8px;
  // justify-content: center;
  // position: sticky;
  // right: 0;
  // background: $backgroundColor;
  // z-index: 1;
  // height: 40px;
}

.fixed-row.hover,
.scrollable-row.hover {
  .fixed-column,
  .column-header,
  .column-data {
    background: #e5e9f5;
  }
}

.list-layout-btn {
  display: flex;
  align-items: center;
  border: 1px solid #aeb3bf;
  border-radius: 0.6em;
  padding: 0.6em 1em 0.5em 1em;
  color: #2c3755;
  font-size: 0.9em;
  transition: border 0.3s ease-out, box-shadow 0.3s ease-out;
  border: 1px solid #C5C4D3;
  border-radius: 6px;
  cursor: pointer;
  text-align: left;
  position: relative;
  padding: 8px 25px 8px 10px;
  color: #313949;
  background: #fff;
  height: 34px;
  font-weight: 500;

  &:hover {
    background: #e1e5ed;
    border: 1px solid #9298a9;
    color: #1b2541;
  }

  .mat-icon {
    font-size: 20px;
  }
}




.scrollable-section.swiper {
  overflow: visible !important;
}

.swiper-wrapper {
  display: flex;
  width: 100%;
}

.swiper-slide {
  flex-shrink: 0;
  width: auto;
}
.swiper-scrollbar {
  position: sticky !important;
}

@media (max-width: 768px) {
  .fixed-column {
    width: 100px;
  }

  .column-header,
  .column-data {
    width: 150px;
  }

  .actions-column {
    width: 80px;
    position: relative;

    button {
      display: none;
    }

    &::after {
      content: '⋮';
      font-size: 20px;
      cursor: pointer;
    }

    &:hover .actions-dropdown {
      display: block;
    }
  }

  .actions-dropdown {
    display: none;
    position: absolute;
    top: 40px;
    right: 0;
    background: $backgroundColor;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    z-index: 10;

    button {
      display: block;
      width: 100%;
      text-align: left;
      margin-bottom: 4px;
    }
  }
}
