<div class="list-layout">
  <!-- Thanh filter -->
  <div class="list-layout-header flex flex-nowrap align-items-center">
    <!--
    <ng-container *ngFor="let filter of config.filters">
      @if(filter.type === 'search') {
      <mat-form-field
        appearance="outline"
        class="mat-form-field-no-subscript"
        >
        <mat-label>{{ filter.label }}</mat-label>

        <input
          matInput
          type="text"
          [(ngModel)]="filterValues()[filter.key]"
          (ngModelChange)="onFilterChange(filter.key, $event)"
          >

        <div matSuffix>
          <button
            mat-icon-button
            >
            <mat-icon
            >search</mat-icon>
          </button>
        </div>
      </mat-form-field>
      }

      <mat-form-field appearance="outline" *ngIf="filter.type === 'select'">
        <mat-label>{{ filter.label }}</mat-label>
        <mat-select [(ngModel)]="filterValues()[filter.key]" (ngModelChange)="onFilterChange(filter.key, $event)">
          <mat-option *ngFor="let option of filter.options" [value]="option.value">
            {{ option.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <ng-container *ngIf="filter.type === 'custom' && filter.template">
        <ng-container *ngTemplateOutlet="filter.template; context: { $implicit: filterValues(), onChange: onFilterChange.bind(this) }"></ng-container>
      </ng-container>
    </ng-container>
    -->

    <button
      mat-icon-button
      class="list-layout-btn"
      matTooltip="Info about the action"
      >
      <mat-icon class="me-2">filter_alt</mat-icon>
      <div>Filter</div>
    </button>

    <div class="ms-auto">
      <div class="btn btn-primary" (click)="editColumns()">
         Edit Columns
      </div>
    </div>
  </div>



  <!-- Danh sách -->
  <div class="list-container" #listContainer>
    <div class="field-filters" #filter>
      <div class="relative h-100">
        <app-field-filters
          [fields]="mockFields"
          [showTitle]="true"
          [showClearAll]="true"
          (filterChange)="onFieldFilterChange($event)"
          (filtersApplied)="onFiltersApplied($event)"
          (filtersReset)="onFiltersReset()">
        </app-field-filters>

        <div
          appResizePanel
          class="panel-resize-handle resize-handle--absolute"
          [leftPanel]="filter"
          [rightPanel]="list"
          [minWidth]="300"
          [maxWidth]="600"
          [isAbsolute]="true"
          [panelName]="'list-layout'">
        </div>
      </div>
    </div>


    <div class="table-wrapper" #list>
      <!-- Block trái: Fixed columns -->
      <div class="fixed-columns" #fixedColumns>
        <div class="fixed-header">
          <div *ngFor="let col of config.fixedColumns" class="fixed-column" (click)="sortColumn(col.key)">
            {{ col.label }}
            <span *ngIf="sortState().key === col.key && sortState().direction">
              {{ sortState().direction === 'asc' ? '↑' : '↓' }}
            </span>
          </div>
        </div>
        <div class="fixed-body">
          <div
            class="fixed-row"
            [class.hover]="hoverState()[item.id]"
            (mouseenter)="onRowHover(item)"
            (mouseleave)="onRowLeave(item)"
            *ngFor="let item of config.items; trackBy: trackById"
            >
            <ng-container *ngIf="fixedColumnTemplate; else defaultFixedColumns">
              <ng-container *ngTemplateOutlet="fixedColumnTemplate; context: { $implicit: item, fixedColumns: config.fixedColumns }"></ng-container>
            </ng-container>
            <ng-template #defaultFixedColumns>
              <div *ngFor="let col of config.fixedColumns" class="fixed-column">
                {{ item[col.key] }}
              </div>
            </ng-template>
          </div>
        </div>
      </div>

      <!-- Block phải: Scrollable columns + Actions -->
      <div class="scrollable-container" #scrollableContainer>
        <!-- Header sticky -->
        <div class="scrollable-header" #scrollableHeader>
          <div *ngFor="let col of columnOrder()" class="column-header" (click)="sortColumn(col)">
            {{ getColumnLabel(col) }}
            <span *ngIf="sortState().key === col && sortState().direction">
              {{ sortState().direction === 'asc' ? '↑' : '↓' }}
            </span>
          </div>
          <div class="column-header actions-column">Hành động</div>
        </div>
        <!-- Body cuộn ngang bằng Swiper -->
        <div class="scrollable-section swiper" #scrollableSection>
          <div class="swiper-wrapper">
            <div class="scrollable-table swiper-slide">
              <div class="scrollable-body">
                <div
                  class="scrollable-row"
                  [class.hover]="hoverState()[item.id]"
                  (mouseenter)="onRowHover(item)"
                  (mouseleave)="onRowLeave(item)"
                  *ngFor="let item of config.items; trackBy: trackById">
                  <ng-container *ngIf="scrollableColumnsTemplate; else defaultScrollableColumns">
                    <ng-container *ngTemplateOutlet="scrollableColumnsTemplate; context: { $implicit: item, visibleColumns: columnOrder() }"></ng-container>
                  </ng-container>
                  <ng-template #defaultScrollableColumns>
                    <div *ngFor="let col of columnOrder()" class="column-data">
                      {{ item[col] }}
                    </div>
                  </ng-template>

                  <div class="column-data actions-column">Hành động</div>


                  <ng-container *ngIf="actionsTemplate; else defaultActions">
                    <ng-container *ngTemplateOutlet="actionsTemplate; context: { $implicit: item, onActionClick: handleActionClick.bind(this) }"></ng-container>
                  </ng-container>
                  <ng-template #defaultActions>
                    <div class="actions-column">
                      <button (click)="handleActionClick(item, 'view')">Xem chi tiết</button>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>

          <div class="swiper-scrollbar" #scrollableScrollbar></div>
        </div>
      </div>
    </div>
  </div>

  <!-- MatPaginator -->
  <div #paginatorEle>
  <mat-paginator
    *ngIf="config.pager"
    [pageIndex]="config.pager.currentPage - 1"
    [pageSize]="config.pager.pageSize"
    [length]="config.pager.totalPages * config.pager.pageSize"
    [pageSizeOptions]="pageSizeOptions"
    (page)="onPageChange($event)">
  </mat-paginator>
  </div>
</div>
