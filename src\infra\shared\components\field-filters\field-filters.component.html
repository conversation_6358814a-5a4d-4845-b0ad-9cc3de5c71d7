<!-- Field Filters Component -->
<div class="field-filters-container">
  <!-- Header với title và clear all button -->
  <div class="filters-header">
    <h5 class="mb-3" *ngIf="showTitle">
      {{ 'FIELD_FILTERS.TITLE' | translate }}
    </h5>
    <mat-form-field
      appearance="outline"
      class="mat-form-field-no-subscript"
      >
      <mat-label>Search</mat-label>

      <input
        matInput
        type="text"
        [(ngModel)]="searchInput"
        >

      <div matSuffix>
        <button
          mat-icon-button
          >
          <mat-icon
          >search</mat-icon>
        </button>
      </div>
    </mat-form-field>
  </div>

  <!-- Danh sách filters -->
  <div class="filters-list" *ngIf="filters().length > 0; else noFields">
    <div
      class="filter-item mb-2"
      *ngFor="let filter of filters(); trackBy: trackByFieldId">
      <app-filter-field
        [filter]="filter"
        (filterChange)="onFilterChange($event)">
      </app-filter-field>
    </div>
  </div>

  <!-- Thông báo khi không có fields -->
  <ng-template #noFields>
    <div class="alert alert-info text-center">
      <i class="fas fa-info-circle me-2"></i>
      {{ 'FIELD_FILTERS.NO_FIELDS' | translate }}
    </div>
  </ng-template>

  <!-- Summary của active filters -->
  <div class="active-filters-summary mt-3" *ngIf="hasActiveFilters()">
    <small class="text-muted">
      <i class="fas fa-filter me-1"></i>
      {{ activeFilters().length }} filter(s) active
    </small>
  </div>

  <!-- Filter Actions Buttons -->
  <div class="filter-actions d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
    <div class="d-flex flex-column flex-md-row gap-2 w-100">
      <button
        type="button"
        class="btn btn-primary"
        [disabled]="!canApplyFilters()"
        (click)="onApplyFilters()">
        <i class="fas fa-check me-1"></i>
        {{ 'FIELD_FILTERS.APPLY' | translate }}
      </button>

      <button
        type="button"
        class="btn btn-outline-secondary"
        [disabled]="!canApplyFilters()"
        (click)="onResetFilters()">
        <i class="fas fa-times me-1"></i>
        {{ 'FIELD_FILTERS.CANCEL' | translate }}
      </button>
    </div>
  </div>
</div>
