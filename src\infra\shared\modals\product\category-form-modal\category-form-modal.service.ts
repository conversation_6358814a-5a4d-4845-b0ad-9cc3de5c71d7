import { Injectable } from '@angular/core';
import { MatDialogConfig } from '@angular/material/dialog';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { CategoryFormModalComponent } from './category-form-modal.component';
import { ProductCategory } from '@mock/product_form';

/**
 * Service để mở modal thêm/sửa danh mục sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class CategoryFormModalService {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal thêm/sửa danh mục sản phẩm
   * @param data Dữ liệu danh mục sản phẩm (nếu có)
   * @returns Promise với kết quả là thông tin danh mục sản phẩm sau khi lưu
   */
  async open(data: { category?: ProductCategory, parentCategories: ProductCategory[] }): Promise<ProductCategory | undefined> {
    const modalConfig: MatDialogConfig<{ category?: ProductCategory, parentCategories: ProductCategory[] }> = {
      data: data,
      width: '600px',
      maxWidth: '95vw'
    };

    try {
      // Mở modal với type rõ ràng
      return await this.responsiveModalService.open<
        CategoryFormModalComponent,
        { category?: ProductCategory, parentCategories: ProductCategory[] },
        ProductCategory
      >(CategoryFormModalComponent, modalConfig);
    } catch (error) {
      console.error('Lỗi khi mở modal danh mục sản phẩm:', error);
      return undefined;
    }
  }
}
