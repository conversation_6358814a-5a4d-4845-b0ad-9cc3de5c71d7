import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { PromotionModalService } from '@/features/sales/order-form/components/modals/promotion-modal';
import { VariantFormModalService } from '@/shared/modals/product/variant-form-modal';
import { MatDialog } from '@angular/material/dialog';
import { NoteModalService } from '@/features/sales/order-form/components/modals/note-modal';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import { MixedPaymentModalService } from '@/features/sales/order-form/components/modals/mixed-payment-modal/mixed-payment-modal.service';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { OrderItemModifierModalService } from '@/shared/modals/sales/order/order-item-modifier-modal/order-item-modifier-modal.service';
import { mockProductSelectorConfig } from '@/mock/sales/order_form.mock';
import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { OrderItemVariantUnitSelectionModalService } from '@/shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { OrderProductPickerComponent } from '@/shared/components/product-selection/order-product-picker/order-product-picker.component';
import { InputPlaceComponent } from '../input/input-place/input-place.component';
import { AddressManualSelectorComponent } from '../input/input-place/components/address-manual-selector/address-manual-selector.component';
import { InputAddressComponent } from '../input/input-place/components/input-address/input-address.component';
import { mockProductModifierGroupList, mockProductUnits, mockVariantList, mockProductList } from '@/mock/shared/product.mock';
import { BatchModalComponent, BatchData } from '@/shared/modals/warehouse/batch-modal/batch-modal.component';
import { CategoryProductModalComponent } from '@/features/warehouse/goods-receipt/components/category-product-modal/category-product-modal.component';
import { AdditionalCostModalService } from '@/features/warehouse/goods-receipt/components/additional-cost-modal/additional-cost-modal.service';
import { TaxFormModalService } from '@/shared/modals/common/tax-form-modal';
import { TaxInfo } from 'salehub_shared_contracts/entities/ims/inventory/goods_receipt';
import { QualityCheckRejectModalComponent, RejectedItem, QualityCheckRejectModalData } from '@/features/warehouse/goods-receipt/components/quality-check-reject-modal/quality-check-reject-modal.component';
import { SimpleNoteModalService } from '@/shared/modals/common/simple-note-modal';
import { ProductFilterModalComponent } from '@/features/warehouse/inventory-check/components/product-filter-modal/product-filter-modal.component';
import { FieldPermissionModalService } from '@/shared/modals/field-permission';
import { ProductFilterModalData, EmbeddedProductSerial, ProductListItem } from '@/features/warehouse/inventory-check/models/api/inventory-check.dto';
import { ProductFilterResult } from '@/features/warehouse/inventory-check/models/view/inventory-check.view-model';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import { ConfirmModalService } from '@/shared/modals/common/confirm-modal';
import { FieldPropertiesModalComponent } from '@/shared/modals/field-properties/field-properties-modal.component';
import { FieldPropertiesData } from '@/shared/modals/field-properties';
import { DynamicLayoutBuilderComponent } from '../dynamic-layout-builder/dynamic-layout-builder.component';
import { FieldFiltersComponent } from '../field-filters/field-filters.component';
import { Field } from '@domain/entities/field.entity';
import { FilterChangeEvent, FieldFilter } from '../field-filters/models/view/field-filter-view.model';
import { mockFields } from '@/mock/fields.mock';

@Component({
  selector: 'app-test-theme',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    InputPlaceComponent,
    FieldFiltersComponent,
    // OrderProductPickerComponent
  ],
  templateUrl: './test-theme.component.html',
  styleUrl: './test-theme.component.scss'
})
export class TestThemeComponent implements OnInit {
  // Sử dụng property injection thay vì constructor
  private dialog = inject(MatDialog);
  private bottomSheet = inject(MatBottomSheet);
  private responsiveModalService = inject(ResponsiveModalService);
  private mixedPaymentModalService = inject(MixedPaymentModalService);
  private productModifierFormModalService = inject(OrderItemModifierModalService);
  private orderItemVariantUnitSelectionModalService = inject(OrderItemVariantUnitSelectionModalService);
  private noteModalService = inject(NoteModalService);
  private promotionModalService = inject(PromotionModalService);
  private variantFormModalService = inject(VariantFormModalService);
  private simpleNoteModalService = inject(SimpleNoteModalService);
  private fieldPermissionModalService = inject(FieldPermissionModalService);
  private taxFormModalService = inject(TaxFormModalService);
  private confirmModalService = inject(ConfirmModalService);

  // Dữ liệu mẫu cho ProductSelector
  productSelectorConfig = mockProductSelectorConfig;

  selectedProducts: OrderItemBaseDetails[] = [];

  // Mock data cho Field Filters
  mockFields = mockFields;

  ngOnInit(): void {
  }

  async openProductModifiersSheet(): Promise<void> {
    // Tạo mock dữ liệu cho OrderItemBaseDetails
    const orderItem: OrderItemBaseDetails = {
      quantity: 1,
      product: {
        productId: 'prod1',
        cost: 150000,
        name: 'Phở bò tái lăn',
        price: 200000,
        variant: {
          variantId: 'v1',
          attributes: [{ name: 'size', value: 'L' }, { name: 'color', value: 'Xanh' }]
        }
      }
    };

    try {
      // Mở modal chọn modifier sử dụng service
      const result = await this.productModifierFormModalService.open({
        list: mockProductModifierGroupList,
        data: orderItem
      });

      if (result) {
        console.log('Product modifier modal result:', result);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn modifier:', error);
    }
  }

  async openMixedPaymentDialog(): Promise<void> {
    // Sample data
    const orderPayment: OrderPayment = {
      totalAmount: 1625000,
      payments: [],
      paidAmount: 0,
      remainingAmount: 1625000,
      paymentStatus: 'unpaid'
    };

    try {
      const result = await this.mixedPaymentModalService.open(orderPayment);
      if (result) {
        console.log('Modal result:', result);
      }
    } catch (error) {
      console.error('Error opening mixed payment modal:', error);
    }
  }

  openVariantSelector() {
    // Mở modal chọn variant và unit sử dụng service
    this.orderItemVariantUnitSelectionModalService.open({
      variants: mockVariantList,
      currentValue: {
        variant: mockVariantList[1], // Chọn variant thứ 2 (L, Đỏ)
        unit: mockProductUnits[1] // Chọn đơn vị vỉ
      },
      units: mockProductUnits
    }).then(result => {
      if (result) {
        console.log('Selected variant:', result.variant);
        console.log('Selected unit:', result.unit);
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn variant và unit:', error);
    });
  }

  /**
   * Mở modal form thuộc tính sản phẩm
   */
  async openVariantFormModal(): Promise<void> {
    try {
      const result = await this.variantFormModalService.open({
        valueSuggestions: ['S', 'M', 'L', 'XL', 'Đỏ', 'Xanh', 'Đen', 'Trắng']
      });

      if (result) {
        console.log('Variant form modal result:', result);
        alert(`Thuộc tính đã được tạo: ${result.name} với ${result.values.length} giá trị`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal form thuộc tính sản phẩm:', error);
    }
  }

  openProductForm(): void {
    console.log('Open product form dialog');
    // Placeholder for opening the product form dialog
    // Trong triển khai thực tế, sẽ mở dialog product-form
  }

  openBatchDialog(): void {
    this.responsiveModalService.open<
      BatchModalComponent,
      BatchData,
      BatchData
    >(
      BatchModalComponent,
      {
        data: {
          batchNumber: '',
          manufacturingDate: new Date(),
          expiryDate: null,
          quantity: null
        }
      }
    ).then(result => {
      if (result) {
        console.log('Batch dialog result:', result);
      }
    }).catch(error => {
      console.error('Error in batch modal:', error);
    });
  }

  openCategoryProductDialog(): void {
    this.responsiveModalService
      .open<CategoryProductModalComponent, any, any>(
        CategoryProductModalComponent,
        { width: '450px', disableClose: false }
      )
      .then(result => {
        console.log('Selected products:', result);
        if (result) {
          alert(`Đã chọn ${result.length} sản phẩm từ nhóm hàng`);
        }
      })
      .catch(() => {});
  }

  /**
   * Mở dialog thêm/sửa chi phí
   */
  async openAdditionalCostDialog(isEdit: boolean = false): Promise<void> {
    // Khởi tạo dữ liệu mẫu chi phí
    const mockCost: ImportAdditionalCost = {
      _id: 'cost1',
      name: 'Phí vận chuyển',
      costValue: {
        type: 'fixed',
        value: 250000
      },
      paidToSupplier: true,
      allocateToItems: true,
      isActive: true,
      autoAddToPurchaseOrder: false,
      refundOnReturn: true,
      tax: {
        rate: 10,
        amount: 25000
      }
    };

    const additionalCostModalService = inject(AdditionalCostModalService);
    const result = await additionalCostModalService.open({
      cost: isEdit ? mockCost : undefined,
      subTotal: 5000000
    });

    if (result) {
      console.log('Additional cost dialog result:', result);
      alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} chi phí: ${result.name}`);
    }
  }

  // Test SelectAdditionalCostsDialog
  openSelectAdditionalCostsDialog(): void {
    // // Khởi tạo dữ liệu mẫu đã chọn
    // const selectedCosts: ImportAdditionalCost[] = [
    //   mockImportAdditionalCosts[0],
    //   mockImportAdditionalCosts[2]
    // ];

    // const dialogRef = this.dialog.open(SelectAdditionalCostsDialogComponent, {
    //   width: '900px',
    //   data: {
    //     items: mockImportAdditionalCosts,
    //     current: selectedCosts,
    //     subTotal: 5000000
    //   }
    // });

    // dialogRef.afterClosed().subscribe((result: ImportAdditionalCost[] | undefined) => {
    //   if (result) {
    //     console.log('Selected additional costs:', result);
    //     alert(`Đã chọn ${result.length} chi phí khác`);
    //   }
    // });
  }

  // Test TaxFormModal
  async openTaxDialog(isEdit: boolean = false): Promise<void> {
    const mockTax: TaxInfo = {
      type: 'VAT',
      rate: 10,
      amount: 500000
    };

    try {
      const result = await this.taxFormModalService.open({
        tax: isEdit ? mockTax : undefined,
        subTotal: 5000000
      });

      if (result) {
        console.log('Tax modal result:', result);
        alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} thuế: ${result.type} với số tiền ${result.amount} VND`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thuế:', error);
    }
  }

  // Test QualityCheckRejectModal
  async openQualityCheckRejectDialog(isEdit: boolean = false): Promise<void> {
    // Tạo mock data cho GoodsReceiptItem
    const mockItems = [
      {
        _id: 'item1',
        product: {
          productId: 'prod1',
          name: 'Áo thun nam',
          sku: 'ATN123',
          price: 150000,
          cost: 100000
        },
        quantityReceived: 50,
        price: 150000,
        subTotal: 7500000,
        total: 7500000,
        inventoryTransactionId: 'inv1'
      },
      {
        _id: 'item2',
        product: {
          productId: 'prod2',
          name: 'Quần jean nữ',
          sku: 'QJN456',
          price: 320000,
          cost: 200000
        },
        quantityReceived: 30,
        price: 320000,
        subTotal: 9600000,
        total: 9600000,
        inventoryTransactionId: 'inv2'
      },
      {
        _id: 'item3',
        product: {
          productId: 'prod3',
          name: 'Áo khoác nam',
          sku: 'AKN789',
          price: 450000,
          cost: 300000
        },
        quantityReceived: 20,
        price: 450000,
        subTotal: 9000000,
        total: 9000000,
        inventoryTransactionId: 'inv3'
      }
    ];

    // Tạo mock data cho RejectedItem nếu đang chỉnh sửa
    const mockRejectedItem: RejectedItem | undefined = isEdit ? {
      _id: 'item1',
      quantity: 5,
      reason: 'Hàng bị lỗi màu, không đúng với mẫu'
    } : undefined;

    const modalData: QualityCheckRejectModalData = {
      items: mockItems,
      rejectedItem: mockRejectedItem
    };

    const result = await this.responsiveModalService.open<
      QualityCheckRejectModalComponent,
      QualityCheckRejectModalData,
      RejectedItem | undefined
    >(QualityCheckRejectModalComponent, {
      data: modalData,
      width: '500px',
      maxWidth: '95vw'
    });

    if (result) {
      console.log('Quality check reject modal result:', result);
      // Tìm tên sản phẩm để hiển thị
      const selectedItem = mockItems.find(item => item._id === result._id);
      const productName = selectedItem ? selectedItem.product.name : 'Sản phẩm';
      alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} sản phẩm bị từ chối: ${productName} - Số lượng: ${result.quantity}`);
    }
  }

  /**
   * Mở SimpleNoteModal để thêm/sửa ghi chú
   * @param existingNote Ghi chú hiện tại (nếu có)
   */
  async openSimpleNoteDialog(existingNote: string = ''): Promise<void> {
    try {
      // Mở modal với ghi chú hiện tại (nếu có)
      const title = existingNote ? 'COMMON.EDIT_NOTE' : 'COMMON.ADD_NOTE';
      const result = await this.simpleNoteModalService.open(title, existingNote);

      // Xử lý kết quả
      if (result !== undefined) {
        console.log('Simple note modal result:', result);
        alert(`Ghi chú đã được ${existingNote ? 'cập nhật' : 'thêm'}: ${result}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú đơn giản:', error);
    }
  }

  /**
   * Mở NoteModal để thêm/sửa ghi chú
   */
  async openNoteModal(): Promise<void> {
    try {
      const result = await this.noteModalService.open({
        internalNote: 'Ghi chú nội bộ mẫu',
        note: 'Ghi chú công khai mẫu'
      });

      if (result) {
        console.log('Note modal result:', result);
        alert(`Ghi chú đã được cập nhật:\nNội bộ: ${result.internalNote}\nCông khai: ${result.note}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú:', error);
    }
  }

  /**
   * Mở PromotionModal để thêm/sửa khuyến mãi
   */
  async openPromotionModal(): Promise<void> {
    try {
      const result = await this.promotionModalService.open({
        totalAmount: 500000,
        discountType: 'amount',
        discountValue: 50000,
        promotionName: 'Khuyến mãi mẫu',
        finalAmount: 450000
      });

      if (result) {
        console.log('Promotion modal result:', result);
        alert(`Khuyến mãi đã được cập nhật:\nLoại: ${result.discountType}\nGiá trị: ${result.discountValue}\nTên: ${result.promotionName}\nThành tiền: ${result.finalAmount}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal khuyến mãi:', error);
    }
  }

  /**
   * Mở ConfirmModal để xác nhận hành động
   */
  async openConfirmDialog(): Promise<void> {
    try {
      const result = await this.confirmModalService.confirm({
        title: 'COMMON.CONFIRM_ACTION',
        message: 'COMMON.CONFIRM_ACTION_MESSAGE',
        confirmText: 'COMMON.YES',
        cancelText: 'COMMON.NO',
        confirmColor: 'primary'
      });

      console.log('Confirm modal result:', result);
      alert(`Kết quả xác nhận: ${result ? 'Đồng ý' : 'Hủy bỏ'}`);
    } catch (error) {
      console.error('Lỗi khi mở modal xác nhận:', error);
    }
  }

  /**
   * Mở FieldPermissionModal để thiết lập quyền truy cập field
   */
  async openFieldPermissionModal(): Promise<void> {
    try {
      const result = await this.fieldPermissionModalService.openWithMockData(
        'Customer Name', // Tên field
        5 // Số lượng profiles
      );

      if (result) {
        console.log('Field permission modal result:', result);
        const summary = result.map(p => `${p.name}: ${p.permission}`).join('\n');
        alert(`Quyền truy cập đã được cập nhật:\n${summary}`);
      } else {
        alert('Thiết lập quyền đã bị hủy!');
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thiết lập quyền field:', error);
    }
  }

  /**
   * Mở FieldPropertiesModal để chỉnh sửa thuộc tính field
   */
  async openFieldPropertiesModal(fieldType: string = 'text'): Promise<void> {
    try {
      // Tạo mock data cho CustomField
      const mockField: any = {
        _id: 1,
        label: 'Customer Name',
        type: fieldType,
        value: fieldType === 'checkbox' ? false : (fieldType === 'multi-picklist' ? [] : ''),
        isPublic: false,
        isRequired: true,
        tooltip: 'Enter customer full name',
        constraints: this.getMockConstraints(fieldType)
      };

      const modalData: FieldPropertiesData = {
        field: mockField,
        availableModules: [
          { _id: 'sales_quotes', name: 'Sales Quotes' },
          { _id: 'contacts', name: 'Contacts' },
          { _id: 'transactions', name: 'Transactions' }
        ]
      };

      const result = await this.responsiveModalService.open<
        FieldPropertiesModalComponent,
        FieldPropertiesData,
        any
      >(FieldPropertiesModalComponent, {
        data: modalData,
        width: '600px',
        maxWidth: '95vw',
        maxHeight: '90vh'
      });

      if (result) {
        console.log('Field properties modal result:', result);
        alert(`Thuộc tính field đã được cập nhật:\nNhãn: ${result.label}\nLoại: ${result.type}\nBắt buộc: ${result.isRequired ? 'Có' : 'Không'}`);
      } else {
        alert('Chỉnh sửa thuộc tính đã bị hủy!');
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chỉnh sửa thuộc tính field:', error);
    }
  }

  /**
   * Tạo mock constraints dựa trên field type
   */
  private getMockConstraints(fieldType: string): any {
    switch (fieldType) {
      case 'text':
      case 'phone':
      case 'url':
        return { maxLength: 255, unique: false };
      case 'number':
        return { maxDigits: 9 };
      case 'textarea':
        return { textType: 'small', maxLength: 2000 };
      case 'picklist':
        return {
          picklistValues: ['Option 1', 'Option 2', 'Option 3'],
          sortOrder: 'input',
          defaultValue: 'Option 1'
        };
      case 'multi-picklist':
        return {
          picklistValues: ['Red', 'Green', 'Blue', 'Yellow'],
          sortOrder: 'alphabetical',
          defaultValue: ['Red', 'Blue']
        };
      case 'search':
        return { searchModule: 'sales_quotes' };
      case 'user':
        return { userType: 'single' };
      case 'upload-file':
        return { allowMultipleFiles: false, maxFiles: 1 };
      case 'upload-image':
        return { maxImages: 3 };
      case 'currency':
        return { maxDigits: 9, decimalPlaces: 2, rounding: 'normal' };
      case 'decimal':
        return { maxDigits: 16, decimalPlaces: 2, useNumberSeparator: true };
      case 'checkbox':
        return { enableByDefault: false };
      default:
        return {};
    }
  }

  /**
   * Mở ProductFilterDialog để lọc sản phẩm
   */
  openProductFilterDialog(): void {
    // Giả định đã chọn kho với ID 'wh1'
    const warehouseId = 'wh1';

    // Trạng thái bộ lọc hiện tại (giả định đã chọn một số danh mục)
    const currentFilter = {
      category: ['cat1', 'cat3'], // Đã chọn danh mục Quần áo người lớn và Giày dép
      warehouseLocation: null // Chưa chọn vị trí kho
    };

    // Mở modal
    this.responsiveModalService.open<
      ProductFilterModalComponent,
      ProductFilterModalData,
      ProductFilterResult
    >(
      ProductFilterModalComponent,
      {
        data: {
          current: currentFilter,
          warehouseId: warehouseId
        }
      }
    ).then((result: ProductFilterResult | undefined) => {
      if (result) {
        console.log('Product filter modal result:', result);
        alert(`Đã lọc được ${result.products.length} sản phẩm`);
      }
    }).catch(error => {
      console.error('Error opening product filter modal:', error);
    });
  }

  /**
   * Mở SerialNumberDialog để quản lý số serial
   */
  openSerialNumberDialog(): void {
    // Tạo một sản phẩm mẫu
    const mockProduct: ProductListItem = mockProductList[0];

    // Tạo danh sách serial mẫu
    const mockSerials: EmbeddedProductSerial[] = [
      { _id: 'serial1', serialNumber: `${mockProduct.sku}-SN001`, status: 'in_stock' },
      { _id: 'serial2', serialNumber: `${mockProduct.sku}-SN002`, status: 'in_stock' },
      { _id: 'serial3', serialNumber: `${mockProduct.sku}-SN003`, status: 'sold' },
      { _id: 'serial4', serialNumber: `${mockProduct.sku}-SN004`, status: 'damaged' },
      { _id: 'serial5', serialNumber: `${mockProduct.sku}-SN005`, status: 'in_transit' }
    ];

    // Mở dialog
    // const dialogRef = this.dialog.open(SerialNumberDialogComponent, {
    //   width: '600px',
    //   data: {
    //     product: mockProduct,
    //     serials: mockSerials
    //   }
    // });

    // // Xử lý kết quả khi đóng dialog
    // dialogRef.afterClosed().subscribe((result: EmbeddedProductSerial[] | undefined) => {
    //   if (result) {
    //     console.log('Serial number dialog result:', result);
    //     const inStockCount = result.filter(serial =>
    //       serial.status === 'in_stock' || serial.status === 'assigned'
    //     ).length;
    //     alert(`Đã cập nhật ${result.length} số serial, số lượng thực tế: ${inStockCount}`);
    //   }
    // });
  }

  /**
   * Xử lý khi filter thay đổi
   * @param event - Filter change event
   */
  onFieldFilterChange(event: FilterChangeEvent): void {
    console.log('Field filter changed:', event);

    // Tìm field tương ứng
    const field = this.mockFields.find(f => f._id === event.fieldId);
    if (field) {
      const status = event.isActive ? 'activated' : 'deactivated';
      console.log(`Filter for "${field.label}" ${status}`);

      if (event.isActive && event.filterValue) {
        console.log('Filter value:', event.filterValue);
      }
    }
  }

  /**
   * Xử lý khi các filters được áp dụng
   * @param activeFilters - Danh sách các filter đang active
   */
  onFiltersApplied(activeFilters: FieldFilter[]): void {
    console.log('Active filters applied:', activeFilters);

    if (activeFilters.length > 0) {
      const filterSummary = activeFilters.map(filter => {
        const operator = filter.filterValue?.operator || 'unknown';
        const value = this.getFilterValueDisplay(filter.filterValue);
        return `${filter.field.label}: ${operator}${value ? ` (${value})` : ''}`;
      }).join(', ');

      console.log(`Applied ${activeFilters.length} filter(s): ${filterSummary}`);

      // Hiển thị thông báo
      alert(`✅ Đã áp dụng ${activeFilters.length} bộ lọc:\n${filterSummary}`);
    } else {
      console.log('No filters applied');
    }
  }

  /**
   * Xử lý khi filters được reset
   */
  onFiltersReset(): void {
    console.log('Filters have been reset');
    alert('🔄 Đã đặt lại tất cả bộ lọc');
  }

  /**
   * Helper method để hiển thị giá trị filter
   * @param filterValue - Giá trị filter
   * @returns String representation của giá trị
   */
  private getFilterValueDisplay(filterValue: any): string {
    if (!filterValue) return '';

    if (filterValue.value !== undefined) {
      return String(filterValue.value);
    }

    if (filterValue.values && Array.isArray(filterValue.values)) {
      return filterValue.values.join(', ');
    }

    if (filterValue.minValue !== undefined && filterValue.maxValue !== undefined) {
      return `${filterValue.minValue} - ${filterValue.maxValue}`;
    }

    if (filterValue.timeValue !== undefined && filterValue.timeUnit) {
      return `${filterValue.timeValue} ${filterValue.timeUnit}`;
    }

    return '';
  }
}
