import { Injectable } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { ImportAdditionalCost } from '../../models/api/goods-receipt.dto';
import { SelectAdditionalCostsModalComponent, SelectAdditionalCostsModalData } from './select-additional-costs-modal.component';

@Injectable({
  providedIn: 'root'
})
export class SelectAdditionalCostsModalService {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal chọn chi phí bổ sung
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<ImportAdditionalCost[] | undefined>
   */
  async open(data: SelectAdditionalCostsModalData): Promise<ImportAdditionalCost[] | undefined> {
    return this.responsiveModalService.open<
      SelectAdditionalCostsModalComponent,
      SelectAdditionalCostsModalData,
      ImportAdditionalCost[]
    >(SelectAdditionalCostsModalComponent, {
      data,
      width: '900px',
      maxWidth: '95vw',
      panelClass: 'select-additional-costs-modal'
    });
  }
}
