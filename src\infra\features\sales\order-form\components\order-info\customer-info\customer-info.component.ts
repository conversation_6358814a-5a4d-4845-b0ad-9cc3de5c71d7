import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { debounceTime, distinctUntilChanged, filter, switchMap } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';
import { InputPlaceComponent } from '@shared/components/input/input-place/input-place.component';
import { Place } from 'salehub_shared_contracts';
import { CustomerService } from './customer.service';

@Component({
  selector: 'app-customer-info',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatIconModule,
    MatChipsModule,
    InputPlaceComponent,
    TranslateModule
  ],
  templateUrl: './customer-info.component.html',
  styleUrls: ['./customer-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CustomerInfoComponent implements OnInit {
  @Input() customer: any = {};
  @Output() customerUpdated = new EventEmitter<any>();

  expanded = true;
  customerSuggestions: any[] = [];
  showCustomerDetails = false;

  // Subjects để xử lý search debounce
  searchPhone$ = new Subject<string>();
  searchName$ = new Subject<string>();

  constructor(private customerService: CustomerService) {}

  ngOnInit(): void {
    // Thiết lập search debounce cho số điện thoại
    this.searchPhone$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(term => term.length >= 3),
      switchMap(term => this.customerService.getCustomerSuggestions(term))
    ).subscribe(suggestions => {
      this.customerSuggestions = suggestions;
    });

    // Thiết lập search debounce cho tên khách hàng
    this.searchName$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(term => term.length >= 3),
      switchMap(term => this.customerService.getCustomerSuggestions(term))
    ).subscribe(suggestions => {
      this.customerSuggestions = suggestions;
    });
  }

  /**
   * Xử lý khi user chọn một khách hàng từ danh sách gợi ý
   */
  selectCustomer(customer: any): void {
    if (!this.customer) {
      this.customer = {};
    }

    // Cập nhật thông tin khách hàng với dữ liệu đã chọn
    this.customer.name = customer.name;
    this.customer.phoneNumber = customer.phoneNumber;
    this.customer.address = customer.address;
    this.customer._id = customer._id;
    this.customer.note = customer.note;
    this.customer.tags = customer.tags;
    this.customer.score = customer.score;
    this.customer.totalSold = customer.totalSold;
    this.customer.countOrders = customer.countOrders;
    this.customer.avatar = customer.avatar;

    // Hiển thị thông tin chi tiết của khách hàng
    this.showCustomerDetails = true;

    // Emit sự kiện thông báo thông tin khách hàng đã thay đổi
    this.customerUpdated.emit(this.customer);
  }

  /**
   * Xử lý khi user nhập số điện thoại
   */
  onPhoneSearch(term: string): void {
    this.searchPhone$.next(term);
  }

  /**
   * Xử lý khi user nhập tên khách hàng
   */
  onNameSearch(term: string): void {
    this.searchName$.next(term);
  }

  /**
   * Xử lý khi địa chỉ thay đổi
   */
  onAddressChange(place: Place): void {
    if (!this.customer) {
      this.customer = {};
    }
    this.customer.address = place;
    this.customerUpdated.emit(this.customer);
  }

  /**
   * Xử lý khi ghi chú thay đổi
   */
  onNoteChange(): void {
    this.customerUpdated.emit(this.customer);
  }

  /**
   * Xử lý khi user thay đổi trực tiếp trường input
   */
  onInputChange(): void {
    this.customerUpdated.emit(this.customer);
  }

  /**
   * Lấy màu CSS cho chip dựa vào loại tag
   */
  getChipColor(type: string): string {
    switch (type) {
    case 'success': return 'mat-success';
    case 'danger': return 'mat-warn';
    case 'primary': return 'mat-primary';
    default: return '';
    }
  }
}
