import { ChangeDetectionStrategy, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';

// Mock data
import { mockEmployeeList, mockWarehouseList, mockSuppliers, mockWarehouseLocations } from '@mock/shared/list.mock';

// Models
import {
  GoodsReceiptItem,
  EmbeddedProduct,
  QualityCheck,
  TransportInfo,
  ImportAdditionalCost,
  TaxInfo,
  GoodsReceipt,
  ProductListItem
} from '../models/api/goods-receipt.dto';
import {
  CategoryProductModalData,
} from '../models/view/goods-receipt.view-model';

// Services
import { GoodsReceiptService } from '../services/goods-receipt.service';

// Components
import { ProductSearchWrapperComponent } from '@/features/warehouse/goods-receipt/components/product-search-wrapper/product-search-wrapper.component';
import { QualityCheckComponent } from '@/features/warehouse/goods-receipt/components/quality-check/quality-check.component';
import { ProductListComponent } from '@/features/warehouse/goods-receipt/components/product-list/product-list.component';
import { TransportInfoComponent } from '@/features/warehouse/goods-receipt/components/transport-info/transport-info.component';
import { FinancialInfoComponent } from '@/features/warehouse/goods-receipt/components/financial-info/financial-info.component';
import { BasicInfoComponent } from '@/features/warehouse/goods-receipt/components/basic-info/basic-info.component';
import { CategoryProductModalComponent } from '@/features/warehouse/goods-receipt/components/category-product-modal/category-product-modal.component';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';

@Component({
  selector: 'app-goods-receipt',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    ProductSearchWrapperComponent,
    QualityCheckComponent,
    ProductListComponent,
    TransportInfoComponent,
    FinancialInfoComponent,
    BasicInfoComponent
  ],
  templateUrl: './goods-receipt.component.html',
  styleUrls: ['./goods-receipt.component.scss'],
  providers: [
    GoodsReceiptService
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GoodsReceiptComponent implements OnInit, OnDestroy {
  private subscriptions = new Subscription();

  // Mock data
  employeeList = mockEmployeeList;
  warehouseList = mockWarehouseList;
  supplierList = mockSuppliers;
  warehouseLocations = mockWarehouseLocations;

  // Dữ liệu form
  basicInfo: Partial<GoodsReceipt> = {};
  qualityCheckInfo: Partial<QualityCheck> = {};
  productItems: GoodsReceiptItem[] = [];
  transportInfo: Partial<TransportInfo> = {};
  financialInfo: Partial<GoodsReceipt> = {};

  // Tính toán tài chính
  subTotal: number = 0;
  totalDiscount: number = 0;
  totalSupplierAdditionalCost: number = 0;
  totalOtherAdditionalCost: number = 0;
  totalAdditionalCost: number = 0;
  totalTax: number = 0;
  additionalCosts: ImportAdditionalCost[] = [];
  taxes: TaxInfo[] = [];

  // ID kho hàng đã chọn
  selectedWarehouseId: string = '';

  // Tham chiếu đến component danh sách sản phẩm
  @ViewChild(ProductListComponent) productListComponent!: ProductListComponent;

  constructor(
    private goodsReceiptService: GoodsReceiptService,
    private dialog: MatDialog,
    private responsiveModalService: ResponsiveModalService
  ) {}

  ngOnInit(): void {
    console.log('GoodsReceiptComponent initialized');
  }

  /**
   * Xử lý khi thông tin cơ bản thay đổi
   * @param data Dữ liệu thông tin cơ bản
   */
  onBasicInfoChanged(data: Partial<GoodsReceipt>): void {
    this.basicInfo = data;
  }

  /**
   * Xử lý khi kho hàng thay đổi
   * @param warehouseId ID kho hàng
   */
  onWarehouseChanged(warehouseId: string): void {
    this.selectedWarehouseId = warehouseId;
  }

  /**
   * Xử lý khi thông tin kiểm tra chất lượng thay đổi
   * @param data Dữ liệu kiểm tra chất lượng
   */
  onQualityCheckChanged(data: Partial<QualityCheck>): void {
    this.qualityCheckInfo = data;
  }

  /**
   * Xử lý khi chọn sản phẩm từ tìm kiếm
   * @param product Sản phẩm đã chọn
   */
  onProductSelected(product: EmbeddedProduct): void {
    // Xử lý thêm sản phẩm vào danh sách
    console.log('Product selected:', product);

    // Thêm sản phẩm vào danh sách thông qua tham chiếu ViewChild
    if (this.productListComponent) {
      this.productListComponent.addProduct(product);
    }
  }

  /**
   * Xử lý khi nhấn nút thêm từ danh mục
   */
  onAddFromCategory(): void {
    // Mở modal chọn sản phẩm từ danh mục
    this.responsiveModalService
      .open<CategoryProductModalComponent, CategoryProductModalData, ProductListItem[]>(
        CategoryProductModalComponent,
        {
          width: '600px',
          disableClose: false,
          data: {}
        }
      )
      .then(result => {
        if (result && this.productListComponent && Array.isArray(result)) {
          console.log('Selected products from categories:', result);
          // Thêm các sản phẩm đã chọn vào danh sách
          // Chuyển đổi mỗi sản phẩm từ mockProductList sang EmbeddedProduct
          result.forEach(product => {
            const embeddedProduct: EmbeddedProduct = {
              productId: product.productId,
              name: product.name,
              sku: product.sku,
              price: product.price,
              cost: product.cost
            };
            this.productListComponent.addProduct(embeddedProduct);
          });
        }
      })
      .catch((e) => console.log(e));
  }

  /**
   * Xử lý khi nhấn nút in
   */
  onPrintList(): void {
    // Xử lý in danh sách
    console.log('Print list clicked');
  }

  /**
   * Xử lý khi danh sách sản phẩm thay đổi
   * @param items Danh sách sản phẩm
   */
  onItemsChanged(items: GoodsReceiptItem[]): void {
    this.productItems = items;
  }

  /**
   * Xử lý khi tổng tiền thay đổi
   * @param subtotal Tổng tiền
   */
  onSubtotalChanged(subtotal: number): void {
    this.subTotal = subtotal;
  }

  /**
   * Xử lý khi thông tin vận chuyển thay đổi
   * @param data Dữ liệu thông tin vận chuyển
   */
  onTransportInfoChanged(data: Partial<TransportInfo>): void {
    this.transportInfo = data;
  }

  /**
   * Xử lý khi thông tin tài chính thay đổi
   * @param data Dữ liệu thông tin tài chính
   */
  onFinancialInfoChanged(data: Partial<GoodsReceipt>): void {
    this.financialInfo = data;
  }

  /**
   * Xử lý khi chi phí bổ sung thay đổi
   * @param costs Danh sách chi phí bổ sung
   */
  onAdditionalCostsChanged(costs: ImportAdditionalCost[]): void {
    this.additionalCosts = costs;

    // Tính toán lại tổng chi phí
    this.totalSupplierAdditionalCost = costs
      .filter(cost => cost.paidToSupplier)
      .reduce((total, cost) => total + (cost.costValue?.value || 0), 0);

    this.totalOtherAdditionalCost = costs
      .filter(cost => !cost.paidToSupplier)
      .reduce((total, cost) => total + (cost.costValue?.value || 0), 0);

    this.totalAdditionalCost = this.totalSupplierAdditionalCost + this.totalOtherAdditionalCost;
  }

  /**
   * Xử lý khi thuế thay đổi
   * @param taxes Danh sách thuế
   */
  onTaxesChanged(taxes: TaxInfo[]): void {
    this.taxes = taxes;

    // Tính toán lại tổng thuế
    this.totalTax = taxes.reduce((total, tax) => total + tax.amount, 0);
  }

  /**
   * Xử lý khi nhấn nút lưu nháp
   * @param data Dữ liệu đã chuẩn bị để lưu
   */
  onSaveDraft(data: GoodsReceipt): void {
    // Nếu không có dữ liệu, tạo dữ liệu form để lưu
    const formData = data || this.getFormData();
    formData.status = 'draft';

    // Log dữ liệu vào console
    console.log('Draft saved:', formData);

    // Gọi service để lưu
    this.goodsReceiptService.createGoodsReceipt(formData).subscribe(result => {
      console.log('Draft saved result:', result);
    });
  }

  /**
   * Xử lý khi nhấn nút hoàn thành
   * @param data Dữ liệu đã chuẩn bị để hoàn thành
   */
  onComplete(data: GoodsReceipt): void {
    // Nếu không có dữ liệu, tạo dữ liệu form để lưu
    const formData = data || this.getFormData();
    formData.status = 'completed';

    // Log dữ liệu vào console
    console.log('Receipt completed:', formData);

    // Gọi service để lưu
    this.goodsReceiptService.createGoodsReceipt(formData).subscribe(result => {
      console.log('Receipt completed result:', result);
    });
  }

  /**
   * Lấy dữ liệu form để lưu
   */
  getFormData(): GoodsReceipt {
    // Tạo một đối tượng GoodsReceipt mới với các giá trị mặc định cần thiết
    const result = {
      _id: '', // ID sẽ được tạo bởi server
      companyId: '',
      storeId: '',
      branchId: '',
      supplier: {
        _id: '',
        name: ''
      },
      items: this.productItems,
      summary: {
        subTotal: this.subTotal,
        totalDiscount: this.totalDiscount,
        totalSupplierAdditionalCost: this.totalSupplierAdditionalCost,
        totalNonSupplierAdditionalCost: this.totalOtherAdditionalCost,
        totalAdditionalCost: this.totalAdditionalCost,
        totalTax: this.totalTax,
        total: this.subTotal - this.totalDiscount + this.totalAdditionalCost + this.totalTax,
        totalQuantity: this.productItems.reduce((sum, item) => sum + item.quantityReceived, 0),
        totalItems: this.productItems.length
      },
      status: 'draft' as 'draft' | 'received' | 'checked' | 'completed' | 'cancelled',
      createdBy: {
        _id: '',
        name: ''
      },
      receivedAt: new Date()
    } as GoodsReceipt;

    // Merge các dữ liệu từ các component con
    if (this.basicInfo) {
      Object.assign(result, this.basicInfo);
    }

    if (this.qualityCheckInfo && this.qualityCheckInfo.status) {
      result.qualityCheck = this.qualityCheckInfo as QualityCheck;
    }

    if (this.transportInfo) {
      result.transportInfo = this.transportInfo as TransportInfo;
    }

    if (this.financialInfo) {
      Object.assign(result, this.financialInfo);
    }

    if (this.additionalCosts && this.additionalCosts.length > 0) {
      result.additionalCosts = this.additionalCosts;
    }

    if (this.taxes && this.taxes.length > 0) {
      result.taxes = this.taxes;
    }

    return result;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
