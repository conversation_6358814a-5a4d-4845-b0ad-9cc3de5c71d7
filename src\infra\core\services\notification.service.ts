import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';

/**
 * Service để hiển thị thông báo cho người dùng
 */
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  /**
   * Constructor
   * @param snackBar MatSnackBar để hiển thị thông báo
   * @param translateService TranslateService để dịch thông báo
   */
  constructor(
    private snackBar: MatSnackBar,
    private translateService: TranslateService
  ) { }

  /**
   * Hiển thị thông báo thành công
   * @param message Nội dung thông báo
   * @param duration Thời gian hiển thị (ms)
   */
  success(message: string, duration: number = 3000): void {
    this.showNotification(message, 'success', duration);
  }

  /**
   * Hiển thị thông báo lỗi
   * @param message Nội dung thông báo
   * @param duration Thời gian hiển thị (ms)
   */
  error(message: string, duration: number = 5000): void {
    this.showNotification(message, 'error', duration);
  }

  /**
   * Hiển thị thông báo cảnh báo
   * @param message Nội dung thông báo
   * @param duration Thời gian hiển thị (ms)
   */
  warning(message: string, duration: number = 4000): void {
    this.showNotification(message, 'warning', duration);
  }

  /**
   * Hiển thị thông báo thông tin
   * @param message Nội dung thông báo
   * @param duration Thời gian hiển thị (ms)
   */
  info(message: string, duration: number = 3000): void {
    this.showNotification(message, 'info', duration);
  }

  /**
   * Hiển thị thông báo với loại và thời gian cụ thể
   * @param message Nội dung thông báo
   * @param type Loại thông báo (success, error, warning, info)
   * @param duration Thời gian hiển thị (ms)
   */
  private showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info', duration: number): void {
    // Dịch tiêu đề thông báo
    const title = this.translateService.instant(`NOTIFICATION.${type.toUpperCase()}`);
    
    // Hiển thị thông báo
    this.snackBar.open(`${title}: ${message}`, 'X', {
      duration: duration,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: [`notification-${type}`]
    });
  }
}
