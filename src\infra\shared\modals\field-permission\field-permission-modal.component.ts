import { ChangeDetectionStrategy, Component, Inject, Optional, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatTableModule } from '@angular/material/table';
import { Profile } from '@shared/models/view/field-permission.model';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface FieldPermissionModalData {
  fieldName: string;
  profiles: Profile[];
}

/**
 * <PERSON><PERSON><PERSON> dữ liệu trả về từ modal
 */
export type FieldPermissionModalResult = Profile[] | undefined;

/**
 * Component modal thiết lập quyền truy cập field
 */
@Component({
  selector: 'app-field-permission-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatRadioModule,
    MatTableModule,
    TranslateModule
  ],
  templateUrl: './field-permission-modal.component.html',
  styleUrls: ['./field-permission-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldPermissionModalComponent {
  /**
   * Dữ liệu đầu vào cho modal
   */
  data: FieldPermissionModalData;

  /**
   * Danh sách profiles với quyền truy cập (reactive signal)
   */
  profiles = signal<Profile[]>([]);

  /**
   * Tên field để hiển thị trong header
   */
  fieldName = signal<string>('');

  /**
   * Các cột hiển thị trong table
   */
  displayedColumns: string[] = ['profileName', 'readWrite', 'readOnly', 'dontShow'];

  constructor(
    @Optional() private dialogRef?: MatDialogRef<FieldPermissionModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<FieldPermissionModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: FieldPermissionModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: FieldPermissionModalData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      fieldName: '',
      profiles: []
    };

    // Khởi tạo signals với dữ liệu đầu vào
    this.fieldName.set(this.data.fieldName);
    // Tạo bản sao của profiles để tránh mutate dữ liệu gốc
    this.profiles.set([...this.data.profiles]);
  }

  /**
   * Xử lý khi thay đổi quyền cho một profile
   * @param profileId ID của profile
   * @param permission Quyền mới
   */
  onPermissionChange(profileId: string, permission: 'read_write' | 'read' | 'none'): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile =>
      profile._id === profileId
        ? { ...profile, permission }
        : profile
    );
    this.profiles.set(updatedProfiles);
  }

  /**
   * Đặt quyền "read_write" cho tất cả profiles
   */
  setAllReadWrite(): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile => ({
      ...profile,
      permission: 'read_write' as const
    }));
    this.profiles.set(updatedProfiles);
  }

  /**
   * Đặt quyền "read" cho tất cả profiles
   */
  setAllReadOnly(): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile => ({
      ...profile,
      permission: 'read' as const
    }));
    this.profiles.set(updatedProfiles);
  }

  /**
   * Đặt quyền "none" cho tất cả profiles
   */
  setAllDontShow(): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile => ({
      ...profile,
      permission: 'none' as const
    }));
    this.profiles.set(updatedProfiles);
  }

  /**
   * Xử lý khi nhấn nút Save
   */
  onSave(): void {
    this.close(this.profiles());
  }

  /**
   * Xử lý khi nhấn nút Cancel
   */
  onCancel(): void {
    this.close(undefined);
  }

  /**
   * TrackBy function cho ngFor để tối ưu hiệu suất
   * @param index Index của item
   * @param profile Profile object
   * @returns ID của profile
   */
  trackByProfileId(index: number, profile: Profile): string {
    return profile._id;
  }

  /**
   * Đóng modal
   * @param result Kết quả trả về
   */
  private close(result: Profile[] | undefined): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}