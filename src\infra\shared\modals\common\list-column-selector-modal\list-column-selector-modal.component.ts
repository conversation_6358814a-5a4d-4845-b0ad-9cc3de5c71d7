import { Component, EventEmitter, Input, Output, signal, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, ViewEncapsulation, Optional, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Column } from '../../../models/view/list-layout.model';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { TranslateModule } from '@ngx-translate/core';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface ListColumnSelectorModalData {
  allColumns: Column[];
  regularColumns: string[];
  pinnedColumns: string[];
  maxPinnedColumns: number
}

/**
 * Kiểu dữ liệu trả về từ modal
 */
export type ListColumnSelectorModalResult = {
  regularColumns: string[];
  pinnedColumns: string[];
};

@Component({
  selector: 'app-list-column-selector',
  standalone: true,
  imports: [
    CommonModule,
    DragDropModule,
    MatCheckboxModule,
    MatCardModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatDialogModule,
    TranslateModule
  ],
  templateUrl: './list-column-selector-modal.component.html',
  styleUrls: ['./list-column-selector-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ListColumnSelectorModalComponent {
  data!: ListColumnSelectorModalData;

  allColumns = signal<Column[]>([]);
  selectedColumns = signal<string[]>([]);
  pinnedColumns = signal<string[]>([]);
  regularColumns = signal<string[]>([]);
  private maxPinnedColumns: number;

  constructor(
    @Optional() private dialogRef?: MatDialogRef<ListColumnSelectorModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<ListColumnSelectorModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: ListColumnSelectorModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: ListColumnSelectorModalData
  ) {
    this.data = (this.dialogData || this.bottomSheetData) as ListColumnSelectorModalData;
    this.selectedColumns.set([
      ...(this.data.regularColumns || []),
      ...(this.data.pinnedColumns || []),
    ]);
    this.allColumns.set([...(this.data.allColumns || [])]);
    this.pinnedColumns.set([...(this.data.pinnedColumns || [])]);
    this.regularColumns.set([...(this.data.regularColumns || [])]);
    this.maxPinnedColumns = this.data.maxPinnedColumns;

    if(!this.maxPinnedColumns) {
      this.maxPinnedColumns = 3;
    }
  }


  toggleColumn(key: string) {
    const selected = [...this.selectedColumns()];

    if (selected.includes(key)) {
      this.selectedColumns.set(selected.filter(col => col !== key));
      if (this.pinnedColumns().includes(key)) {
        this.pinnedColumns.set(this.pinnedColumns().filter(col => col !== key));
      } else {
        this.regularColumns.set(this.regularColumns().filter(col => col !== key));
      }
    } else {
      this.selectedColumns.set([...selected, key]);
      if (!this.regularColumns().includes(key)) {
        this.regularColumns.set([...this.regularColumns(), key]);
      }
    }
  }

  togglePin(key: string) {
    const pinned = [...this.pinnedColumns()];
    const regular = [...this.regularColumns()];

    if (pinned.includes(key)) {
      // Bỏ pin: chuyển từ pinned sang regular
      this.pinnedColumns.set(pinned.filter(col => col !== key));
      this.regularColumns.set([...regular, key]);
    } else {
      // Kiểm tra giới hạn maxPinnedColumns
      if (this.maxPinnedColumns !== undefined && pinned.length >= this.maxPinnedColumns) {
        console.warn(`Cannot pin column "${key}". Maximum pinned columns (${this.maxPinnedColumns}) reached.`);
        return;
      }
      // Pin: chuyển từ regular sang pinned
      if (!this.selectedColumns().includes(key)) {
        this.selectedColumns.set([...this.selectedColumns(), key]);
      }
      this.pinnedColumns.set([...pinned, key]);
      this.regularColumns.set(regular.filter(col => col !== key));
    }
  }

  drop(event: CdkDragDrop<string[]>) {
    const previousContainer = event.previousContainer;
    const currentContainer = event.container;

    if (previousContainer === currentContainer) {
      // Kéo thả trong cùng danh sách
      const items = currentContainer.id === 'pinnedList' ? [...this.pinnedColumns()] : [...this.regularColumns()];
      moveItemInArray(items, event.previousIndex, event.currentIndex);
      if (currentContainer.id === 'pinnedList') {
        this.pinnedColumns.set(items);
      } else {
        this.regularColumns.set(items);
      }
    } else {
      // Kiểm tra giới hạn maxPinnedColumns khi kéo sang pinnedList
      if (currentContainer.id === 'pinnedList' && this.maxPinnedColumns !== undefined && this.pinnedColumns().length >= this.maxPinnedColumns) {
        console.warn(`Cannot move column to Fixed Columns. Maximum pinned columns (${this.maxPinnedColumns}) reached.`);
        return;
      }

      const previousItems = previousContainer.id === 'pinnedList' ? [...this.pinnedColumns()] : [...this.regularColumns()];
      const currentItems = currentContainer.id === 'pinnedList' ? [...this.pinnedColumns()] : [...this.regularColumns()];

      transferArrayItem(
        previousItems,
        currentItems,
        event.previousIndex,
        event.currentIndex
      );

      if (previousContainer.id === 'pinnedList') {
        this.pinnedColumns.set(previousItems);
        this.regularColumns.set(currentItems);
        const movedItem = currentItems[event.currentIndex];
        if (this.pinnedColumns().includes(movedItem)) {
          this.pinnedColumns.set(this.pinnedColumns().filter(col => col !== movedItem));
        }
      } else {
        this.regularColumns.set(previousItems);
        this.pinnedColumns.set(currentItems);
        const movedItem = currentItems[event.currentIndex];
        if (!this.pinnedColumns().includes(movedItem)) {
          this.pinnedColumns.set([...this.pinnedColumns(), movedItem]);
        }
      }
    }
  }

  getColumnLabel(key: string): string {
    const column = this.data.allColumns.find(c => c.key === key);
    return column?.label || key;
  }

  isPinDisabled(key: string): boolean {
    return this.maxPinnedColumns !== undefined && this.pinnedColumns().length >= this.maxPinnedColumns && !this.pinnedColumns().includes(key);
  }

  trackByColumn(index: number, col: string): string {
    return col;
  }

  trackByColumn2(index: number, col: Column): string {
    return col.key;
  }

  /**
   * Xử lý khi nhấn nút Hủy
   */
  onCancel(): void {
    this.close(true);
  }

  /**
   * Xử lý khi nhấn nút Xác nhận
   */
  onConfirm(): void {
    this.close(false);
  }

  /**
   * Đóng modal
   */
  private close(isCancel: boolean): void {
    let result: ListColumnSelectorModalResult | null = null;
    if(!isCancel) {
      result = {
        regularColumns: this.regularColumns(),
        pinnedColumns: this.pinnedColumns()
      }
    }
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
