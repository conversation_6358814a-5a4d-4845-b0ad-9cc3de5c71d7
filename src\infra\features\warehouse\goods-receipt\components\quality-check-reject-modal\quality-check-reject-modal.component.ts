import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetModule, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { GoodsReceiptItem } from '../../models/api/goods-receipt.dto';

/**
 * Interface cho sản phẩm bị từ chối trong kiểm tra chất lượng
 */
export interface RejectedItem {
  _id: string; // ID của GoodsReceiptItem
  quantity: number; // Số lượng bị từ chối
  reason: string; // Lý do từ chối
}

/**
 * Interface cho dữ liệu truyền vào modal
 */
export interface QualityCheckRejectModalData {
  items: GoodsReceiptItem[]; // Danh sách sản phẩm
  rejectedItem?: RejectedItem; // Sản phẩm bị từ chối (nếu đang chỉnh sửa)
}

/**
 * Component modal quản lý thông tin sản phẩm bị từ chối trong kiểm tra chất lượng
 */
@Component({
  selector: 'app-quality-check-reject-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    TranslateModule,
  ],
  templateUrl: './quality-check-reject-modal.component.html',
  styleUrls: ['./quality-check-reject-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class QualityCheckRejectModalComponent implements OnInit {
  // Inject các service cần thiết
  private dialogRef?: MatDialogRef<QualityCheckRejectModalComponent>;
  private bottomSheetRef?: MatBottomSheetRef<QualityCheckRejectModalComponent>;
  private data: QualityCheckRejectModalData;
  private fb = inject(FormBuilder);

  // Form group quản lý dữ liệu sản phẩm bị từ chối
  rejectForm!: FormGroup;

  // Danh sách sản phẩm để hiển thị trong select
  items: GoodsReceiptItem[] = [];

  // Biến để theo dõi nếu đang chỉnh sửa hay tạo mới
  isEditMode = false;

  // Số lượng nhận của sản phẩm được chọn
  selectedItemQuantity = 0;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: QualityCheckRejectModalData | null | undefined,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: QualityCheckRejectModalData | null | undefined,
    @Optional() dialogRefInject?: MatDialogRef<QualityCheckRejectModalComponent>,
    @Optional() bottomSheetRefInject?: MatBottomSheetRef<QualityCheckRejectModalComponent>
  ) {
    this.dialogRef = dialogRefInject;
    this.bottomSheetRef = bottomSheetRefInject;
    this.data = this.dialogData || this.bottomSheetData || { items: [] };
  }

  ngOnInit(): void {
    // Lấy danh sách sản phẩm
    this.items = this.data.items;

    // Xác định chế độ chỉnh sửa hay tạo mới
    this.isEditMode = !!this.data.rejectedItem;

    // Khởi tạo form với các validators
    this.initForm();

    // Nếu có dữ liệu được truyền vào, cập nhật form
    if (this.isEditMode && this.data.rejectedItem) {
      this.rejectForm.patchValue({
        _id: this.data.rejectedItem._id,
        quantity: this.data.rejectedItem.quantity,
        reason: this.data.rejectedItem.reason
      });

      // Tìm sản phẩm tương ứng để lấy số lượng
      const selectedItem = this.items.find(item => item._id === this.data.rejectedItem?._id);
      if (selectedItem) {
        this.selectedItemQuantity = selectedItem.quantityReceived;

        // Cập nhật validator cho quantity
        this.updateQuantityValidator();
      }

      // Trong chế độ chỉnh sửa, không cho phép thay đổi sản phẩm
      this.rejectForm.get('_id')?.disable();
    }

    // Lắng nghe sự thay đổi của _id để cập nhật selectedItemQuantity
    this.rejectForm.get('_id')?.valueChanges.subscribe(_id => {
      const selectedItem = this.items.find(item => item._id === _id);
      if (selectedItem) {
        this.selectedItemQuantity = selectedItem.quantityReceived;

        // Cập nhật validator cho quantity
        this.updateQuantityValidator();
      } else {
        this.selectedItemQuantity = 0;
      }
    });
  }

  /**
   * Khởi tạo form với các validators
   */
  private initForm(): void {
    this.rejectForm = this.fb.group({
      _id: ['', Validators.required],
      quantity: [null, [Validators.required, Validators.min(1)]],
      reason: ['', [Validators.required, Validators.maxLength(500)]]
    });
  }

  /**
   * Cập nhật validator cho trường quantity dựa trên selectedItemQuantity
   */
  private updateQuantityValidator(): void {
    this.rejectForm.get('quantity')?.setValidators([
      Validators.required,
      Validators.min(1),
      Validators.max(this.selectedItemQuantity)
    ]);
    this.rejectForm.get('quantity')?.updateValueAndValidity();
  }

  /**
   * Đóng modal và trả về dữ liệu nếu form hợp lệ
   */
  save(): void {
    if (this.rejectForm.valid) {
      const formValue = this.rejectForm.getRawValue(); // getRawValue để lấy cả giá trị của các control đã bị disable

      const rejectedItem: RejectedItem = {
        _id: formValue._id,
        quantity: formValue.quantity,
        reason: formValue.reason
      };

      if (this.dialogRef) {
        this.dialogRef.close(rejectedItem);
      } else if (this.bottomSheetRef) {
        this.bottomSheetRef.dismiss(rejectedItem);
      }
    } else {
      // Đánh dấu tất cả các trường là đã touched để hiển thị lỗi nếu có
      this.rejectForm.markAllAsTouched();
    }
  }

  /**
   * Đóng modal mà không lưu dữ liệu
   */
  cancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  /**
   * Lấy tên hiển thị của sản phẩm (tên + SKU)
   */
  getProductDisplayName(item: GoodsReceiptItem): string {
    return `${item.product.name} (SKU: ${item.product.sku})`;
  }
}
