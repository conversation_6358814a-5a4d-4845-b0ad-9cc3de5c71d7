import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnInit, OnDestroy, AfterViewInit, Output, TemplateRef, ViewChild, ViewContainerRef, inject, signal, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatBottomSheet, MatBottomSheetRef, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { Overlay, OverlayRef, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { Subscription } from 'rxjs';

import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { ProductUnit } from 'salehub_shared_contracts';
import { OrderItemService } from './order-item.service';
import { OrderItemVariantUnitSelectionModalService } from '@/shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { OrderItemModifierModalService } from '@/shared/modals/sales/order/order-item-modifier-modal/order-item-modifier-modal.service';
import { PromotionModalService } from '@/features/sales/order-form/components/modals/promotion-modal';
import { NoteModalService } from '@/features/sales/order-form/components/modals/note-modal';
import { OrderProductPickerModalService } from '@/shared/modals/product/order-product-picker-modal';
import { ProductList } from 'salehub_shared_contracts/requests/shared/product';
import { mockProductModifierGroupList } from '@/mock/shared/product.mock';

/**
 * Component hiển thị thông tin đơn hàng
 */
@Component({
  selector: 'app-order-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatTooltipModule,
    MatMenuModule,
    MatBottomSheetModule,
    OverlayModule,
    TranslateModule
    // OrderProductPickerComponent
  ],
  templateUrl: './order-details.component.html',
  styleUrls: ['./order-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class OrderDetailsComponent implements OnInit, OnDestroy, AfterViewInit {
  /**
   * Service xử lý thông tin đơn hàng
   */
  private orderItemService = inject(OrderItemService);
  private overlay = inject(Overlay);
  private viewContainerRef = inject(ViewContainerRef);
  private bottomSheet = inject(MatBottomSheet);
  private dialog = inject(MatDialog);
  private productModifierFormModalService = inject(OrderItemModifierModalService);
  private orderItemVariantUnitSelectionModalService = inject(OrderItemVariantUnitSelectionModalService);
  private noteModalService = inject(NoteModalService);
  private promotionModalService = inject(PromotionModalService);
  private orderProductPickerModalService = inject(OrderProductPickerModalService);

  /**
   * Danh sách sản phẩm trong đơn hàng
   */
  private _items = signal<OrderItemBaseDetails[]>([]);

  @Input() set items(value: OrderItemBaseDetails[]) {
    this._items.set(value || []);
  }

  get items(): OrderItemBaseDetails[] {
    return this._items();
  }

  /**
   * Sự kiện khi danh sách sản phẩm thay đổi
   */
  @Output() itemsUpdated = new EventEmitter<OrderItemBaseDetails[]>();

  /**
   * Từ khóa tìm kiếm sản phẩm
   */
  searchKeyword = signal('');

  /**
   * Trạng thái mở rộng của panel
   */
  panelExpanded = true;

  /**
   * Danh sách sản phẩm từ mock data
   */
  productList: ProductList = [];

  /**
   * Reference đến ô input tìm kiếm
   */
  @ViewChild('searchInput') searchInputRef!: ElementRef;

  /**
   * Reference đến template của product selection
   */
  @ViewChild('productSelectionTpl') productSelectionTpl!: TemplateRef<any>;

  /**
   * Overlay reference
   */
  private overlayRef: OverlayRef | null = null;

  /**
   * Subscription cho các sự kiện
   */
  private subscription = new Subscription();

  /**
   * Kích thước menu động
   */
  menuWidth: number = 400;
  menuHeight: number = 400;

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    // Lấy danh sách sản phẩm từ service
    this.productList = this.orderItemService.getProductList();
  }

  /**
   * Thiết lập kích thước menu sau khi view được khởi tạo
   */
  ngAfterViewInit(): void {
    // Cập nhật kích thước menu dựa trên input width
    if (this.searchInputRef?.nativeElement) {
      const formField = this.searchInputRef.nativeElement.closest('mat-form-field');
      if (formField) {
        this.menuWidth = formField.clientWidth;
      }
    }

    // Không đăng ký sự kiện click window để giữ menu luôn hiển thị
  }

  /**
   * Dọn dẹp tài nguyên khi component bị hủy
   */
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.overlayRef) {
      this.overlayRef.dispose();
    }
  }

  /**
   * Thêm sản phẩm vào đơn hàng
   * @param items Danh sách sản phẩm cần thêm
   */
  addItems(items: OrderItemBaseDetails[]): void {
    // Thêm sản phẩm mới vào danh sách
    const newItems = [...this._items(), ...items];
    this._items.set(newItems);

    // Phát sự kiện cập nhật danh sách sản phẩm
    this.itemsUpdated.emit(newItems);

    // Đóng menu
    this.closeMenu();

    // Xóa từ khóa tìm kiếm
    this.searchKeyword.set('');
  }

  /**
   * Xóa sản phẩm khỏi đơn hàng
   * @param index Vị trí sản phẩm cần xóa
   */
  removeItem(index: number): void {
    const currentItems = [...this._items()];
    currentItems.splice(index, 1);
    this._items.set(currentItems);
    this.itemsUpdated.emit(currentItems);
  }

  /**
   * Cập nhật số lượng sản phẩm
   * @param index Vị trí sản phẩm cần cập nhật
   * @param quantity Số lượng mới
   */
  updateQuantity(index: number, quantity: number): void {
    if (quantity <= 0) return;

    const currentItems = [...this._items()];
    currentItems[index].quantity = quantity;
    this._items.set(currentItems);
    this.itemsUpdated.emit(currentItems);
  }

  /**
   * Tăng số lượng sản phẩm
   * @param index Vị trí sản phẩm cần tăng số lượng
   */
  increaseQuantity(index: number): void {
    const currentItems = [...this._items()];
    currentItems[index].quantity += 1;
    this._items.set(currentItems);
    this.itemsUpdated.emit(currentItems);
  }

  /**
   * Giảm số lượng sản phẩm
   * @param index Vị trí sản phẩm cần giảm số lượng
   */
  decreaseQuantity(index: number): void {
    const currentItems = [...this._items()];
    if (currentItems[index].quantity > 1) {
      currentItems[index].quantity -= 1;
      this._items.set(currentItems);
      this.itemsUpdated.emit(currentItems);
    }
  }

  /**
   * Cập nhật giá sản phẩm
   * @param index Vị trí sản phẩm cần cập nhật
   * @param price Giá mới
   */
  updatePrice(index: number, price: number): void {
    if (price < 0) return;

    const currentItems = [...this._items()];
    currentItems[index].product!.userOverride!.price = price;
    this._items.set(currentItems);
    this.itemsUpdated.emit(currentItems);
  }

  /**
   * Xử lý khi từ khóa tìm kiếm thay đổi
   * @param keyword Từ khóa tìm kiếm mới
   */
  onSearchKeywordChange(keyword: string): void {
    this.searchKeyword.set(keyword);
  }

  /**
   * Xóa từ khóa tìm kiếm
   */
  clearSearch(): void {
    this.searchKeyword.set('');
    if (this.searchInputRef) {
      this.searchInputRef.nativeElement.focus();
    }
  }

  /**
   * Tính thành tiền cho một sản phẩm
   * @param item Thông tin sản phẩm
   * @returns Thành tiền
   */
  calculateItemTotal(item: OrderItemBaseDetails): number {
    return this.orderItemService.calculateItemTotal(item);
  }

  /**
   * Mở modal để chọn variant cho sản phẩm
   * @param itemIndex Vị trí sản phẩm cần cập nhật variant
   */
  openVariantSelector(itemIndex: number): void {
    const currentItem = this.items[itemIndex];

    this.orderItemVariantUnitSelectionModalService.open({
      variants: this.orderItemService.getProductVariants(currentItem.product?.productId),
      currentValue: {
        variant: currentItem.product?.variant || { variantId: '', attributes: [] },
        unit: currentItem.product?.unit || { unitName: '', conversionRate: 1, isBaseUnit: true, price: 0 }
      },
      units: []
    }).then(result => {
      if (result) {
        // Cập nhật variant cho sản phẩm
        const currentItems = [...this._items()];
        currentItems[itemIndex].product!.variant = result.variant;
        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn variant:', error);
    });
  }

  /**
   * Mở modal để chọn đơn vị tính cho sản phẩm
   * @param itemIndex Vị trí sản phẩm cần cập nhật đơn vị tính
   */
  openUnitSelector(itemIndex: number): void {
    const currentItem = this.items[itemIndex];

    this.orderItemVariantUnitSelectionModalService.open({
      variants: [],
      currentValue: {
        variant: currentItem.product?.variant || { variantId: '', attributes: [] },
        unit: currentItem.product?.unit || { unitName: '', conversionRate: 1, isBaseUnit: true, price: 0 }
      },
      units: this.orderItemService.getProductUnits(currentItem.product?.productId)
    }).then(result => {
      if (result) {
        // Cập nhật đơn vị tính cho sản phẩm
        const currentItems = [...this._items()];
        currentItems[itemIndex].product!.unit = result.unit;
        if(result.unit?.price) {
          currentItems[itemIndex].product!.userOverride!.price = result.unit.price;
        }
        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn đơn vị tính:', error);
    });
  }

  /**
   * Mở menu chọn sản phẩm khi focus vào ô tìm kiếm
   */
  async openProductMenu(): Promise<void> {
    // if (this.overlayRef) {
    //   return;
    // }

    // const config = new OverlayConfig({
    //   hasBackdrop: false,
    //   scrollStrategy: this.overlay.scrollStrategies.reposition(),
    //   width: `${this.menuWidth}px`,
    //   maxHeight: `${this.menuHeight}px`
    // });

    // const positionStrategy = this.overlay.position()
    //   .flexibleConnectedTo(this.searchInputRef)
    //   .withPositions([
    //     {
    //       originX: 'start',
    //       originY: 'bottom',
    //       overlayX: 'start',
    //       overlayY: 'top',
    //       offsetY: 8
    //     },
    //     {
    //       originX: 'start',
    //       originY: 'top',
    //       overlayX: 'start',
    //       overlayY: 'bottom',
    //       offsetY: -8
    //     }
    //   ]);

    // config.positionStrategy = positionStrategy;

    // this.overlayRef = this.overlay.create(config);
    // const portal = new TemplatePortal(this.productSelectionTpl, this.viewContainerRef);
    // this.overlayRef.attach(portal);

    try {
      const result = await this.orderProductPickerModalService.open({
        list: this.productList,
        data: this.items
      });

      if (result) {
        this.addItems(result);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn sản phẩm:', error);
    }
  }

  /**
   * Xử lý sự kiện khi click vào input
   */
  onInputClick(event: MouseEvent): void {
    event.stopPropagation();
    this.openProductMenu();
  }

  /**
   * Đóng menu chọn sản phẩm
   */
  closeMenu(): void {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }

  /**
   * Mở modal để chọn modifiers cho sản phẩm
   * @param itemIndex Vị trí sản phẩm cần cập nhật modifiers
   */
  async openModifiersSheet(itemIndex: number): Promise<void> {
    try {
      const currentItem = this.items[itemIndex];

      // Mở modal chọn modifier sử dụng service
      const result = await this.productModifierFormModalService.open({
        list: mockProductModifierGroupList,
        data: currentItem
      });

      if (result) {
        // Cập nhật modifierGroups cho sản phẩm
        const currentItems = [...this._items()];
        currentItems[itemIndex].modifierGroups = result;
        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn modifier:', error);
    }
  }

  /**
   * Mở modal để thêm khuyến mại cho sản phẩm
   * @param itemIndex Vị trí sản phẩm cần thêm khuyến mại
   */
  async openPromotionDialog(itemIndex: number): Promise<void> {
    const currentItem = this.items[itemIndex];
    const totalAmount = currentItem.quantity * (currentItem.product?.userOverride?.price || currentItem.product?.price || 0);

    try {
      const result = await this.promotionModalService.open({
        totalAmount,
        discountType: currentItem.discount ? 'amount' : undefined,
        discountValue: currentItem.discount,
        promotionName: currentItem.discountInfo?.length ? currentItem.discountInfo[0].name : '',
        finalAmount: totalAmount - (currentItem.discount || 0)
      });

      if (result) {
        // Cập nhật giảm giá cho sản phẩm
        const currentItems = [...this._items()];
        currentItems[itemIndex].discount = result.discountValue;

        // Nếu chưa có discountInfo, khởi tạo mảng rỗng
        if (!currentItems[itemIndex].discountInfo) {
          currentItems[itemIndex].discountInfo = [];
        }

        // Thêm thông tin khuyến mãi mới
        currentItems[itemIndex].discountInfo = [{
          amount: result.discountValue,
          name: result.promotionName
        }];

        // Đánh dấu sản phẩm có khuyến mãi
        currentItems[itemIndex].hasPromotion = true;
        this.orderItemService.calculateItemTotal(currentItems[itemIndex]);

        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal khuyến mại:', error);
    }
  }

  /**
   * Mở modal để thêm ghi chú cho sản phẩm
   * @param itemIndex Vị trí sản phẩm cần thêm ghi chú
   */
  async openNoteDialog(itemIndex: number): Promise<void> {
    const currentItem = this.items[itemIndex];

    try {
      const result = await this.noteModalService.open({
        internalNote: currentItem.internalNote || '',
        note: currentItem.note || ''
      });

      if (result) {
        // Cập nhật ghi chú cho sản phẩm
        const currentItems = [...this._items()];
        currentItems[itemIndex].internalNote = result.internalNote;
        currentItems[itemIndex].note = result.note;
        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú:', error);
    }
  }

  /**
   * Cập nhật số lượng sản phẩm modifier
   * @param itemIndex Vị trí sản phẩm cha
   * @param groupId ID của nhóm modifier
   * @param modifierIndex Vị trí modifier cần cập nhật
   * @param quantity Số lượng mới
   */
  updateModifierQuantity(itemIndex: number, groupId: string, modifierIndex: number, quantity: number): void {
    if (quantity <= 0) return;

    const currentItems = [...this._items()];
    const item = currentItems[itemIndex];
    const modifierGroup = item.modifierGroups?.find(group => group._id === groupId);

    if (modifierGroup && modifierGroup.modifiers && modifierGroup.modifiers[modifierIndex]) {
      modifierGroup.modifiers[modifierIndex].quantity = quantity;
      this._items.set(currentItems);
      this.itemsUpdated.emit(currentItems);
    }
  }

  /**
   * Tăng số lượng sản phẩm modifier
   * @param itemIndex Vị trí sản phẩm cha
   * @param groupId ID của nhóm modifier
   * @param modifierIndex Vị trí modifier cần tăng số lượng
   */
  increaseModifierQuantity(itemIndex: number, groupId: string, modifierIndex: number): void {
    const currentItems = [...this._items()];
    const item = currentItems[itemIndex];
    const modifierGroup = item.modifierGroups?.find(group => group._id === groupId);

    if (modifierGroup && modifierGroup.modifiers && modifierGroup.modifiers[modifierIndex]) {
      modifierGroup.modifiers[modifierIndex].quantity += 1;
      this._items.set(currentItems);
      this.itemsUpdated.emit(currentItems);
    }
  }

  /**
   * Giảm số lượng sản phẩm modifier
   * @param itemIndex Vị trí sản phẩm cha
   * @param groupId ID của nhóm modifier
   * @param modifierIndex Vị trí modifier cần giảm số lượng
   */
  decreaseModifierQuantity(itemIndex: number, groupId: string, modifierIndex: number): void {
    const currentItems = [...this._items()];
    const item = currentItems[itemIndex];
    const modifierGroup = item.modifierGroups?.find(group => group._id === groupId);

    if (modifierGroup && modifierGroup.modifiers && modifierGroup.modifiers[modifierIndex]) {
      const modifier = modifierGroup.modifiers[modifierIndex];
      if (modifier.quantity > 1) {
        modifier.quantity -= 1;
        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    }
  }

  /**
   * Xóa modifier khỏi sản phẩm
   * @param itemIndex Vị trí sản phẩm cha
   * @param groupId ID của modifier group
   * @param modifierIndex Vị trí của modifier
   */
  removeModifier(itemIndex: number, groupId: string, modifierIndex: number): void {
    const currentItems = [...this._items()];
    const item = currentItems[itemIndex];

    if (item.modifierGroups) {
      const groupIndex = item.modifierGroups.findIndex(g => g._id === groupId);
      if (groupIndex !== -1) {
        // Xóa modifier khỏi danh sách
        item.modifierGroups[groupIndex].modifiers.splice(modifierIndex, 1);

        // Nếu không còn modifier nào trong group, xóa luôn group
        if (item.modifierGroups[groupIndex].modifiers.length === 0) {
          item.modifierGroups.splice(groupIndex, 1);
        }

        // Nếu không còn group nào, đặt modifierGroups về undefined hoặc mảng rỗng
        if (item.modifierGroups.length === 0) {
          item.modifierGroups = [];
        }

        this._items.set(currentItems);
        this.itemsUpdated.emit(currentItems);
      }
    }
  }
}
