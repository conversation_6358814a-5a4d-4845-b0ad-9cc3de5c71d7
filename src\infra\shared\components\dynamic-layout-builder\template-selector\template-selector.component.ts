import { Component, EventEmitter, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { Section } from '@shared/models/view/dynamic-layout-builder.model';

/**
 * Interface cho template ngành hàng
 */
export interface IndustryTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'fashion' | 'beauty' | 'food' | 'electronics' | 'general';
  sections: Section[];
  tags: string[];
}

/**
 * Component để chọn template ngành hàng cho Dynamic Layout Builder
 * Hiển thị dropdown và preview các template có sẵn
 */
@Component({
  selector: 'app-template-selector',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatCardModule,
    MatChipsModule,
    TranslateModule
  ],
  templateUrl: './template-selector.component.html',
  styleUrls: ['./template-selector.component.scss']
})
export class TemplateSelectorComponent {

  /**
   * Event được emit khi người dùng chọn và áp dụng template
   */
  @Output() templateApplied = new EventEmitter<IndustryTemplate>();

  /**
   * Event được emit khi người dùng preview template
   */
  @Output() templatePreviewed = new EventEmitter<IndustryTemplate>();

  /**
   * ID của template được chọn
   */
  selectedTemplateId: string = '';

  /**
   * Signal cho template được chọn
   */
  selectedTemplate = signal<IndustryTemplate | null>(null);

  /**
   * Signal cho trạng thái đang apply template
   */
  isApplying = signal<boolean>(false);

  /**
   * Signal cho danh sách templates có sẵn
   */
  availableTemplates = signal<IndustryTemplate[]>([]);

  ngOnInit(): void {
    this.loadAvailableTemplates();
  }

  /**
   * Load danh sách templates có sẵn
   */
  private loadAvailableTemplates(): void {
    const templates: IndustryTemplate[] = [
      {
        id: 'fashion-basic',
        name: 'Thời trang cơ bản',
        description: 'Template cho cửa hàng thời trang với các trường cơ bản',
        icon: 'checkroom',
        category: 'fashion',
        tags: ['Thời trang', 'Quần áo', 'Phụ kiện'],
        sections: [
          {
            id: 'personal-info',
            title: 'Thông tin cá nhân',
            fields: [
              { id: 1, type: 'text', label: 'Họ và tên', required: true, order: 1 },
              { id: 2, type: 'phone', label: 'Số điện thoại', required: true, order: 2 },
              { id: 3, type: 'email', label: 'Email', required: false, order: 3 }
            ]
          },
          {
            id: 'fashion-preferences',
            title: 'Sở thích thời trang',
            fields: [
              { id: 4, type: 'size', label: 'Size thường mặc', required: true, order: 1 },
              { id: 5, type: 'color', label: 'Màu sắc yêu thích', required: false, order: 2 },
              { id: 6, type: 'select', label: 'Phong cách', required: false, order: 3 }
            ]
          }
        ]
      },
      {
        id: 'beauty-basic',
        name: 'Mỹ phẩm cơ bản',
        description: 'Template cho cửa hàng mỹ phẩm và chăm sóc da',
        icon: 'face',
        category: 'beauty',
        tags: ['Mỹ phẩm', 'Chăm sóc da', 'Làm đẹp'],
        sections: [
          {
            id: 'personal-info',
            title: 'Thông tin cá nhân',
            fields: [
              { id: 1, type: 'text', label: 'Họ và tên', required: true, order: 1 },
              { id: 2, type: 'phone', label: 'Số điện thoại', required: true, order: 2 },
              { id: 3, type: 'date', label: 'Ngày sinh', required: false, order: 3 }
            ]
          },
          {
            id: 'skin-info',
            title: 'Thông tin da',
            fields: [
              { id: 4, type: 'select', label: 'Loại da', required: true, order: 1 },
              { id: 5, type: 'checkbox', label: 'Vấn đề da', required: false, order: 2 },
              { id: 6, type: 'textarea', label: 'Ghi chú đặc biệt', required: false, order: 3 }
            ]
          }
        ]
      },
      {
        id: 'food-basic',
        name: 'Thực phẩm cơ bản',
        description: 'Template cho cửa hàng thực phẩm và đồ uống',
        icon: 'restaurant',
        category: 'food',
        tags: ['Thực phẩm', 'Đồ uống', 'Ăn uống'],
        sections: [
          {
            id: 'personal-info',
            title: 'Thông tin cá nhân',
            fields: [
              { id: 1, type: 'text', label: 'Họ và tên', required: true, order: 1 },
              { id: 2, type: 'phone', label: 'Số điện thoại', required: true, order: 2 },
              { id: 3, type: 'text', label: 'Địa chỉ giao hàng', required: true, order: 3 }
            ]
          },
          {
            id: 'food-preferences',
            title: 'Sở thích ẩm thực',
            fields: [
              { id: 4, type: 'checkbox', label: 'Dị ứng thực phẩm', required: false, order: 1 },
              { id: 5, type: 'select', label: 'Khẩu vị', required: false, order: 2 },
              { id: 6, type: 'textarea', label: 'Ghi chú đặc biệt', required: false, order: 3 }
            ]
          }
        ]
      }
    ];

    this.availableTemplates.set(templates);
  }

  /**
   * Xử lý khi người dùng chọn template từ dropdown
   */
  onTemplateSelected(templateId: string): void {
    this.selectedTemplateId = templateId;
    const template = this.availableTemplates().find(t => t.id === templateId);
    this.selectedTemplate.set(template || null);
  }

  /**
   * Xử lý khi người dùng click vào template card
   */
  onTemplateCardClick(templateId: string): void {
    this.selectedTemplateId = templateId;
    this.onTemplateSelected(templateId);
  }

  /**
   * Xử lý khi người dùng áp dụng template
   */
  onApplyTemplate(): void {
    const template = this.selectedTemplate();
    if (template) {
      this.isApplying.set(true);

      // Simulate API call delay
      setTimeout(() => {
        this.templateApplied.emit(template);
        this.isApplying.set(false);
      }, 500);
    }
  }

  /**
   * Xử lý khi người dùng chọn và áp dụng template ngay lập tức
   */
  onSelectAndApplyTemplate(templateId: string): void {
    this.onTemplateSelected(templateId);
    setTimeout(() => {
      this.onApplyTemplate();
    }, 100);
  }

  /**
   * Xử lý khi người dùng preview template
   */
  onPreviewTemplate(): void {
    const template = this.selectedTemplate();
    if (template) {
      this.templatePreviewed.emit(template);
    }
  }

  /**
   * Lấy label cho category
   */
  getCategoryLabel(category: string): string {
    const categoryLabels: { [key: string]: string } = {
      'fashion': 'Thời trang',
      'beauty': 'Mỹ phẩm',
      'food': 'Thực phẩm',
      'electronics': 'Điện tử',
      'general': 'Tổng quát'
    };
    return categoryLabels[category] || category;
  }

  /**
   * TrackBy function cho templates
   */
  trackByTemplate(index: number, template: IndustryTemplate): string {
    return template.id;
  }

  /**
   * TrackBy function cho sections
   */
  trackBySection(index: number, section: Section): string {
    return section.id;
  }
}
