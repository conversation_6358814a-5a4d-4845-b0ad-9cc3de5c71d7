import { Component, EventEmitter, Input, OnInit, Output, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { OrderDelivery } from 'salehub_shared_contracts/entities/oms/order/order_components/order_delivery';
import { TranslateModule } from '@ngx-translate/core';
import { InputPlaceComponent } from '@shared/components/input/input-place/input-place.component';
import { ShippingService } from './shipping.service';

/**
 * Component để quản lý thông tin vận chuyển đơn hàng
 */
@Component({
  selector: 'app-shipping-info',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatSlideToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatButtonToggleModule,
    TranslateModule,
    InputPlaceComponent
  ],
  templateUrl: './shipping-info.component.html',
  styleUrls: ['./shipping-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShippingInfoComponent implements OnInit {
  /**
   * Đầu vào thông tin vận chuyển từ component cha
   */
  @Input() delivery: OrderDelivery = {
    deliveryType: 'physical'
  };

  /**
   * Đầu vào thông tin khách hàng từ component cha
   */
  @Input() customerInfo: any;

  /**
   * Sự kiện phát ra khi thông tin vận chuyển thay đổi
   */
  @Output() deliveryInfoUpdated = new EventEmitter<OrderDelivery>();

  /**
   * Danh sách địa chỉ lấy hàng
   */
  pickupAddresses: any[] = [];

  /**
   * Danh sách đơn vị vận chuyển
   */
  shippingCarriers: any[] = [];

  /**
   * Tùy chọn thử hàng
   */
  tryOnOptions = [
    { value: 'allow_view_only', label: 'SALES.ORDER_FORM.SHIPPING_INFO.TRY_ON.VIEW_ONLY' },
    { value: 'allow_try', label: 'SALES.ORDER_FORM.SHIPPING_INFO.TRY_ON.ALLOW_TRY' },
    { value: 'no_view', label: 'SALES.ORDER_FORM.SHIPPING_INFO.TRY_ON.NO_VIEW' },
    { value: 'allow_view_no_charge', label: 'SALES.ORDER_FORM.SHIPPING_INFO.TRY_ON.VIEW_NO_CHARGE' }
  ];

  /**
   * Tùy chọn thời gian lấy hàng
   */
  pickupTimeOptions = [
    { value: 'all_day', label: 'SALES.ORDER_FORM.SHIPPING_INFO.PICKUP_TIME.ALL_DAY' },
    { value: '8h-12h', label: 'SALES.ORDER_FORM.SHIPPING_INFO.PICKUP_TIME.MORNING' },
    { value: '13h-17h', label: 'SALES.ORDER_FORM.SHIPPING_INFO.PICKUP_TIME.AFTERNOON' }
  ];

  /**
   * Phí vận chuyển mặc định
   */
  defaultShippingFee = 0;

  /**
   * Constructor với dependency injection
   */
  constructor(private shippingService: ShippingService) {
    // Dependency Injection cho ShippingService
  }

  /**
   * Lifecycle hook khi component được khởi tạo
   */
  ngOnInit(): void {
    // Khởi tạo các đối tượng nếu chưa có
    this.initializeDeliveryObjects();

    // Lấy danh sách địa chỉ lấy hàng từ service
    this.pickupAddresses = this.shippingService.getPickupAddresses();

    // Lấy danh sách đơn vị vận chuyển từ service
    this.shippingService.getDeliveryCompanies().subscribe((companies: any[]) => {
      this.shippingCarriers = companies;
    });
  }

  /**
   * Khởi tạo các đối tượng trong delivery nếu chưa có
   */
  private initializeDeliveryObjects(): void {
    // Khởi tạo physicalDelivery nếu chưa có
    if (!this.delivery.physicalDelivery) {
      this.delivery.physicalDelivery = {
        distance: 0,
        fee: 0,
        carrier: undefined,
        driver: undefined,
        deliveryInfo: {
          name: this.customerInfo?.name || '',
          phoneNumber: this.customerInfo?.phoneNumber || '',
          address: this.customerInfo?.address || {},
          pickupAddress: {},
          deliveryFeeForCustomer: 0,
          returnFeeForPartner: 0,
          shippingPayer: 'customer',
          deliveryMethod: 'pickup_by_courier',
          pickupTime: 'all_day',
          returnFee: 0,
          autoSendToCarrier: true,
          tryOn: 'allow_view_only',
          dimensions: {
            height: 0,
            length: 0,
            width: 0
          },
          weight: 0
        }
      };

      // Thêm property selfTransport cho physicalDelivery
      (this.delivery.physicalDelivery as any).selfTransport = false;
    }

    // Khởi tạo digitalDelivery nếu chưa có và loại vận chuyển là digital
    if (this.delivery.deliveryType === 'digital' && !this.delivery.digitalDelivery) {
      this.delivery.digitalDelivery = {
        downloadLink: '',
        accessCode: '',
        expirationDate: undefined
      };
    }

    // Khởi tạo serviceDelivery nếu chưa có và loại vận chuyển là service
    if (this.delivery.deliveryType === 'service' && !this.delivery.serviceDelivery) {
      this.delivery.serviceDelivery = {
        appointmentTime: undefined,
        location: ''
      };
    }
  }

  /**
   * Cập nhật thông tin vận chuyển và phát ra sự kiện
   */
  updateDeliveryInfo(): void {
    // Tính phí vận chuyển dựa trên thông tin
    if (this.delivery.physicalDelivery?.deliveryInfo) {
      this.delivery.physicalDelivery.fee = this.shippingService.calculateShippingFee(
        this.delivery.physicalDelivery.deliveryInfo
      );
    }

    // Phát ra sự kiện để cập nhật đơn hàng
    this.deliveryInfoUpdated.emit(this.delivery);
  }

  /**
   * Thiết lập trạng thái tự vận chuyển
   * @param value Giá trị true/false cho tự vận chuyển
   */
  setSelfTransport(value: boolean): void {
    if (this.delivery.physicalDelivery) {
      (this.delivery.physicalDelivery as any).selfTransport = value;
    }
  }

  /**
   * Xử lý sự kiện khi chọn đơn vị vận chuyển
   * @param carrier Dữ liệu carrier từ mat-select
   */
  onCarrierSelected(carrier: any): void {
    const carrierId = carrier && carrier._id ? carrier._id : '';
    if (carrierId && this.delivery.physicalDelivery) {
      this.delivery.physicalDelivery.carrier = carrier;
      this.updateDeliveryInfo();
    }
  }

  /**
   * Xử lý khi thay đổi loại giao hàng
   * @param type Loại giao hàng
   */
  onDeliveryTypeChange(type: 'physical' | 'digital' | 'service' | 'selfPickup'): void {
    this.delivery.deliveryType = type;
    this.initializeDeliveryObjects();
    this.updateDeliveryInfo();
  }

  /**
   * Xử lý khi thông tin địa chỉ giao hàng thay đổi
   * @param place Thông tin địa chỉ mới
   */
  onDeliveryAddressChange(place: any): void {
    if (this.delivery.physicalDelivery?.deliveryInfo) {
      this.delivery.physicalDelivery.deliveryInfo.address = place;

      // Cập nhật khoảng cách nếu có
      if (place.distance) {
        this.delivery.physicalDelivery.distance = place.distance.distance;
      }

      this.updateDeliveryInfo();
    }
  }

  /**
   * Sao chép thông tin từ khách hàng sang người nhận
   */
  copyFromCustomer(): void {
    if (this.customerInfo && this.delivery.physicalDelivery?.deliveryInfo) {
      this.delivery.physicalDelivery.deliveryInfo.name = this.customerInfo.name || '';
      this.delivery.physicalDelivery.deliveryInfo.phoneNumber = this.customerInfo.phoneNumber || '';
      this.delivery.physicalDelivery.deliveryInfo.address = this.customerInfo.address || {};

      // Cập nhật khoảng cách nếu có
      if (this.customerInfo.address?.distance) {
        this.delivery.physicalDelivery.distance = this.customerInfo.address.distance.distance;
      }

      this.updateDeliveryInfo();
    }
  }

  /**
   * Kiểm tra xem có sử dụng tự vận chuyển hay không
   * @returns true nếu tự vận chuyển, false nếu không
   */
  isSelfTransport(): boolean {
    return this.delivery.physicalDelivery
      ? (this.delivery.physicalDelivery as any).selfTransport === true
      : false;
  }

  /**
   * Cập nhật phương thức giao hàng một cách an toàn
   * @param method Phương thức giao hàng mới
   */
  updateDeliveryMethod(method: string): void {
    if (this.delivery.physicalDelivery?.deliveryInfo) {
      this.delivery.physicalDelivery.deliveryInfo.deliveryMethod = method as 'pickup_by_courier' | 'drop_off_at_post';
      this.updateDeliveryInfo();
    }
  }

  /**
   * Cập nhật thời gian lấy hàng một cách an toàn
   * @param time Thời gian lấy hàng mới
   */
  updatePickupTime(time: string): void {
    if (this.delivery.physicalDelivery?.deliveryInfo) {
      this.delivery.physicalDelivery.deliveryInfo.pickupTime = time as 'all_day' | '8h-12h' | '13h-17h';
      this.updateDeliveryInfo();
    }
  }
}
