.picklist-filter-input {
  width: 100%;

  // Selection summary styles
  .selection-summary {
    margin-top: 4px;
    
    small {
      font-size: 0.75rem;
      color: #6c757d;
      font-style: italic;
    }
  }

  // Material form field customization
  ::ng-deep .mat-mdc-form-field {
    width: 100%;
    
    .mat-mdc-form-field-flex {
      align-items: flex-start;
      min-height: 56px;
    }

    .mat-mdc-form-field-infix {
      min-height: 40px;
      padding: 8px 0;
    }

    // Picklist input styles
    .picklist-input {
      font-size: 0.875rem;
      line-height: 1.4;
      min-width: 120px;
      
      &:focus {
        outline: none;
      }
      
      &::placeholder {
        color: #6c757d;
        opacity: 0.7;
      }
    }

    // Label styles
    .mat-mdc-form-field-label {
      font-size: 0.875rem;
      color: #6c757d;
    }

    // Error styles
    .mat-mdc-form-field-error {
      font-size: 0.75rem;
      color: #dc3545;
      margin-top: 4px;
    }

    // Disabled state
    &.mat-form-field-disabled {
      .mat-mdc-form-field-flex {
        background-color: #f8f9fa;
      }
      
      .picklist-input {
        color: #6c757d;
        cursor: not-allowed;
      }
    }

    // Focus state
    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        border-color: #007bff;
      }
    }

    // Invalid state
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline-thick {
        border-color: #dc3545;
      }
    }
  }

  // Material chips styles
  ::ng-deep mat-chip-grid {
    .mat-mdc-chip-grid {
      min-height: 40px;
      padding: 4px 0;
      margin: 0;
    }
    
    .mat-mdc-chip {
      margin: 2px 4px 2px 0;
      font-size: 0.875rem;
      background-color: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
      
      &:hover {
        background-color: #bbdefb;
      }
      
      .mat-mdc-chip-remove {
        width: 18px;
        height: 18px;
        font-size: 16px;
        color: #1976d2;
        
        &:hover {
          background-color: rgba(25, 118, 210, 0.1);
        }
        
        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
    
    .mat-mdc-chip-input {
      font-size: 0.875rem;
      min-width: 120px;
      border: none;
      outline: none;
    }
  }

  // Material autocomplete styles
  ::ng-deep .picklist-autocomplete {
    .mat-mdc-autocomplete-panel {
      max-height: 200px;
      
      .picklist-option {
        font-size: 0.875rem;
        min-height: 40px;
        padding: 8px 16px;
        
        &:hover {
          background-color: rgba(0, 123, 255, 0.1);
        }
        
        &.mat-mdc-option-active {
          background-color: rgba(0, 123, 255, 0.2);
        }
        
        &.mat-selected {
          background-color: #e3f2fd;
          color: #1976d2;
        }
      }
      
      .no-options-message {
        font-size: 0.8rem;
        color: #6c757d;
        font-style: italic;
        text-align: center;
        cursor: default;
        
        &:hover {
          background-color: transparent !important;
        }
      }
    }
  }
}

.no-input-message {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  text-align: center;
  
  small {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }
}

// Field type specific styling
.picklist-filter-input {
  &.multi-picklist {
    ::ng-deep .mat-mdc-chip {
      background-color: #e8f5e8;
      color: #2e7d32;
      border-color: #c8e6c9;
      
      &:hover {
        background-color: #c8e6c9;
      }
      
      .mat-mdc-chip-remove {
        color: #2e7d32;
      }
    }
  }
  
  &.single-picklist {
    ::ng-deep .mat-mdc-chip {
      background-color: #fff3e0;
      color: #f57c00;
      border-color: #ffcc02;
      
      &:hover {
        background-color: #ffcc02;
      }
      
      .mat-mdc-chip-remove {
        color: #f57c00;
      }
    }
  }
}

// Responsive design
@media (max-width: 576px) {
  .picklist-filter-input {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-infix {
        min-height: 36px;
        padding: 6px 0;
      }
      
      .picklist-input {
        font-size: 0.8rem;
        min-width: 100px;
      }
      
      .mat-mdc-form-field-label {
        font-size: 0.8rem;
      }
    }
    
    ::ng-deep mat-chip-grid {
      .mat-mdc-chip {
        font-size: 0.8rem;
        margin: 1px 2px 1px 0;
        
        .mat-mdc-chip-remove {
          width: 16px;
          height: 16px;
          font-size: 14px;
          
          mat-icon {
            font-size: 14px;
            width: 14px;
            height: 14px;
          }
        }
      }
      
      .mat-mdc-chip-input {
        font-size: 0.8rem;
        min-width: 100px;
      }
    }
    
    .selection-summary {
      margin-top: 2px;
      
      small {
        font-size: 0.7rem;
      }
    }
  }
  
  .no-input-message {
    padding: 8px 12px;
    
    small {
      font-size: 0.7rem;
    }
  }
}
