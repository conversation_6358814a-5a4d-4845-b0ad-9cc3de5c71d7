import { ChangeDetectionStrategy, Component, computed, EventEmitter, Input, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatNativeDateModule } from '@angular/material/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioModule } from '@angular/material/radio';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import { ChipFormFieldComponent } from '@shared/components/chip-form-field/chip-form-field.component';
import { MixedPaymentModalService } from '@/features/sales/order-form/components/modals/mixed-payment-modal/mixed-payment-modal.service';
import { NoteModalService } from '@/features/sales/order-form/components/modals/note-modal';
import { PromotionModalService } from '@/features/sales/order-form/components/modals/promotion-modal';

@Component({
  selector: 'app-payment-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatRadioModule,
    TranslateModule,
    ChipFormFieldComponent
  ],
  templateUrl: './payment-details.component.html',
  styleUrls: ['./payment-details.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentDetailsComponent {
  @Input() order: CreateOrderRequest;

  @Output() basicInfoUpdated = new EventEmitter<{ createdBy: any, saleChannel: any, createdAt: Date }>();
  @Output() paymentUpdated = new EventEmitter<OrderPayment>();
  @Output() summaryUpdated = new EventEmitter<any>();

  // Mock data (sẽ thay thế bằng service thực tế sau)
  mockEmployeeList = [
    { _id: '1', name: 'Nguyễn Văn A - NV001' },
    { _id: '2', name: 'Trần Thị B - NV002' },
    { _id: '3', name: 'Lê Văn C - NV003' }
  ];

  mockSaleChannelList = [
    { _id: '1', name: 'direct', displayName: 'Tại cửa hàng' },
    { _id: '2', name: 'phone', displayName: 'Điện thoại' },
    { _id: '3', name: 'online', displayName: 'Online' }
  ];

  mockOrderStatuses = [
    { id: 'pending', name: 'Đang xử lý' },
    { id: 'confirmed', name: 'Đã xác nhận' },
    { id: 'delivering', name: 'Đang giao hàng' },
    { id: 'completed', name: 'Đã giao' },
    { id: 'cancelled', name: 'Hủy' }
  ];

  showTagInput = signal(false);

  // Computed signal lấy danh sách tên tag từ order.tags
  tagNames = computed(() => this.order?.tags?.map((tag: { name: string }) => tag.name) || []);

  constructor(
    private dialog: MatDialog,
    private noteModalService: NoteModalService,
    private promotionModalService: PromotionModalService
  ) {}

  ngOnInit(): void {
    // ... existing code ...
  }

  // Xử lý khi thay đổi nhân viên bán hàng
  onEmployeeChange(employeeId: string): void {
    // ... existing code ...
  }

  // Xử lý khi thay đổi kênh bán hàng
  onSaleChannelChange(channelId: string): void {
    // ... existing code ...
  }

  // Xử lý khi thay đổi ngày giờ tạo đơn
  onCreatedAtChange(date: Date | null): void {
    // ... existing code ...
  }

  // Hiển thị/ẩn ô nhập tag
  onAddTagClick(): void {
    this.showTagInput.set(!this.showTagInput());
  }

  // Xử lý khi danh sách tên tag thay đổi từ ChipFormFieldComponent
  onTagNamesChange(newTagNames: string[]): void {
    if (!this.order) return;

    const currentTags = this.order.tags || [];
    const currentTagNames = currentTags.map((tag: { name: string }) => tag.name);

    // Tag names to add: exist in new but not in current
    const tagNamesToAdd = newTagNames.filter(name => !currentTagNames.includes(name));

    // Update order.tags
    // Giữ lại những tag cũ có tên trong newTagNames
    const updatedTags = currentTags.filter((tag: { name: string }) => newTagNames.includes(tag.name));
    // Thêm những tag mới có tên trong tagNamesToAdd
    tagNamesToAdd.forEach(name => updatedTags.push({ name }));

    this.order.tags = updatedTags;

    // Thông báo cập nhật order
    this.summaryUpdated.emit({ tags: this.order.tags });

    // Bạn có thể chọn ẩn input sau khi cập nhật hoặc giữ nguyên
    // this.showTagInput.set(false);
  }

  // Xử lý khi nhấn nút ghi chú
  async onNoteClick(): Promise<void> {
    try {
      const result = await this.noteModalService.open({
        note: this.order.note || '',
        internalNote: this.order.internalNote || ''
      });

      if (result) {
        // Cập nhật ghi chú từ modal
        const updatedOrder = { ...this.order };
        if (result.note !== undefined) {
          updatedOrder.note = result.note;
        }
        if (result.internalNote !== undefined) {
          updatedOrder.internalNote = result.internalNote;
        }
        this.summaryUpdated.emit(updatedOrder);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú:', error);
    }
  }

  // Xử lý khi nhấn nút giảm giá
  async onDiscountClick(): Promise<void> {
    // Tính tổng tiền hàng hiện tại để hiển thị trong modal
    const totalItemsAmount = this.calculateTotalItemsAmount();

    try {
      const result = await this.promotionModalService.open({
        totalAmount: totalItemsAmount,
        discounts: this.order.discounts
      });

      if (result) {
        // Cập nhật thông tin giảm giá từ modal
        const updatedOrder = { ...this.order };
        updatedOrder.discounts = result.discounts;
        this.summaryUpdated.emit(updatedOrder);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal khuyến mại:', error);
    }
  }

  // Tính tổng tiền hàng (helper method)
  private calculateTotalItemsAmount(): number {
    if (!this.order.items || this.order.items.length === 0) {
      return 0;
    }

    return this.order.items.reduce((total: number, item: { quantity?: number, price?: number }) => total + ((item.quantity || 0) * (item.price || 0)), 0);
  }
}
