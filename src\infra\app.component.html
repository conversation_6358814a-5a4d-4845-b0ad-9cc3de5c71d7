@if (viewport$ | async; as viewport) {
<mat-drawer-container
  class="app-drawer"
  [class.drawer-open]="drawer.opened"
  [class.is-desktop] = 'viewport?.isDesktop'
  hasBackdrop="true"
  (backdropClick)="drawerBackdropClick()"
  >
  <mat-drawer
    #drawer
    mode="over"
    autoFocus="false"
    >
    @if (drawer.opened) {
    <app-navigation-drawer></app-navigation-drawer>
    }
  </mat-drawer>

  <mat-drawer-content>
    <main
      class="main main-container"
      [class.hide-sidenav] = '!isShowHeader'
      [class.is-desktop] = 'viewport?.isDesktop'
      >
      <app-header></app-header>

        @if (!viewport.isDesktop) {
          <app-bottom-navigation></app-bottom-navigation>
        }


      <div class="app-main">
        <router-outlet></router-outlet>
      </div>

      <div class="app-loader" [hidden]="!hasLoadingBar">
        @if (hasLoadingBar) {
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
        }
      </div>
    </main>
  </mat-drawer-content>
</mat-drawer-container>

}
