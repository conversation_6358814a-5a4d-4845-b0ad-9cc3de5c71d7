import { Component, ViewEncapsulation, signal, ChangeDetectionStrategy, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { MatRippleModule } from '@angular/material/core';
import { AccordionModule } from 'primeng/accordion';
import { PosUpdateData } from 'salehub_shared_contracts';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { AppNavigationDrawerService } from './app-navigation-drawer.service';
import { NavigationService } from '../navigation.service';
import { Navigation, NavigationItem } from '@/config/navigation.config';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-navigation-drawer',
  standalone: true,
  imports: [
    RouterLink,
    MatRippleModule,
    AccordionModule,
    CommonModule,
    TranslateModule
  ],
  templateUrl: './app-navigation-drawer.component.html',
  styleUrls: ['./app-navigation-drawer.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppNavigationDrawerComponent implements AfterViewInit {
  @ViewChild('navigationContentInner') navigationContentInner!: ElementRef<any>;

  private timeoutScrollToElement!: ReturnType<typeof setTimeout>;

  // Getter cho các signals từ service
  get updateData(): ReturnType<typeof signal<PosUpdateData>> { return this.navigationDrawerService.updateData; }
  get previousActiveNavigationBlock() { return this.navigationDrawerService.previousActiveNavigationBlock; }
  get activeNavigationBlock() { return this.navigationDrawerService.activeNavigationBlock; }
  get iconAnimating() { return this.navigationDrawerService.iconAnimating; }

  get treeUrl() { return this.navigationService.treeUrl; }


  constructor(
    private navigationDrawerService: AppNavigationDrawerService,
    private navigationService: NavigationService
  ) {
  }

  ngAfterViewInit() {
    this.scrollInToRouterActive();
  }

  scrollInToRouterActive() {
    const ele1: HTMLElement | null = document.querySelector('app-navigation-drawer .sub-url.active');
    const ele2: HTMLElement | null = document.querySelector('app-navigation-drawer .top-url.active2');
    this.scrollToElement(ele1 ?? ele2);
  }

  scrollToElement(ele: HTMLElement | null) {
    if(ele) {
      const topPos = ele.offsetTop;

      if(topPos &&
        this.navigationContentInner?.nativeElement &&
        this.navigationContentInner.nativeElement.scrollTop > topPos
      ) {
        this.navigationContentInner.nativeElement.scrollTop = topPos;
      }
    }
  }


  // Xử lý sự kiện khi mở accordion
  onAccordionOpen(navigation: Navigation, id: string) {
    if(this.timeoutScrollToElement) {
      clearTimeout(this.timeoutScrollToElement);
    }
    this.navigationDrawerService.onAccordionOpen(navigation);

    this.timeoutScrollToElement = setTimeout(() => {
      this.scrollToElement(document.querySelector(`app-navigation-drawer #${id} p-accordion-header`));
    }, AppNavigationDrawerService.ANIMATION_TIME_MS);
  }

  // Phương thức để mở drawer
  openDrawer() {
    this.navigationDrawerService.openDrawer();
  }

  // Phương thức để đóng drawer
  closeDrawer() {
    this.navigationDrawerService.closeDrawer();
  }

  isTopLevelItemActive(item: NavigationItem): boolean {
    return this.navigationService.isUrlInNavigationTree(this.navigationService.currentUrl(), item);
  }

  onClickUrl() {
    this.closeDrawer();
    // setTimeout(() => this.closeDrawer(), 200);
  }
}
