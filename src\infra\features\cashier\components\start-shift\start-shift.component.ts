import { UpdateDataStore } from '@core/store/update_data.store';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Component, Inject } from '@angular/core';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { PosInitData, PosUpdateData } from 'salehub_shared_contracts';
import { HttpService } from '@core/services/http.service';

@Component({
  selector: 'shift-start-dialog',
  templateUrl: 'start-shift.dialog.html',
  standalone: true,
  imports: [
    MatCheckboxModule
  ]
})
export class ShiftStartDialog {
  checkedIds: string[] = [];
  isSubmitting = false;

  constructor(
    public dialogRef: MatDialogRef<ShiftStartDialog>,
    @Inject(MAT_DIALOG_DATA) public data: PosInitData['receiptionists'],
    private http: HttpService,
    private updateDataStore: UpdateDataStore
  ) {}

  submit() {
    this.isSubmitting = true;

    this.http.post<PosUpdateData['currentShift']>(
      'cashier',
      'start_shift',
      {
        employeeIds: this.checkedIds
      }
    )
      .subscribe(
        {
          next: (value) => {
            this.isSubmitting = false;
            const data = this.updateDataStore.getData();
            data.currentShift = value;

            this.updateDataStore.updatePosData(data);

            this.dialogRef.close();
          },
          error: () => {
            this.isSubmitting = false;
          }
        }
      );
  }

  check($event: MatCheckboxChange) {
    const _id = $event.source.value;
    if(!$event.checked) {
      this.checkedIds = this.checkedIds.filter(id => id !== _id);
    } else {
      this.checkedIds.push(_id);
    }
  }

  close(): void {
    this.dialogRef.close();
  }
}
