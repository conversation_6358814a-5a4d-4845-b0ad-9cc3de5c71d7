import { Component, Inject, Optional } from "@angular/core";
import { CommonModule } from "@angular/common";
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from "@angular/material/dialog";
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from "@angular/material/bottom-sheet";
import { MatButtonModule } from "@angular/material/button";
import { TranslateModule } from "@ngx-translate/core";

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface ConfirmModalData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: "primary" | "accent" | "warn";
}

/**
 * Kiểu dữ liệu trả về từ modal
 */
export type ConfirmModalResult = boolean;

/**
 * Component modal xác nhận
 */
@Component({
  selector: "app-confirm-modal",
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    TranslateModule
  ],
  template: `
    <h2 mat-dialog-title>{{ title | translate }}</h2>
    <mat-dialog-content>
      <p>{{ message | translate }}</p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">
        {{ cancelText | translate }}
      </button>
      <button mat-raised-button [color]="confirmColor" (click)="onConfirm()">
        {{ confirmText | translate }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    mat-dialog-content {
      min-width: 300px;
    }
  `]
})
export class ConfirmModalComponent {
  /**
   * Tiêu đề của modal
   */
  title: string;

  /**
   * Nội dung thông báo
   */
  message: string;

  /**
   * Văn bản nút xác nhận
   */
  confirmText: string;

  /**
   * Văn bản nút hủy
   */
  cancelText: string;

  /**
   * Màu sắc nút xác nhận
   */
  confirmColor: "primary" | "accent" | "warn";

  /**
   * Dữ liệu đầu vào cho modal
   */
  data: ConfirmModalData;

  constructor(
    @Optional() private dialogRef?: MatDialogRef<ConfirmModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<ConfirmModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: ConfirmModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: ConfirmModalData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      title: "COMMON.CONFIRM",
      message: "COMMON.CONFIRM_MESSAGE"
    };

    this.title = this.data.title || "COMMON.CONFIRM";
    this.message = this.data.message || "COMMON.CONFIRM_MESSAGE";
    this.confirmText = this.data.confirmText || "COMMON.CONFIRM";
    this.cancelText = this.data.cancelText || "COMMON.CANCEL";
    this.confirmColor = this.data.confirmColor || "primary";
  }

  /**
   * Xử lý khi nhấn nút Xác nhận
   */
  onConfirm(): void {
    this.close(true);
  }

  /**
   * Xử lý khi nhấn nút Hủy
   */
  onCancel(): void {
    this.close(false);
  }

  /**
   * Đóng modal
   */
  private close(result: boolean): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
