@use '../_variable' as *;

.mat-drawer {
  position: fixed !important;
}
.mat-drawer-inner-container {
  overflow: hidden !important;
}
.mat-drawer-backdrop {
  backdrop-filter: initial;
}
.mat-drawer-transition .mat-drawer-backdrop {
  transition-property: background-color, visibility, backdrop-filter;
}
.mat-drawer-backdrop.mat-drawer-shown {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px); /* Hỗ trợ trình duyệt WebKit */
}

.app-main, .mat-drawer-content {
  width: inherit;
  position: relative;
  background: none !important;
}
.mat-drawer-inner-container {
  scrollbar-width: thin;
}

.mat-drawer-content {
  overflow: visible !important;
}


@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  .mat-drawer-container.app-drawer {
    overflow: visible;

    &.drawer-open {
      overflow: hidden;

      // app-header {
      //   left: $desktopSidebarWidth+px;
      // }
    }

    .mat-drawer {
      width: $desktopSidebarWidth+px;
    }
    .mat-drawer-content {
      overflow: visible;
    }
  }
}
