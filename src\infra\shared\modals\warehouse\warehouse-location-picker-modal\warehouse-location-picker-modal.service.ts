import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { WarehouseLocationPickerModalComponent } from './warehouse-location-picker-modal.component';
import { WarehouseLocationPickerModalData, WarehouseLocationPickerModalResult } from './models/warehouse-location-picker-modal.model';

/**
 * Service để mở modal chọn vị trí kho
 */
@Injectable({
  providedIn: 'root'
})
export class WarehouseLocationPickerModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal chọn vị trí kho
   * @param data Dữ liệu cho modal
   * @returns Promise<WarehouseLocationPickerModalResult> Kết quả ID vị trí đã chọn hoặc undefined nếu hủy
   */
  async open(data: WarehouseLocationPickerModalData): Promise<WarehouseLocationPickerModalResult> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '95vw',
        autoFocus: true,
        restoreFocus: true,
        ariaDescribedBy: 'location-dialog-description'
      };
      
      const result = await this.responsiveModalService.open<
        WarehouseLocationPickerModalComponent,
        WarehouseLocationPickerModalData,
        WarehouseLocationPickerModalResult
      >(WarehouseLocationPickerModalComponent, modalConfig);
      
      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal chọn vị trí kho:', error);
      return undefined;
    }
  }
}
