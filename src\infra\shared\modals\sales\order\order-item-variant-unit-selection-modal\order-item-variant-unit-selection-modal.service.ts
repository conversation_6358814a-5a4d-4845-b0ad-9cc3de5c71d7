import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { OrderItemVariantUnitSelectionModalComponent } from './order-item-variant-unit-selection-modal.component';
import { 
  OrderItemVariantUnitSelectionModalData, 
  OrderItemVariantUnitSelectionResult 
} from '@/shared/models/view/order-item-variant-unit-selection.model';

/**
 * Service để mở modal chọn variant và unit cho sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class OrderItemVariantUnitSelectionModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal chọn variant và unit cho sản phẩm
   * @param data Dữ liệu cho modal
   * @returns Promise<OrderItemVariantUnitSelectionResult | null> Kết quả chọn variant và unit hoặc null nếu hủy
   */
  async open(data: OrderItemVariantUnitSelectionModalData): Promise<OrderItemVariantUnitSelectionResult | null> {
    try {
      const modalConfig = {
        data,
        panelClass: 'variant-unit-selection-modal'
      };
      
      const result = await this.responsiveModalService.open<
        OrderItemVariantUnitSelectionModalComponent,
        OrderItemVariantUnitSelectionModalData,
        OrderItemVariantUnitSelectionResult
      >(OrderItemVariantUnitSelectionModalComponent, modalConfig);
      
      return result || null;
    } catch (error) {
      console.error('Lỗi khi mở modal chọn variant và unit:', error);
      return null;
    }
  }
}
