import { Component, ViewEncapsulation, Input, Output, EventEmitter, Inject, Optional, ChangeDetectionStrategy, signal, computed } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import {
  EmblaCarouselDirective
} from 'embla-carousel-angular';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheet } from '@angular/material/bottom-sheet';
import { MatDialog } from '@angular/material/dialog';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { PosCategorizedProducts, PosInvoiceItem, PosProduct } from 'salehub_shared_contracts';
import { PageMessageService } from '@core/services/page_message.service';
import { scrollTop } from '@shared/utils';
import { CashierOrderProductService } from './order-product-list.service';
import { CashierOrderEditProductComponent } from '../order-edit-product/order-edit-product.component';

@Component({
  selector: 'app-order-product-list',
  standalone: true,
  imports: [
    MatRadioModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatAutocompleteModule,
    MatSelectModule,
    FormatStringPipe,
    EmblaCarouselDirective
  ],
  providers: [
    CashierOrderProductService
  ],
  templateUrl: './order-product-list.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrl: './order-product-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
/**
 * @description
 * component này được sử dụng trong 2 trường hợp:
 * 1. khi tạo order mới
 * 2. khi sửa order
 *
 * Trong mỗi trường hợp, sẽ có 2 loại là chọn mặt hàng chính và chọn mặt hàng topping
 *
 * component này hoạt động riêng biệt, với service cũng là riêng biệt
 * để mỗi lần submit sẽ trả về kết quả là {items: PosInvoiceItem[], products: PosCategorizedProducts}
 * cho component cha xử lý
 * nếu không dùng dữ liệu gốc từ CashierOrderService thì sẽ bị trùng lặp dữ liệu
 * khi nó chọn topping
 *
 */
export class CashierOrderProductListComponent {
  /**
   * khi sửa 1 order, sẽ có selectedItems
   * còn khi tạo order mới thì không
   */
  @Input() iSelectedItems!: PosInvoiceItem[];
  @Input() iProducts!: PosCategorizedProducts;
  @Input() iToppingProducts!: PosCategorizedProducts;

  @Output() invoiceItems = new EventEmitter<{items: PosInvoiceItem[], products: PosCategorizedProducts}>();

  emblaCarouselOptions = {
    loop: false,
    dragFree: true
  };

  total = signal<number>(0);
  activeProductIndex = signal<number>(0);

  selectedItems!: CashierOrderProductService['selectedItems'];
  products!: CashierOrderProductService['products'];

  /**
   * hiện từ product list chính hay topping list
   */
  isSelectingToppingItems = signal<boolean>(false);
  toppingForItem = signal<string>('');

  hasButton = computed(() => {
    if(this.isSelectingToppingItems()) {
      return true;
    }

    return this.selectedItems()?.length > 0;
  });

  constructor(
    private _bottomSheet: MatBottomSheet,
    private pageMsg: PageMessageService,
    private service: CashierOrderProductService,
    public dialog: MatDialog,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private d?: {
      selectedItems: PosInvoiceItem[],
      products: PosCategorizedProducts,
      toppingForItem: string
    }
  ) {
    this.selectedItems = this.service.selectedItems;
    this.products = this.service.products;

    if(d) {
      this.toppingForItem.set(d.toppingForItem);
      this.isSelectingToppingItems.set(true);

      this.service.buildProductOnViewInit(
        d.selectedItems,
        d.products,
        true
      );

      this.getTotal();
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if(this.iSelectedItems && this.iProducts) {
        // console.warn('set tu ngAfterViewInit');

        this.service.buildProductOnViewInit(
          this.iSelectedItems,
          this.iProducts,
          false
        );
        this.getTotal();
      }

      // this.openCustomItem(this.products[0].items[0]);
    }, 0);
  }

  openTopping(productIndex: number, productCategorizedIndex: number) {
    const products: PosProduct[] = this.products()[productCategorizedIndex].items;
    const product = products[productIndex];
    const item = product.selectedItem;

    if(!item) {
      this.pageMsg.error(`Vui lòng chọn lại: ${product.name}`);
      return;
    }

    /**
     * clone không khi chọn 1 lần
     * chọn lần thứ 2 nó dùng lại ref cũ
     */
    const toppingProducts = JSON.parse(JSON.stringify(this.iToppingProducts));
    const originItemOptionsLength = item.options?.length ?? 0;

    const ref = this._bottomSheet
      .open(CashierOrderProductListComponent, {
        data: {
          toppingForItem: item.name,
          products: toppingProducts,
          selectedItems: item.options ?? []
        },
        panelClass: ['invoice', 'invoice-topping', 'full-screen-bottom-sheet'],
        disableClose: true
      });

    ref.afterDismissed()
      .subscribe({
        next: (result: {items: PosInvoiceItem[], products: PosCategorizedProducts}) => {
          // false = close
          if(!result) {
            return;
          }

          this.service.buildProductWithTopping(
            products,
            productIndex,
            productCategorizedIndex,
            product,
            item,
            originItemOptionsLength,
            result.items
          );
          this.getTotal();

          // @ts-ignore
          this.bottomRef = null;
        }
      });
  }

  openCustomItem(products: PosProduct[], productIndex: number, productCategorizedIndex: number) : void {
    const product = products[productIndex];
    const item = product.selectedItem;

    if(!item) {
      this.pageMsg.error(`Chưa chọn mặt hàng: ${product.name}`);
      return;
    }

    const dialogRef = this.dialog.open(CashierOrderEditProductComponent, {
      data: {
        invoiceItem: item
      },
      panelClass: ['full-screen-modal'],
      disableClose: true
    });

    const originIsCustomItem = !!item.isCustomItem;
    const originOptionsLength = item.options?.length ?? 0;

    dialogRef.afterClosed()
      .subscribe(
        {
          next: (newItem: PosInvoiceItem) => {
            this.service.buildCustomItemResult(
              originIsCustomItem,
              originOptionsLength,
              product,
              products,
              productIndex,
              productCategorizedIndex,
              newItem
            );
          },
          error: () => {
          }
        }
      );
  }

  addItem(productIndex: number, productCategorizedIndex: number, event?: Event) {
    if(event) {
      event.stopPropagation();
    }

    this.service.addItem(productIndex, productCategorizedIndex, event);
    this.getTotal();
  }

  removeItem(productIndex: number, productCategorizedIndex: number, event: Event) {
    event.stopPropagation();
    this.service.removeItem(productIndex, productCategorizedIndex);
    this.getTotal();
  }

  getTotal() {
    this.total.set(this.service.getTotal());
  }

  reset() {
    this.service.reset();
    this.isSelectingToppingItems.set(false);
    this.getTotal();
    scrollTop();
  }

  submit() {
    const items = this.selectedItems().filter(x => x.quantity > 0);
    const result = {
      items,
      products: this.products()
    };

    if(this._bottomSheet?._openedBottomSheetRef) {
      this._bottomSheet.dismiss(result);
      return;
    }

    this.invoiceItems.emit(result);
  }

  close() {
    if(this._bottomSheet?._openedBottomSheetRef) {
      this._bottomSheet.dismiss(false);
    }
  }

  selectProductListIndex(index: number) {
    this.activeProductIndex.set(index);
    scrollTop();
  }
}
