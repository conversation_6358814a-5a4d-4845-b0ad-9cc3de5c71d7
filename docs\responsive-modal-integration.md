# Hướng dẫn sử dụng `ResponsiveModalService`

## Tổng quan
`ResponsiveModalService` là một dịch vụ Angular cho phép mở modal (`MatDialog`) hoặc bottom sheet (`MatBottomSheet`) một cách linh hoạt, tự động chuyển đổi giữa hai chế độ dựa trên kích thước màn hình thiết bị (desktop hoặc mobile). Dịch vụ này sử dụng các token chuẩn của Angular Material (`MAT_DIALOG_DATA` và `MAT_BOTTOM_SHEET_DATA`) để truyền dữ liệu vào component, không yêu cầu implements interface `ModalComponent` hay sử dụng `MODAL_DATA`.

## Cách sử dụng
Dịch vụ cung cấp phương thức `open` để mở modal hoặc bottom sheet, với các generic type được chỉ định rõ ràng để đảm bảo an toàn kiểu TypeScript.

### Phương thức chính
```typescript
async open<T, D = any, R = any>(
  component: Type<T>,
  modalConfig: MatDialogConfig<D> | MatBottomSheetConfig<D> = {},
  forceMode?: 'dialog' | 'bottom-sheet'
): Promise<R | undefined>
```
- **`T`**: Kiểu của component Angular (ví dụ: `UserProfileModalComponent`).
- **`D`**: Kiểu của dữ liệu truyền vào (ví dụ: `{ userId: number }`).
- **`R`**: Kiểu của kết quả trả về khi modal/bottom sheet đóng (ví dụ: `boolean`).
- **`component`**: Component Angular sẽ hiển thị (phải là một `Type<T>`).
- **`modalConfig`**: Cấu hình cho dialog (`MatDialogConfig<D>`) hoặc bottom sheet (`MatBottomSheetConfig<D>`), bao gồm thuộc tính `data` để truyền dữ liệu kiểu `D`.
- **`forceMode`**: (Tùy chọn) Ép sử dụng `'dialog'` hoặc `'bottom-sheet'`, bỏ qua kiểm tra kích thước màn hình.
- **Trả về**: `Promise<R | undefined>` chứa kết quả khi modal/bottom sheet đóng.

### Ví dụ component đơn giản
Dưới đây là một component đơn giản (`UserProfileModalComponent`) sử dụng `MAT_DIALOG_DATA` và `MAT_BOTTOM_SHEET_DATA` để nhận dữ liệu:

```typescript
import { Component, Inject, Optional } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';

// Định nghĩa interface cho dữ liệu
interface UserProfileData {
  userId: number;
}

@Component({
  selector: 'app-user-profile-modal',
  template: `
    <h2>User Profile</h2>
    <p>ID: {{ data?.userId }}</p>
    <button (click)="close(true)">Close</button>
  `,
  styles: [`
    h2 { font-size: 1.5rem; margin-bottom: 1rem; }
    p { margin-bottom: 1rem; }
    button { padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; }
  `]
})
export class UserProfileModalComponent {
  data: UserProfileData | null | undefined;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: UserProfileData | null | undefined,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: UserProfileData | null | undefined,
    @Optional() private dialogRef?: MatDialogRef<UserProfileModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<UserProfileModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData;
  }

  close(result: boolean): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
```

#### Giải thích component
1. **Interface `UserProfileData`**:
   - Định nghĩa kiểu dữ liệu `{ userId: number }` để đảm bảo an toàn kiểu TypeScript.
2. **Template**:
   - Hiển thị tiêu đề, `userId` từ `data`, và nút đóng.
   - Sử dụng toán tử `?.` để xử lý trường hợp `data` là `null` hoặc `undefined`.
3. **Constructor**:
   - Inject `@Optional() @Inject(MAT_DIALOG_DATA)` để nhận dữ liệu từ `MatDialog` (kiểu `UserProfileData | null | undefined`).
   - Inject `@Optional() @Inject(MAT_BOTTOM_SHEET_DATA)` để nhận dữ liệu từ `MatBottomSheet` (kiểu `UserProfileData | null | undefined`).
   - Kết hợp dữ liệu: `this.data = this.dialogData || this.bottomSheetData`.
   - Inject `@Optional() MatDialogRef` và `@Optional() MatBottomSheetRef` để đóng modal/bottom sheet.
4. **Phương thức `close`**:
   - Nhận tham số `result` kiểu `boolean` và trả về khi đóng.
   - Kiểm tra `dialogRef` hoặc `bottomSheetRef` để gọi `close` (dialog) hoặc `dismiss` (bottom sheet).

### Ví dụ sử dụng `ResponsiveModalService` với type rõ ràng
Dưới đây là cách sử dụng `ResponsiveModalService` để mở `UserProfileModalComponent`, với các generic type được chỉ định đầy đủ:

```typescript
import { Component } from '@angular/core';
import { ResponsiveModalService } from './responsive-modal.service';
import { UserProfileModalComponent } from './user-profile-modal.component';
import { MatDialogConfig } from '@angular/material/dialog';

// Định nghĩa interface cho dữ liệu
interface UserProfileData {
  userId: number;
}

@Component({
  selector: 'app-example',
  template: `<button (click)="openModal()">Mở Modal</button>`
})
export class ExampleComponent {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  async openModal(): Promise<void> {
    // Dữ liệu đầu vào
    const modalData: UserProfileData = { userId: 123 };

    // Cấu hình modal với kiểu rõ ràng
    const modalConfig: MatDialogConfig<UserProfileData> = {
      data: modalData,
      width: '400px', // Chiều rộng dialog (desktop)
      maxWidth: '90vw', // Chiều rộng tối đa
      panelClass: 'custom-modal' // Lớp CSS tùy chỉnh
    };

    try {
      // Mở modal/bottom sheet với type rõ ràng
      const result: boolean | undefined = await this.responsiveModalService.open<
        UserProfileModalComponent,
        UserProfileData,
        boolean
      >(UserProfileModalComponent, modalConfig);

      // Xử lý kết quả
      console.log('Modal đóng với kết quả:', result);
    } catch (error) {
      console.error('Lỗi khi mở modal:', error);
    }
  }
}
```

#### Giải thích ví dụ
1. **Interface `UserProfileData`**:
   - Định nghĩa `{ userId: number }` để sử dụng trong `modalConfig.data` và component.
2. **Dữ liệu (`modalData`)**:
   - Khởi tạo `modalData` với kiểu `UserProfileData` và truyền vào `modalConfig.data`.
3. **Cấu hình (`modalConfig`)**:
   - Khai báo `MatDialogConfig<UserProfileData>` để chỉ định kiểu dữ liệu `D`.
   - Bao gồm `data`, `width`, `maxWidth`, và `panelClass` để tùy chỉnh giao diện.
4. **Mở modal/bottom sheet**:
   - Gọi `responsiveModalService.open` với:
     - `T = UserProfileModalComponent`: Kiểu component.
     - `D = UserProfileData`: Kiểu dữ liệu đầu vào.
     - `R = boolean`: Kiểu kết quả trả về.
   - Truyền `UserProfileModalComponent` và `modalConfig`.
   - Dịch vụ tự động chọn `MatDialog` (desktop) hoặc `MatBottomSheet` (mobile) dựa trên `ViewportService.isMobile()`.
5. **Xử lý kết quả**:
   - Kết quả là `boolean | undefined` (ví dụ: `true` nếu nhấn nút đóng).
   - Sử dụng `try-catch` để xử lý lỗi.

### Ví dụ ép chế độ bottom sheet
```typescript
const result: boolean | undefined = await this.responsiveModalService.open<
  UserProfileModalComponent,
  UserProfileData,
  boolean
>(
  UserProfileModalComponent,
  {
    data: { userId: 123 },
    panelClass: 'custom-bottom-sheet'
  },
  'bottom-sheet' // Ép dùng bottom sheet ngay cả trên desktop
);
```

## Lưu ý quan trọng
1. **Generic types**:
   - Luôn chỉ định rõ `T`, `D`, và `R` khi gọi `.open` để đảm bảo an toàn kiểu:
     - `T`: Kiểu component (ví dụ: `UserProfileModalComponent`).
     - `D`: Kiểu dữ liệu đầu vào (ví dụ: `UserProfileData`).
     - `R`: Kiểu kết quả trả về (ví dụ: `boolean`).
   - Ví dụ: `.open<UserProfileModalComponent, UserProfileData, boolean>(...)`.
2. **Component**:
   - Không cần implements `ModalComponent` hay khai báo `modalData`.
   - Sử dụng `@Optional() @Inject(MAT_DIALOG_DATA)` và `@Optional() @Inject(MAT_BOTTOM_SHEET_DATA)` để nhận dữ liệu.
   - Kết hợp dữ liệu bằng `this.data = this.dialogData || this.bottomSheetData`.
3. **Module imports**:
   - Đảm bảo import `MatDialogModule` và `MatBottomSheetModule` trong module chứa component (ví dụ: `AppModule`).
   - Ví dụ:
     ```typescript
     imports: [MatDialogModule, MatBottomSheetModule]
     ```
4. **An toàn kiểu**:
   - Sử dụng interface (như `UserProfileData`) để định nghĩa dữ liệu.
   - Đảm bảo `modalConfig` có kiểu `MatDialogConfig<D>` hoặc `MatBottomSheetConfig<D>` khớp với `D`.
5. **Xử lý `null`/`undefined`**:
   - Trong template, dùng `?.` (ví dụ: `data?.userId`) để xử lý trường hợp `data` là `null` hoặc `undefined`.
   - Trong component, kiểm tra `dialogRef` hoặc `bottomSheetRef` trước khi gọi `close`/`dismiss`.
