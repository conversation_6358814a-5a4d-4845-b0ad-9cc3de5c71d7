<h2 mat-dialog-title>Edit Colunms</h2>


<div class="column-selector-container">
  <mat-card class="left-panel">
    <h3>DIMENSIONS & METRICS</h3>
    <mat-list class="column-list">
      <mat-list-item *ngFor="let col of allColumns(); trackBy: trackByColumn2" class="column-item">
        <mat-checkbox
          [checked]="selectedColumns().includes(col.key)"
          (change)="toggleColumn(col.key)">
          {{ col.label }}
        </mat-checkbox>
      </mat-list-item>
    </mat-list>
  </mat-card>

  <mat-card class="right-panel">
    <h3>Selected Columns</h3>
    <div class="section">
      <h4>Fixed Columns</h4>
      <mat-list class="column-list" cdkDropList id="pinnedList" (cdkDropListDropped)="drop($event)" [cdkDropListConnectedTo]="['regularList']">
        <mat-list-item
          *ngFor="let col of pinnedColumns(); trackBy: trackByColumn"
          class="column-item"
          cdkDrag
          >
        <ng-container *ngTemplateOutlet="listItem; context: { col: col }"></ng-container>
      </mat-list-item>
      </mat-list>
    </div>
    <div class="section">
      <h4>Regular Columns</h4>
      <mat-list class="column-list" cdkDropList id="regularList" (cdkDropListDropped)="drop($event)" [cdkDropListConnectedTo]="['pinnedList']">
        <mat-list-item
          *ngFor="let col of regularColumns(); trackBy: trackByColumn"
          class="column-item"
          cdkDrag
          >
        <ng-container *ngTemplateOutlet="listItem; context: { col: col }"></ng-container>
      </mat-list-item>

      </mat-list>
    </div>
  </mat-card>
</div>

<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="onConfirm()">
    {{ 'COMMON.CONFIRM' | translate }}
  </button>
</mat-dialog-actions>


<ng-template #listItem let-col="col">
  <div class="flex align-items-center">
    <div class="handle" cdkDragHandle>
      <mat-icon class="d-block">drag_indicator</mat-icon>
    </div>
    <div>
      {{ getColumnLabel(col) }}
    </div>
    <div class="ms-auto">
      <button mat-icon-button (click)="togglePin(col)" class="pin-button">
        <mat-icon
          matTooltip="Info about the action"
        >push_pin</mat-icon>
      </button>
        <button mat-icon-button (click)="toggleColumn(col)" class="pin-button">
        <mat-icon
          matTooltip="Info about the action"
        >close</mat-icon>
      </button>
    </div>
  </div>
</ng-template>

