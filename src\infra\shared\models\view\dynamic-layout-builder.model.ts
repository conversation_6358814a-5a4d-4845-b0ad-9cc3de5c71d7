// Field value types - Union type cho tất cả các loại giá trị có thể
export type FieldValue = string | number | boolean | Date | string[] | number[] | null | undefined;

// Field constraints interface - Ràng buộc cho từng loại field
export interface FieldConstraints {
  // Text field constraints
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  textType?: string; // 'small', 'medium', 'large'

  // Number field constraints
  min?: number;
  max?: number;
  step?: number;
  maxDigits?: number;

  // Date field constraints
  minDate?: string;
  maxDate?: string;

  // Select/dropdown constraints
  options?: Array<{ value: string | number; label: string }> | string[];
  picklistValues?: string[]; // For picklist fields
  multiple?: boolean;

  // File upload constraints
  allowedFileTypes?: string[];
  allowedTypes?: string[]; // Alias for allowedFileTypes
  maxFileSize?: number; // in bytes
  maxSize?: number; // Alias for maxFileSize
  allowMultipleFiles?: boolean;
  maxFiles?: number;
  maxImages?: number;

  // Checkbox/Radio constraints
  defaultValue?: boolean | string | number | string[];
  enableByDefault?: boolean;

  // Color field constraints
  format?: string; // 'hex', 'rgb', 'hsl'

  // Search/Brand constraints
  searchable?: boolean;
  hierarchical?: boolean;

  // Email constraints
  unique?: boolean;

  // Permission constraints
  permissions?: Array<{ profileId: string; permission: string }>;

  // Search module constraints
  searchModule?: string;

  // Picklist constraints
  sortOrder?: 'input' | 'alphabetical';

  // User field constraints
  userType?: 'single' | 'multiple';

  // Common constraints
  required?: boolean;
  readonly?: boolean;
  disabled?: boolean;
}

// Simplified field interface for dynamic layout builder
export interface LayoutField {
  id: number;
  _id?: number; // Backward compatibility
  type: string;
  label: string;
  value?: FieldValue;
  isRequired?: boolean;
  required?: boolean; // Alias for isRequired
  isPublic?: boolean;
  constraints?: FieldConstraints;
  order: number;
  description?: string; // Description for field type selector
  placeholder?: string; // Placeholder text for form fields
}

/**
 * Sử dụng Input trong Component:
    layoutConfig: Khởi tạo Golden Layout với bố cục panels.
    sections: Hiển thị sections và fields trong Golden Layout.
    templates: Hiển thị trong dropdown chọn template ngành.
    tenantId: Gửi kèm trong API call và kiểm tra quyền qua IAM.
    availableFieldTypes: Hiển thị danh sách Field Types trong block New Fields.

    Validation Input:
    layoutConfig: Nếu cung cấp, phải hợp lệ theo cấu trúc Golden Layout. Nếu không, sử dụng mặc định.
    sections: Mảng có thể rỗng, nhưng mỗi section phải có id duy nhất và fields hợp lệ.
    templates: Mảng có thể rỗng, nhưng mỗi template phải có name và sections hợp lệ.
    availableFieldTypes: Phải chứa ít nhất một loại field, với type và label hợp lệ.
    Nếu input không hợp lệ, component hiển thị thông báo lỗi (dùng mat-snack-bar) và fallback về trạng thái mặc định.
 */
export interface GoldenLayoutConfig {
  content?: LayoutContentItem[];
  settings?: {
    hasHeaders?: boolean;
    constrainDragToContainer?: boolean;
    reorderEnabled?: boolean;
    selectionEnabled?: boolean;
    popoutWholeStack?: boolean;
    blockedPopoutsThrowError?: boolean;
    closePopoutsOnUnload?: boolean;
    showPopoutIcon?: boolean;
    showMaximiseIcon?: boolean;
    showCloseIcon?: boolean;
  };
  dimensions?: {
    borderWidth?: number;
    minItemHeight?: number;
    minItemWidth?: number;
    headerHeight?: number;
    dragProxyWidth?: number;
    dragProxyHeight?: number;
  };
  labels?: {
    close?: string;
    maximise?: string;
    minimise?: string;
    popout?: string;
  };
}
export interface DynamicLayoutBuilderInput {
  layoutConfig?: GoldenLayoutConfig; // Cấu hình Golden Layout hiện tại (tùy chọn)
  sections: Section[]; // Danh sách sections hiện tại
  templates: Template[]; // Danh sách templates ngành
  availableFieldTypes: LayoutField[]; // Danh sách loại field khả dụng
}
export interface Section {
  id: string; // ID duy nhất của section
  title: string; // Tiêu đề section (ví dụ: "Thông tin cơ bản")
  fields: LayoutField[]; // Danh sách fields trong section
}
export interface Template {
  name: string; // Tên template (ví dụ: "Thời trang")
  sections: Section[]; // Danh sách sections của template
}

// Drag and drop data interface
export interface DragData {
  type: 'field-type' | 'field' | 'section';
  fieldType?: LayoutField;
  field?: LayoutField;
  section?: Section;
  sourceIndex?: number;
  sourceSection?: string;
}

// Preview data interface cho preview panel
export interface PreviewData {
  [fieldId: string]: FieldValue;
}

// API response interface cho submit layout
export interface SubmitLayoutResponse {
  success: boolean;
  id: string;
  message?: string;
  errors?: string[];
}

// Layout state interface cho Golden Layout
export interface LayoutState {
  content: LayoutContentItem[];
  settings?: {
    hasHeaders?: boolean;
    constrainDragToContainer?: boolean;
    reorderEnabled?: boolean;
    selectionEnabled?: boolean;
    popoutWholeStack?: boolean;
    blockedPopoutsThrowError?: boolean;
    closePopoutsOnUnload?: boolean;
    showPopoutIcon?: boolean;
    showMaximiseIcon?: boolean;
    showCloseIcon?: boolean;
  };
  dimensions?: {
    borderWidth?: number;
    minItemHeight?: number;
    minItemWidth?: number;
    headerHeight?: number;
    dragProxyWidth?: number;
    dragProxyHeight?: number;
  };
}

// Layout content item interface
export interface LayoutContentItem {
  type: 'row' | 'column' | 'stack' | 'component';
  content?: LayoutContentItem[];
  width?: number;
  height?: number;
  id?: string;
  isClosable?: boolean;
  reorderEnabled?: boolean;
  title?: string;
  componentName?: string;
  componentState?: Record<string, unknown> | {
    sectionId?: string;
    section?: Section;
    fieldCount?: number;
    message?: string;
  };
}

// Track by function return type
export type TrackByFunction<T> = (index: number, item: T) => string | number;
