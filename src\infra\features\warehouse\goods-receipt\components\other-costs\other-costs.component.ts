import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { OtherCostsService } from './other-costs.service';
import { SelectAdditionalCostsModalService } from '@/features/warehouse/goods-receipt/components/select-additional-costs-modal';
import { ImportAdditionalCost } from '../../models/api/goods-receipt.dto';

@Component({
  selector: 'app-other-costs',
  templateUrl: './other-costs.component.html',
  styleUrls: ['./other-costs.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    MatIconModule,
    MatBadgeModule,
    MatButtonModule,
    MatTooltipModule,
    TranslateModule
  ],
  providers: [OtherCostsService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OtherCostsComponent implements OnInit {
  /**
   * Danh sách tất cả chi phí
   */
  @Input() additionalCosts: ImportAdditionalCost[] = [];

  /**
   * Tổng tiền hàng để tính thuế tự động cho chi phí phần trăm
   */
  @Input() subTotal: number = 0;

  /**
   * Sự kiện khi danh sách chi phí thay đổi
   */
  @Output() additionalCostsChange = new EventEmitter<ImportAdditionalCost[]>();

  /**
   * Danh sách chi phí nhập khác
   */
  otherCosts: ImportAdditionalCost[] = [];

  /**
   * Tổng chi phí nhập khác
   */
  totalOtherCost: number = 0;

  /**
   * Trạng thái mở rộng của panel
   */
  isExpanded: boolean = false;

  constructor(
    private selectAdditionalCostsModalService: SelectAdditionalCostsModalService,
    private otherCostsService: OtherCostsService
  ) {}

  ngOnInit(): void {
    this.updateOtherCosts();
  }

  /**
   * Cập nhật khi input thay đổi
   */
  ngOnChanges(): void {
    this.updateOtherCosts();
  }

  /**
   * Cập nhật danh sách chi phí nhập khác
   */
  private updateOtherCosts(): void {
    // Lọc ra các chi phí nhập khác (không trả cho nhà cung cấp)
    this.otherCosts = this.additionalCosts.filter(cost => !cost.paidToSupplier);

    // Tính tổng chi phí
    this.totalOtherCost = this.otherCostsService.calculateTotalCost(this.otherCosts);
  }

  /**
   * Mở modal chọn chi phí nhập khác
   */
  async openSelectCostsDialog(): Promise<void> {
    // Lấy danh sách tất cả chi phí có thể chọn từ service
    this.otherCostsService.getAllCosts().subscribe(async allCosts => {
      const result = await this.selectAdditionalCostsModalService.open({
        items: allCosts.filter(cost => !cost.paidToSupplier), // Chỉ lấy chi phí không trả cho NCC
        current: this.otherCosts,
        subTotal: this.subTotal
      });

      if (result) {
        // Lọc ra các chi phí trả cho nhà cung cấp
        const supplierCosts = this.additionalCosts.filter(cost => cost.paidToSupplier);

        // Cập nhật danh sách chi phí
        const updatedCosts = [...supplierCosts, ...result];

        // Emit sự kiện thay đổi
        this.additionalCostsChange.emit(updatedCosts);
      }
    });
  }

  /**
   * Format số hiển thị có dấu phân cách hàng nghìn
   */
  formatNumber(value: number): string {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  /**
   * Format giá trị chi phí hiển thị
   */
  formatCostValue(cost: ImportAdditionalCost): string {
    if (cost.costValue.type === 'fixed') {
      return `${this.formatNumber(cost.costValue.value)} VND`;
    } else {
      return `${cost.costValue.value}% (${this.formatNumber((cost.costValue.value * this.subTotal) / 100)} VND)`;
    }
  }

  /**
   * Toggle trạng thái mở rộng của panel
   */
  toggleExpanded(): void {
    this.isExpanded = !this.isExpanded;
  }
}
