import { ChangeDetectionStrategy, Component, EventEmitter, forwardRef, Input, OnInit, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

/**
 * Interface cho giá trị autocomplete có id
 */
export interface AutocompleteValue {
  _id?: string;
  value: string;
}

/**
 * Component AutocompleteWithNewValue
 * Cho phép chọn giá trị từ danh sách hoặc tạo giá trị mới nếu không tìm thấy
 */
@Component({
  selector: 'app-autocomplete-with-new-value',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    TranslateModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AutocompleteWithNewValueComponent),
      multi: true
    }
  ],
  templateUrl: './autocomplete-with-new-value.component.html',
  styleUrls: ['./autocomplete-with-new-value.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AutocompleteWithNewValueComponent implements ControlValueAccessor, OnInit {
  /**
   * Danh sách gợi ý ban đầu
   */
  @Input() set options(values: AutocompleteValue[]) {
    this._options = values || [];
    this.filteredOptions.set(this._options);
  }

  /**
   * Getter cho options
   */
  get options(): AutocompleteValue[] {
    return this._options;
  }

  /**
   * Label hiển thị cho trường input
   */
  @Input() label = 'Chọn giá trị';

  /**
   * Placeholder hiển thị cho trường input
   */
  @Input() placeholder = 'Nhập giá trị';

  /**
   * Thông báo lỗi khi trường input không hợp lệ
   */
  @Input() errorMsg = '';

  /**
   * Có bắt buộc chọn giá trị hay không
   */
  @Input() required = false;

  /**
   * Sự kiện phát ra khi giá trị thay đổi
   */
  @Output() valueChange = new EventEmitter<AutocompleteValue | null>();

  /**
   * Danh sách gợi ý đã lọc
   */
  filteredOptions = signal<AutocompleteValue[]>([]);

  /**
   * Giá trị hiện tại đã chọn
   */
  selectedValue: AutocompleteValue | null = null;

  /**
   * Giá trị hiện tại của trường input
   */
  inputValue = '';

  /**
   * Danh sách gợi ý ban đầu
   */
  private _options: AutocompleteValue[] = [];

  /**
   * Hàm callback khi giá trị thay đổi
   */
  private onChange: (value: AutocompleteValue | null) => void = () => {};

  /**
   * Hàm callback khi trường input được chạm vào
   */
  private onTouched: () => void = () => {};

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    this.filter();
  }

  /**
   * Xử lý sự kiện khi chọn một giá trị từ autocomplete
   * @param event Sự kiện từ MatAutocomplete
   */
  onOptionSelected(event: MatAutocompleteSelectedEvent): void {
    const option = event.option.value as AutocompleteValue;
    this.selectedValue = option;
    this.inputValue = option.value;
    this.onChange(option);
    this.valueChange.emit(option);
  }

  /**
   * Lọc danh sách gợi ý dựa trên giá trị input
   */
  filter(): void {
    const filterValue = this.inputValue.toLowerCase();

    if (!filterValue) {
      this.filteredOptions.set(this._options);
      return;
    }

    const filtered = this._options.filter(option =>
      option.value.toLowerCase().includes(filterValue)
    );

    this.filteredOptions.set(filtered);
  }

  /**
   * Xử lý sự kiện khi mất focus
   */
  onBlur(): void {
    this.onTouched();

    if (!this.inputValue) {
      this.selectedValue = null;
      this.onChange(null);
      this.valueChange.emit(null);
      return;
    }

    // Tìm xem giá trị input có khớp với option nào không
    const existingOption = this._options.find(
      option => option.value.toLowerCase() === this.inputValue.toLowerCase()
    );

    if (existingOption) {
      // Nếu có, chọn option đó
      this.selectedValue = existingOption;
      this.inputValue = existingOption.value;
      this.onChange(existingOption);
      this.valueChange.emit(existingOption);
    } else {
      // Nếu không, tạo giá trị mới
      const newValue: AutocompleteValue = { value: this.inputValue };
      this.selectedValue = newValue;
      this.onChange(newValue);
      this.valueChange.emit(newValue);
    }
  }

  /**
   * Thiết lập giá trị từ bên ngoài
   * @param value Giá trị
   */
  writeValue(value: AutocompleteValue | null): void {
    this.selectedValue = value;
    this.inputValue = value?.value || '';
  }

  /**
   * Đăng ký hàm callback khi giá trị thay đổi
   * @param fn Hàm callback
   */
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  /**
   * Đăng ký hàm callback khi trường input được chạm vào
   * @param fn Hàm callback
   */
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  /**
   * Hiển thị tên của option
   * @param option Option
   * @returns Tên hiển thị
   */
  displayFn(option: AutocompleteValue | null): string {
    return option ? option.value : '';
  }
}
