import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@core/services/auth.service';

@Component({
  selector: 'app-logout',
  standalone: true,
  imports: [],
  templateUrl: './logout.component.html'
})
export class LogoutComponent {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  async ngOnInit() {
    const result = await this.authService.logout();
    result.subscribe({
      next: () => {
        this.router.navigate(['login'], { queryParams: { redirect: '/' } });
      }
    })
  }
}
