import { ChangeDetectionStrategy, Component } from '@angular/core';
import { OrganizationChartModule } from 'primeng/organizationchart';
import { TreeNode } from 'primeng/api';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog'; // Import DialogModule
import { PageContextBarComponent } from '@/shared/components/page-context-bar/page-context-bar.component';

@Component({
  selector: 'app-manage-departments',
  imports: [
    OrganizationChartModule,
    CommonModule,
    DialogModule,
    PageContextBarComponent
  ],
  templateUrl: './manage-departments.component.html',
  styleUrl: './manage-departments.component.scss',
  // changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageDepartmentsComponent {
  data: TreeNode[] = []; // Cây tổ chức chính (phòng ban)
  employeeTree: TreeNode[] = []; // Cây nhân viên (hiển thị trong dialog)
  displayDialog: boolean = false; // <PERSON><PERSON>m soát hiển thị dialog
  selectedDepartment: string = ''; // Lưu tên phòng ban được chọn

  // data: TreeNode[] = [
  //   {
  //     label: 'Root',
  //     children: [
  //       { label: 'Child 1' },
  //       { label: 'Child 2' }
  //     ]
  //   }
  // ];

  ngOnInit() {
    this.data = [
      {
        label: 'Ban giám đốc',
        expanded: true,
        data: {
          employeeHierarchy: [
            {
              label: 'Giám đốc Nguyễn Văn A',
              expanded: true,
              children: [
                { label: 'Phó giám đốc Trần Thị B' }
              ]
            }
          ]
        },
        children: [
          {
            label: 'Kinh doanh',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng phòng Lê Văn C',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Phạm Thị D' },
                    { label: 'Nhân viên Hoàng Văn E' }
                  ]
                }
              ]
            },
            children: [
              { label: 'Team kinh doanh 1' },
              { label: 'Team kinh doanh 2' }
            ]
          },
          {
            label: 'Chăm sóc khách hàng',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng phòng Nguyễn Thị F',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Trần Văn G' }
                  ]
                }
              ]
            },
            children: [
              { label: 'CSKH 2' }
            ]
          },
          {
            label: 'Marketing',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng phòng Lê Thị H',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Phạm Văn I' }
                  ]
                }
              ]
            }
          },
          {
            label: 'Angle Kid 1',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng nhóm Nguyễn Văn J',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Trần Thị K' }
                  ]
                }
              ]
            },
            children: [
              { label: 'Team kinh doanh Angle 1' }
            ]
          },
          {
            label: 'Angle Kid 2',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng nhóm Lê Văn L',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Phạm Thị M' }
                  ]
                }
              ]
            },
            children: [
              { label: 'Team kinh doanh Angle 2' }
            ]
          },
          {
            label: 'Angle Kid 3',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng nhóm Nguyễn Thị N',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Trần Văn O' }
                  ]
                }
              ]
            },
            children: [
              { label: 'Team kinh doanh Angle 3' }
            ]
          },
          {
            label: 'Photoshop',
            expanded: true,
            data: {
              employeeHierarchy: [
                {
                  label: 'Trưởng nhóm Lê Thị P',
                  expanded: true,
                  children: [
                    { label: 'Nhân viên Phạm Văn Q' }
                  ]
                }
              ]
            }
          }
        ]
      }
    ];
  }

  // Xử lý khi click vào node
  onNodeSelect(node: TreeNode) {
    // console.log(event); // Debug để kiểm tra click

    // const node: TreeNode = event.node;
    this.selectedDepartment = node.label as string; // Lưu tên phòng ban
    this.employeeTree = node.data?.employeeHierarchy || []; // Cập nhật cây nhân viên
    this.displayDialog = true; // Mở dialog
  }

  // Đóng dialog
  onDialogClose() {
    this.displayDialog = false;
    this.employeeTree = []; // Xóa cây nhân viên khi đóng dialog
  }
}
