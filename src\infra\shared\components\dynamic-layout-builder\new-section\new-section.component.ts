import { Component, EventEmitter, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

/**
 * Component để tạo section mới trong Dynamic Layout Builder
 * Hiển thị nút tạo section với icon và text
 */
@Component({
  selector: 'app-new-section',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './new-section.component.html',
  styleUrls: ['./new-section.component.scss']
})
export class NewSectionComponent {

  /**
   * Event được emit khi người dùng click tạo section mới
   */
  @Output() createSection = new EventEmitter<void>();

  /**
   * Signal để quản lý trạng thái đang tạo section
   */
  isCreating = signal(false);

  /**
   * X<PERSON> lý khi người dùng click nút tạo section
   */
  onCreateSection(): void {
    if (this.isCreating()) {
      return; // Tránh double click
    }

    // Set trạng thái đang tạo
    this.isCreating.set(true);

    // Emit event
    this.createSection.emit();

    // Reset trạng thái sau 500ms (giả lập thời gian tạo)
    setTimeout(() => {
      this.isCreating.set(false);
    }, 500);
  }
}
