// @forward './fonts/google-sans.scss';
@forward './fonts/metropolis.scss';

// @forward './fonts/keen-icon-duotone.scss';
@forward './fonts/keen-icon-solid.scss';
@forward './fonts/keen-icons-outline.scss';
@forward './fonts/keen-icons-filled.scss';

// @forward './fonts/bootstrap-icons.scss';
// @forward './fonts/line-awesome.scss';
@forward './fonts/fontawesome.scss';
@forward './fonts/material-icons.scss';

// @forward '../../node_modules/bootstrap/dist/css/bootstrap-reboot.min.css';
@forward '../../../../node_modules/bootstrap/dist/css/bootstrap.min.css';
@forward './component/bootstrap-expand.scss';

// @forward '../../node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css';
@forward '../../../../node_modules/ngx-toastr/toastr.css';

@use './component/animation.scss';
@use './component/metronic.scss';
@use './component/base.scss';
@use './component/toast.scss';
@use './component/swiper.scss';

@use './angular/index.scss';
@use './directive/resize-panel.scss';

