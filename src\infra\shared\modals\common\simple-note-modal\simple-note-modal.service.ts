import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { 
  SimpleNoteModalComponent, 
  SimpleNoteModalData, 
  SimpleNoteModalResult 
} from './simple-note-modal.component';

/**
 * Service để mở modal ghi chú đơn giản
 */
@Injectable({
  providedIn: 'root'
})
export class SimpleNoteModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal ghi chú đơn giản
   * @param title Tiêu đề của modal (mặc định là 'COMMON.NOTE')
   * @param note Ghi chú hiện tại (nếu có)
   * @returns Promise<string | undefined> Kết quả từ modal
   */
  async open(title: string = 'COMMON.NOTE', note: string = ''): Promise<SimpleNoteModalResult> {
    try {
      const modalConfig = {
        data: {
          title,
          note
        },
        width: '500px',
        maxWidth: '95vw'
      };
      
      const result = await this.responsiveModalService.open<
        SimpleNoteModalComponent,
        SimpleNoteModalData,
        SimpleNoteModalResult
      >(SimpleNoteModalComponent, modalConfig);
      
      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú đơn giản:', error);
      return undefined;
    }
  }
}
