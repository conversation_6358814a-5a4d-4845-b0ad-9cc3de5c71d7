// Field Filters Component Styles
app-field-filters  {
  display: block;
  height: 100%;
}
.field-filters-container {
  // Responsive design
  @media (max-width: 768px) {
    padding: 0.75rem;
  }

  h5 {
    color: #495057;
    font-weight: 600;
  }

  .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;

    &:hover {
      background-color: #6c757d;
      border-color: #6c757d;
      color: #fff;
    }
  }
}

.filters-list {
  max-height: 400px;
  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.filter-item {
  border-bottom: 1px solid #f8f9fa;
  padding-bottom: 0.5rem;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.active-filters-summary {
  border-top: 1px solid #e9ecef;
  padding-top: 0.75rem;

  small {
    display: flex;
    align-items: center;

    i {
      color: #007bff;
    }
  }
}

// Alert styles
.alert {
  border: none;
  border-radius: 0.375rem;

  &.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;

    i {
      color: #0dcaf0;
    }
  }
}

// Filter Actions Buttons
.filter-actions {
  .btn {
    min-width: 120px;
    font-weight: 500;

    i {
      font-size: 0.875rem;
    }

    &.btn-primary {
      background-color: #007bff;
      border-color: #007bff;

      &:hover:not(:disabled) {
        background-color: #0056b3;
        border-color: #0056b3;
      }

      &:disabled {
        background-color: #6c757d;
        border-color: #6c757d;
        opacity: 0.65;
        cursor: not-allowed;
      }
    }

    &.btn-outline-secondary {
      color: #6c757d;
      border-color: #6c757d;

      &:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: #fff;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .filter-actions {
    .d-flex {
      .btn {
        width: 100%;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .field-filters-container {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.5rem;

      .btn {
        align-self: flex-end;
      }
    }
  }

  .filters-list {
    max-height: 300px;
  }

  .filter-actions {
    .btn {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
    }
  }
}

// Compact mat-form-field styles - chiều cao 34px
.compact-search-field {
  ::ng-deep {
    .mat-mdc-form-field {
      height: 34px;
      line-height: 34px;

      .mat-mdc-form-field-flex {
        height: 34px;
        align-items: center;
        padding: 0 12px;
      }

      .mat-mdc-form-field-infix {
        min-height: 34px;
        padding: 0;
        border-top: none;
        display: flex;
        align-items: center;
      }

      .mat-mdc-input-element {
        height: 34px;
        line-height: 34px;
        padding: 0;
        font-size: 14px;

        &::placeholder {
          color: #6c757d;
          opacity: 0.7;
        }
      }

      // Loại bỏ label floating để tiết kiệm không gian
      .mat-mdc-form-field-label-wrapper {
        display: none;
      }

      // Điều chỉnh outline
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-width: 1px;
          border-color: #ced4da;
        }
      }

      // Hover state
      &:hover .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #adb5bd;
        }
      }

      // Focus state
      &.mat-focused .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #007bff;
          border-width: 2px;
        }
      }

      // Error state
      &.mat-form-field-invalid .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-gap,
        .mat-mdc-form-field-outline-end {
          border-color: #dc3545;
        }
      }

      // Disabled state
      &.mat-form-field-disabled {
        .mat-mdc-form-field-outline {
          .mat-mdc-form-field-outline-start,
          .mat-mdc-form-field-outline-gap,
          .mat-mdc-form-field-outline-end {
            border-color: #e9ecef;
          }
        }

        .mat-mdc-input-element {
          color: #6c757d;
          background-color: #f8f9fa;
        }
      }
    }

    // Điều chỉnh icon trong mat-form-field
    .mat-mdc-form-field-icon-prefix,
    .mat-mdc-form-field-icon-suffix {
      height: 34px;
      display: flex;
      align-items: center;

      .mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #6c757d;
      }
    }

    // Loại bỏ subscript wrapper để tiết kiệm không gian
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }
}
