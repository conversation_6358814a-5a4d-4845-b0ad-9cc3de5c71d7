import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Field } from '@domain/entities/field.entity';
import { FieldFiltersService } from './services/field-filters.service';
import { FilterChangeEvent, FieldFilter } from './models/view/field-filter-view.model';
import { FilterFieldComponent } from './filter-field/filter-field.component';
import { Subscription } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-field-filters',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FilterFieldComponent,
    MatFormFieldModule,
    MatIconModule,
    FormsModule,
    MatInputModule
  ],
  templateUrl: './field-filters.component.html',
  styleUrls: ['./field-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldFiltersComponent implements OnInit, OnDestroy {
  private subscriptions = new Subscription();

  // Input properties
  @Input() fields: Field[] = [];
  @Input() showTitle = true;
  @Input() showClearAll = true;

  // Output events
  @Output() filterChange = new EventEmitter<FilterChangeEvent>();
  @Output() filtersApplied = new EventEmitter<FieldFilter[]>();
  @Output() filtersReset = new EventEmitter<void>();

  searchInput!: string;

  // Signals để quản lý state
  readonly isInitialized = signal(false);
  readonly filters = computed(() => this.fieldFiltersService.filtersState().filters);
  readonly activeFilters = computed(() => this.fieldFiltersService.activeFilters());
  readonly hasActiveFilters = computed(() => this.fieldFiltersService.hasActiveFilters());

  // Validation signals
  readonly hasValidActiveFilters = computed(() => {
    const activeFilters = this.activeFilters();
    return activeFilters.length > 0 && activeFilters.every(filter =>
      this.validateActiveFilter(filter)
    );
  });

  readonly canApplyFilters = computed(() => {
    // Sử dụng hasActiveFilters thay vì hasValidActiveFilters để đơn giản hóa
    // Validation sẽ được thực hiện trong onApplyFilters()
    return this.hasActiveFilters();
  });

  constructor(private fieldFiltersService: FieldFiltersService) {
    // Không còn auto-emit filtersApplied trong effect
    // Chỉ emit khi user click nút "Áp dụng"
  }

  ngOnInit(): void {
    this.initializeFilters();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Khởi tạo filters từ danh sách fields
   */
  private initializeFilters(): void {
    if (this.fields && this.fields.length > 0) {
      this.fieldFiltersService.initializeFilters(this.fields);
      this.isInitialized.set(true);
    }
  }

  /**
   * Xử lý khi filter của một field thay đổi
   * @param event - Filter change event
   */
  onFilterChange(event: FilterChangeEvent): void {
    // Cập nhật service state
    this.fieldFiltersService.updateFilter(
      event.fieldId,
      event.isActive,
      event.filterValue
    );

    // Emit event ra ngoài
    this.filterChange.emit(event);
  }


  /**
   * Validate một active filter
   * @param filter - FieldFilter để validate
   * @returns true nếu filter valid
   */
  private validateActiveFilter(filter: FieldFilter): boolean {
    if (!filter.isActive || !filter.filterValue) {
      return false;
    }
    return this.fieldFiltersService.validateFilterValue(
      filter.field.type,
      filter.filterValue
    );
  }

  /**
   * Validate tất cả active filters
   * @returns true nếu tất cả active filters đều valid
   */
  private validateAllActiveFilters(): boolean {
    const activeFilters = this.activeFilters();
    if (activeFilters.length === 0) {
      return false;
    }
    return activeFilters.every(filter => this.validateActiveFilter(filter));
  }

  /**
   * Xử lý khi user click nút "Áp dụng"
   */
  onApplyFilters(): void {
    const activeFilters = this.activeFilters();
    console.log('onApplyFilters - Active filters:', activeFilters);

    // Tạm thời skip validation để test
    console.log('Skipping validation for testing...');
    this.filtersApplied.emit(activeFilters);
  }

  /**
   * Xử lý khi user click nút "Hủy" / "Reset"
   */
  onResetFilters(): void {
    this.fieldFiltersService.clearAllFilters();
    this.filtersReset.emit();
  }

  /**
   * Track function cho ngFor để tối ưu performance
   * @param _index - Index của item (không sử dụng)
   * @param filter - FieldFilter object
   * @returns Unique identifier
   */
  trackByFieldId(_index: number, filter: FieldFilter): number {
    return filter.field._id;
  }
}
