import { Injectable, inject } from "@angular/core";
import { ResponsiveModalService } from "@core/services/responsive-modal.service";
import { 
  ConfirmModalComponent, 
  ConfirmModalData, 
  ConfirmModalResult 
} from "./confirm-modal.component";

/**
 * Service để mở modal xác nhận
 */
@Injectable({
  providedIn: "root"
})
export class ConfirmModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal xác nhận
   * @param data Dữ liệu cho modal
   * @returns Promise<boolean> Kết quả từ modal (true nếu xác nhận, false nếu hủy)
   */
  async confirm(data: ConfirmModalData): Promise<ConfirmModalResult> {
    try {
      const modalConfig = {
        data,
        width: "400px",
        maxWidth: "95vw",
        disableClose: true
      };
      
      const result = await this.responsiveModalService.open<
        ConfirmModalComponent,
        ConfirmModalData,
        ConfirmModalResult
      >(ConfirmModalComponent, modalConfig);
      
      return result === true;
    } catch (error) {
      console.error("Lỗi khi mở modal xác nhận:", error);
      return false;
    }
  }
}
