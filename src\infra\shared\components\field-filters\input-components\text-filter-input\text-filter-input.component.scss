.text-filter-input {
  width: 100%;

  // Material form field customization
  ::ng-deep .mat-mdc-form-field {
    width: 100%;
    
    .mat-mdc-form-field-flex {
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      min-height: 40px;
      padding: 8px 0;
    }

    // Input field styles
    .text-filter-input-field {
      font-size: 0.875rem;
      line-height: 1.4;
      
      &:focus {
        outline: none;
      }
      
      &[type="email"] {
        text-transform: lowercase;
      }
      
      &[type="url"] {
        text-transform: lowercase;
      }
      
      &[type="tel"] {
        letter-spacing: 0.5px;
      }
    }

    // Textarea specific styles
    .text-filter-textarea {
      font-size: 0.875rem;
      line-height: 1.4;
      resize: vertical;
      min-height: 60px;
      
      &:focus {
        outline: none;
      }
    }

    // Label styles
    .mat-mdc-form-field-label {
      font-size: 0.875rem;
      color: #6c757d;
    }

    // Error styles
    .mat-mdc-form-field-error {
      font-size: 0.75rem;
      color: #dc3545;
      margin-top: 4px;
    }

    // Disabled state
    &.mat-form-field-disabled {
      .mat-mdc-form-field-flex {
        background-color: #f8f9fa;
      }
      
      .text-filter-input-field,
      .text-filter-textarea {
        color: #6c757d;
        cursor: not-allowed;
      }
    }

    // Focus state
    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        border-color: #007bff;
      }
    }

    // Invalid state
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline-thick {
        border-color: #dc3545;
      }
    }
  }
}

.no-input-message {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  text-align: center;
  
  small {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }
}

// Responsive design
@media (max-width: 576px) {
  .text-filter-input {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-infix {
        min-height: 36px;
        padding: 6px 0;
      }
      
      .text-filter-input-field,
      .text-filter-textarea {
        font-size: 0.8rem;
      }
      
      .mat-mdc-form-field-label {
        font-size: 0.8rem;
      }
    }
  }
  
  .no-input-message {
    padding: 8px 12px;
    
    small {
      font-size: 0.7rem;
    }
  }
}
