import { Routes } from '@angular/router';
import { CrmRoutes } from '@features/crm/crm-routing';
import { EMSRoutes, OrganizationRoutes } from '@features/ems/ems-routing';
import { CommunicationRoutes } from '@features/communication/communication-routing';
import { SalesRoutes } from '@features/sales/sales-routing';
import { WarehouseRoutes } from '@features/warehouse/warehouse-routing';
import { CashierRoutes } from './features/cashier/cashier-routing';
import { ProductRoutes } from './features/product/product-routing';
import { canActivateCashier, canActivateLogin } from './core/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./shared/components/test-theme/test-theme.component')
      .then((m) => m.TestThemeComponent)
  },
  {
    path: 'test',
    loadComponent: () => import('./shared/components/test-theme/test-theme.component')
      .then((m) => m.TestThemeComponent)
  },
  {
    path: 'test-layout-builder',
    loadComponent: () => import('./shared/components/dynamic-layout-builder/dynamic-layout-builder.component')
      .then((m) => m.DynamicLayoutBuilderComponent)
  },
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component')
      .then((m) => m.LoginComponent),

    canActivate: [canActivateLogin]
  },
  {
    path: 'logout',
    loadComponent: () => import('./features/auth/logout/logout.component')
      .then((m) => m.LogoutComponent),

    canActivate: [canActivateCashier]
  },
  // Đã chuyển route này vào warehouse-routing.ts với providers
  ...CashierRoutes,
  ...CrmRoutes,
  ...EMSRoutes,
  ...OrganizationRoutes,
  ...CommunicationRoutes,
  ...SalesRoutes,
  ...WarehouseRoutes,
  ...ProductRoutes
];
