import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { delay, tap } from 'rxjs/operators';

import { Section, LayoutField } from '@shared/models/view/dynamic-layout-builder.model';

/**
 * SectionService - Service quản lý logic nghiệp vụ cho Section
 * 
 * Chức năng chính:
 * - Quản lý thêm/xóa/cập nhật fields trong section
 * - Sắp xếp thứ tự fields
 * - Validation section và fields
 * - Xử lý drag & drop fields
 * - Quản lý trạng thái section
 */
@Injectable({
  providedIn: 'root'
})
export class SectionService {
  
  // BehaviorSubjects để quản lý trạng thái
  private sectionsSubject = new BehaviorSubject<Section[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  // Public observables
  public sections$ = this.sectionsSubject.asObservable();
  public isLoading$ = this.loadingSubject.asObservable();
  public error$ = this.errorSubject.asObservable();

  // Signals cho reactive state management
  public currentSections = signal<Section[]>([]);
  public isLoading = signal<boolean>(false);

  constructor() {
    // Subscribe to BehaviorSubject để cập nhật signals
    this.sections$.subscribe(sections => {
      this.currentSections.set(sections);
    });

    this.isLoading$.subscribe(loading => {
      this.isLoading.set(loading);
    });
  }

  /**
   * Tạo section mới
   */
  createSection(title?: string): Section {
    const newSection: Section = {
      id: this.generateSectionId(),
      title: title || `Section ${this.currentSections().length + 1}`,
      fields: []
    };

    const currentSections = this.sectionsSubject.value;
    this.sectionsSubject.next([...currentSections, newSection]);

    return newSection;
  }

  /**
   * Cập nhật tên section
   */
  updateSectionTitle(sectionId: string, newTitle: string): Observable<boolean> {
    return of(true).pipe(
      delay(100), // Mô phỏng validation
      tap(() => {
        const currentSections = this.sectionsSubject.value;
        const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

        if (sectionIndex === -1) {
          this.errorSubject.next(`Section with id ${sectionId} not found`);
          return;
        }

        // Validation tên section
        if (!newTitle.trim()) {
          this.errorSubject.next('Section title cannot be empty');
          return;
        }

        // Kiểm tra tên trùng lặp
        const isDuplicate = currentSections.some((section, index) => 
          index !== sectionIndex && section.title.toLowerCase() === newTitle.trim().toLowerCase()
        );

        if (isDuplicate) {
          this.errorSubject.next('Section title already exists');
          return;
        }

        // Cập nhật tên section
        const updatedSections = [...currentSections];
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          title: newTitle.trim()
        };

        this.sectionsSubject.next(updatedSections);
        this.clearError();
      })
    );
  }

  /**
   * Xóa section
   */
  deleteSection(sectionId: string): Observable<boolean> {
    return of(true).pipe(
      delay(200), // Mô phỏng confirmation
      tap(() => {
        const currentSections = this.sectionsSubject.value;
        const filteredSections = currentSections.filter(s => s.id !== sectionId);
        
        if (filteredSections.length === currentSections.length) {
          this.errorSubject.next(`Section with id ${sectionId} not found`);
          return;
        }

        this.sectionsSubject.next(filteredSections);
        this.clearError();
      })
    );
  }

  /**
   * Thêm field vào section
   */
  addFieldToSection(sectionId: string, fieldType: LayoutField): Observable<boolean> {
    return of(true).pipe(
      delay(100),
      tap(() => {
        const currentSections = this.sectionsSubject.value;
        const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

        if (sectionIndex === -1) {
          this.errorSubject.next(`Section with id ${sectionId} not found`);
          return;
        }

        // Tạo field mới với ID duy nhất
        const newField: LayoutField = {
          ...fieldType,
          id: this.generateFieldId(),
          label: fieldType.label || `New ${fieldType.type}`,
          isRequired: false,
          order: currentSections[sectionIndex].fields.length + 1
        };

        // Cập nhật section với field mới
        const updatedSections = [...currentSections];
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          fields: [...updatedSections[sectionIndex].fields, newField]
        };

        this.sectionsSubject.next(updatedSections);
        this.clearError();
      })
    );
  }

  /**
   * Xóa field khỏi section
   */
  removeFieldFromSection(sectionId: string, fieldId: number): Observable<boolean> {
    return of(true).pipe(
      delay(100),
      tap(() => {
        const currentSections = this.sectionsSubject.value;
        const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

        if (sectionIndex === -1) {
          this.errorSubject.next(`Section with id ${sectionId} not found`);
          return;
        }

        // Xóa field khỏi section
        const updatedSections = [...currentSections];
        const originalFieldsLength = updatedSections[sectionIndex].fields.length;
        
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          fields: updatedSections[sectionIndex].fields.filter(f => f.id !== fieldId)
        };

        // Kiểm tra field có tồn tại không
        if (updatedSections[sectionIndex].fields.length === originalFieldsLength) {
          this.errorSubject.next(`Field with id ${fieldId} not found in section ${sectionId}`);
          return;
        }

        this.sectionsSubject.next(updatedSections);
        this.clearError();
      })
    );
  }

  /**
   * Cập nhật field trong section
   */
  updateFieldInSection(sectionId: string, fieldId: number, updatedField: Partial<LayoutField>): Observable<boolean> {
    return of(true).pipe(
      delay(100),
      tap(() => {
        const currentSections = this.sectionsSubject.value;
        const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

        if (sectionIndex === -1) {
          this.errorSubject.next(`Section with id ${sectionId} not found`);
          return;
        }

        const fieldIndex = currentSections[sectionIndex].fields.findIndex(f => f.id === fieldId);
        if (fieldIndex === -1) {
          this.errorSubject.next(`Field with id ${fieldId} not found in section ${sectionId}`);
          return;
        }

        // Validation field label nếu có cập nhật
        if (updatedField.label !== undefined) {
          if (!updatedField.label.trim()) {
            this.errorSubject.next('Field label cannot be empty');
            return;
          }

          // Kiểm tra label trùng lặp trong cùng section
          const isDuplicate = currentSections[sectionIndex].fields.some((field, index) => 
            index !== fieldIndex && field.label.toLowerCase() === updatedField.label!.trim().toLowerCase()
          );

          if (isDuplicate) {
            this.errorSubject.next('Field label already exists in this section');
            return;
          }
        }

        // Cập nhật field
        const updatedSections = [...currentSections];
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          fields: [
            ...updatedSections[sectionIndex].fields.slice(0, fieldIndex),
            { ...updatedSections[sectionIndex].fields[fieldIndex], ...updatedField },
            ...updatedSections[sectionIndex].fields.slice(fieldIndex + 1)
          ]
        };

        this.sectionsSubject.next(updatedSections);
        this.clearError();
      })
    );
  }

  /**
   * Sắp xếp lại thứ tự fields trong section
   */
  reorderFieldsInSection(sectionId: string, fields: LayoutField[]): Observable<boolean> {
    return of(true).pipe(
      delay(50),
      tap(() => {
        const currentSections = this.sectionsSubject.value;
        const sectionIndex = currentSections.findIndex(s => s.id === sectionId);

        if (sectionIndex === -1) {
          this.errorSubject.next(`Section with id ${sectionId} not found`);
          return;
        }

        // Cập nhật order cho các fields
        const reorderedFields = fields.map((field, index) => ({
          ...field,
          order: index + 1
        }));

        // Cập nhật section với fields đã sắp xếp lại
        const updatedSections = [...currentSections];
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          fields: reorderedFields
        };

        this.sectionsSubject.next(updatedSections);
        this.clearError();
      })
    );
  }

  /**
   * Toggle required status của field
   */
  toggleFieldRequired(sectionId: string, fieldId: number, isRequired: boolean): Observable<boolean> {
    return this.updateFieldInSection(sectionId, fieldId, { isRequired });
  }

  /**
   * Validate section
   */
  validateSection(section: Section): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Kiểm tra tên section
    if (!section.title.trim()) {
      errors.push('Section title is required');
    }

    // Kiểm tra fields
    if (section.fields.length === 0) {
      errors.push('Section must have at least one field');
    }

    // Kiểm tra field labels trùng lặp
    const fieldLabels = section.fields.map(f => f.label.toLowerCase());
    const duplicateLabels = fieldLabels.filter((label, index) => fieldLabels.indexOf(label) !== index);
    if (duplicateLabels.length > 0) {
      errors.push('Duplicate field labels found');
    }

    // Kiểm tra required fields có label hợp lệ
    const requiredFieldsWithoutLabel = section.fields.filter(f => f.isRequired && !f.label.trim());
    if (requiredFieldsWithoutLabel.length > 0) {
      errors.push('Required fields must have labels');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Lấy section theo ID
   */
  getSectionById(sectionId: string): Section | null {
    return this.currentSections().find(s => s.id === sectionId) || null;
  }

  /**
   * Cập nhật danh sách sections
   */
  updateSections(sections: Section[]): void {
    this.sectionsSubject.next(sections);
  }

  /**
   * Tạo ID duy nhất cho section
   */
  private generateSectionId(): string {
    return 'section_' + Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Tạo ID duy nhất cho field
   */
  private generateFieldId(): number {
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.errorSubject.next(null);
  }

  /**
   * Get current error
   */
  getCurrentError(): string | null {
    return this.errorSubject.value;
  }
}
