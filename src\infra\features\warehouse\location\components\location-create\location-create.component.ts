import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { LocationFormComponent } from '../location-form/location-form.component';
import { LocationService } from '../../services/location.service';
import { LocationFormData } from '../../models/api/location.dto';

@Component({
  selector: 'app-location-create',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    TranslateModule,
    LocationFormComponent
  ],
  templateUrl: './location-create.component.html',
  styleUrls: ['./location-create.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationCreateComponent implements OnInit {
  editMode = false;
  locationId: string | null = null;
  parentId: string | null = null;
  currentLevel = 1;
  locationData: LocationFormData | null = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private locationService: LocationService
  ) {
    // Dependencies injection constructor
  }

  ngOnInit(): void {
    // Kiểm tra xem đang ở mode edit hay create
    this.route.paramMap.subscribe(params => {
      this.locationId = params.get('id');
      this.editMode = !!this.locationId;

      // Lấy parent ID từ query param nếu có
      this.route.queryParamMap.subscribe(queryParams => {
        this.parentId = queryParams.get('parentId');
        const levelParam = queryParams.get('level');
        if (levelParam) {
          this.currentLevel = parseInt(levelParam, 10);
        }
      });

      // Nếu đang ở mode edit thì load dữ liệu location
      if (this.editMode && this.locationId) {
        this.loadLocationDetails();
      }
    });
  }

  loadLocationDetails(): void {
    if (!this.locationId) return;

    this.locationService.getLocationById(this.locationId).subscribe(location => {
      if (location) {
        this.locationData = {
          name: location.name,
          code: location.code,
          type: location.type,
          parentId: location.parentId,
          capacity: location.capacity,
          dimensions: location.dimensions,
          status: location.status,
          autoGenerateCode: false,
          quantity: 1
        };
      }
    });
  }

  onFormSubmit(formData: LocationFormData): void {
    if (this.editMode && this.locationId) {
      this.locationService.updateLocation(this.locationId, formData).subscribe({
        next: () => {
          this.router.navigate(['/warehouse/location/list']);
        },
        error: (error) => {
          console.error('Error updating location:', error);
        }
      });
    } else {
      this.locationService.createLocation(formData).subscribe({
        next: () => {
          this.router.navigate(['/warehouse/location/list']);
        },
        error: (error) => {
          console.error('Error creating location:', error);
        }
      });
    }
  }

  onFormCancel(): void {
    this.router.navigate(['/warehouse/location/list']);
  }
}
