import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import { AdditionalCostModalData } from '../../models/view/goods-receipt.view-model';

@Component({
  selector: 'app-additional-cost-modal',
  templateUrl: './additional-cost-modal.component.html',
  styleUrls: ['./additional-cost-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatRadioModule,
    MatCheckboxModule,
    MatTooltipModule,
    TranslateModule
  ]
})
export class AdditionalCostModalComponent implements OnInit {
  // Form group để quản lý form
  form: FormGroup;

  // Biến để hiển thị tiêu đề modal
  modalTitle: string;

  // Biến để theo dõi nếu user đã nhập số tiền thuế thủ công
  manualTaxAmount = false;

  // Dữ liệu đầu vào cho modal
  data: AdditionalCostModalData;

  constructor(
    private fb: FormBuilder,
    @Optional() private dialogRef?: MatDialogRef<AdditionalCostModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<AdditionalCostModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: AdditionalCostModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: AdditionalCostModalData
  ) {
    // Kết hợp dữ liệu từ dialog hoặc bottom sheet
    this.data = this.dialogData || this.bottomSheetData || { cost: undefined, subTotal: 0 };

    // Xác định tiêu đề modal dựa vào dữ liệu đầu vào
    this.modalTitle = this.data.cost ? 'WAREHOUSE.ADDITIONAL_COST.EDIT_TITLE' : 'WAREHOUSE.ADDITIONAL_COST.ADD_TITLE';

    // Khởi tạo form với các giá trị mặc định hoặc từ dữ liệu đầu vào
    this.form = this.fb.group({
      name: [this.data.cost?.name || '', [Validators.required, Validators.maxLength(100)]],
      costValue: this.fb.group({
        type: [this.data.cost?.costValue?.type || 'fixed', Validators.required],
        value: [this.data.cost?.costValue?.value || 0, [Validators.required, Validators.min(0)]]
      }),
      paidToSupplier: [this.data.cost?.paidToSupplier || false],
      allocateToItems: [this.data.cost?.allocateToItems !== undefined ? this.data.cost.allocateToItems : true],
      tax: this.fb.group({
        rate: [this.data.cost?.tax?.rate || 0, [Validators.min(0), Validators.max(100)]],
        amount: [this.data.cost?.tax?.amount || 0, [Validators.min(0)]]
      })
    });
  }

  ngOnInit(): void {
    // Đăng ký theo dõi sự thay đổi của loại chi phí và giá trị để tính toán thuế tự động
    this.form.get('costValue')?.valueChanges.subscribe(() => {
      if (!this.manualTaxAmount) {
        this.updateTaxAmount();
      }
    });

    this.form.get('tax.rate')?.valueChanges.subscribe(() => {
      if (!this.manualTaxAmount) {
        this.updateTaxAmount();
      }
    });

    // Đánh dấu khi người dùng nhập số tiền thuế thủ công
    this.form.get('tax.amount')?.valueChanges.subscribe(value => {
      if (value !== this.calculateTaxAmount()) {
        this.manualTaxAmount = true;
      }
    });
  }

  /**
   * Cập nhật số tiền thuế tự động dựa trên tỷ lệ thuế và giá trị chi phí
   */
  updateTaxAmount(): void {
    const taxAmount = this.calculateTaxAmount();
    if (taxAmount !== null) {
      this.form.get('tax.amount')?.setValue(taxAmount, { emitEvent: false });
      this.manualTaxAmount = false;
    }
  }

  /**
   * Tính toán số tiền thuế dựa trên loại chi phí, giá trị chi phí và tỷ lệ thuế
   * @returns Số tiền thuế hoặc null nếu không đủ dữ liệu để tính
   */
  calculateTaxAmount(): number | null {
    const costType = this.form.get('costValue.type')?.value;
    const costValue = this.form.get('costValue.value')?.value;
    const taxRate = this.form.get('tax.rate')?.value;

    if (costValue === null || costValue === undefined || taxRate === null || taxRate === undefined) {
      return null;
    }

    if (costType === 'fixed') {
      return Number(((costValue * taxRate) / 100).toFixed(0));
    } else if (costType === 'percentage' && this.data.subTotal) {
      // Nếu là phần trăm, tính dựa trên tổng tiền hàng và tỷ lệ
      const costAmount = (costValue * this.data.subTotal) / 100;
      return Number(((costAmount * taxRate) / 100).toFixed(0));
    }

    return null;
  }

  /**
   * Xử lý khi người dùng nhấn Lưu, tạo đối tượng ImportAdditionalCost từ form
   */
  onSave(): void {
    if (this.form.invalid) {
      return;
    }

    const formValue = this.form.value;

    // Tạo đối tượng ImportAdditionalCost từ giá trị form
    const result: ImportAdditionalCost = {
      _id: this.data.cost?._id || '',
      name: formValue.name,
      costValue: {
        type: formValue.costValue.type,
        value: formValue.costValue.value
      },
      paidToSupplier: formValue.paidToSupplier,
      allocateToItems: formValue.allocateToItems,
      isActive: true,
      autoAddToPurchaseOrder: false,
      refundOnReturn: false
    };

    // Thêm thông tin thuế nếu có
    if (formValue.tax?.rate || formValue.tax?.amount) {
      result.tax = {
        rate: formValue.tax.rate || 0,
        amount: formValue.tax.amount || 0
      };
    }

    // Đóng dialog hoặc bottom sheet và trả về kết quả
    this.close(result);
  }

  /**
   * Hủy bỏ modal
   */
  onCancel(): void {
    this.close();
  }

  /**
   * Đóng modal và trả về kết quả
   */
  private close(result?: ImportAdditionalCost): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  /**
   * Lấy đơn vị của chi phí dựa vào loại chi phí
   */
  getCostValueUnit(): string {
    return this.form.get('costValue.type')?.value === 'fixed' ? 'VND' : '%';
  }
}
