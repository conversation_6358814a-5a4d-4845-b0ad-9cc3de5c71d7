import { ElementRef, Injectable, signal } from '@angular/core';
import { Router, RoutesRecognized } from '@angular/router';
import { UpdateDataStore } from '@core/store/update_data.store';
import { InitDataStore } from '@core/store/init_data.store';
import { PosUpdateData, APP_CONST } from 'salehub_shared_contracts';
import { Navigation } from '@config/navigation.config';
import { EventEmitter } from '@angular/core';
import { NavigationService } from '../navigation.service';
import { ViewportService } from '@/core/services/viewport.service';
import { MatBottomSheet, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { AppNavigationDrawerComponent } from './app-navigation-drawer.component';
import { RouterEventService } from '@/core/services/router_events.service';
import { Subject, take } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AppNavigationDrawerService {
  static ANIMATION_TIME_MS = 400;

  // Signals
  updateData!: ReturnType<typeof signal<PosUpdateData>>;
  iconAnimating = signal<string>(''); // Track if icon is currently animating

  activeNavigationBlock = signal<string>(''); // Initialize with 0
  previousActiveNavigationBlock = signal<string>(''); // Track the previously active index

  private timeoutIconAnimating!: ReturnType<typeof setTimeout>;
  private timeoutUnsetActiveNavigationBlock!: ReturnType<typeof setTimeout>;
  private timeoutPreviousActiveNavigationBlock!: ReturnType<typeof setTimeout>;
  private destroy$ = new Subject<void>();
  private bottomSheetRef!: MatBottomSheetRef<AppNavigationDrawerComponent, any>;



  // Events
  drawerOpened = new EventEmitter<boolean>();
  drawerClosed = new EventEmitter<boolean>();

  constructor(
    private updateDataStore: UpdateDataStore,
    private bottomSheet: MatBottomSheet,
    private viewportService: ViewportService,
    private navigationService: NavigationService
  ) {

  }


  /**
   * Khởi tạo dữ liệu navigation
   */
  initialize(): void {
    this.updateData = this.updateDataStore.getDataSignal();

    /**
     * phải listen ở đây
     * listen ở component khi nhấn vào link
     * url chưa chuyển, navigationOnChange chưa đc gọi thì component đã bị destroy
     * nên activeNavigationBlock sẽ không bao giờ đc set lại
     */
    this.navigationService.navigationOnChange(this.destroy$).subscribe(
      value => {
        this.activeNavigationBlock.set(value.activeNavigation?.module || '');
      }
    )
  }



  /**
   * Xử lý sự kiện khi mở accordion
   */
  onAccordionOpen(navigation: Navigation): void {
    if(this.timeoutIconAnimating) {
      clearTimeout(this.timeoutIconAnimating);
    }
    if(this.timeoutUnsetActiveNavigationBlock) {
      clearTimeout(this.timeoutUnsetActiveNavigationBlock);
    }
    if(this.timeoutPreviousActiveNavigationBlock) {
      clearTimeout(this.timeoutPreviousActiveNavigationBlock);
    }
    // Kích hoạt animation cho icon
    this.iconAnimating.set(navigation.module);
    this.timeoutIconAnimating = setTimeout(() => {
      this.iconAnimating.set('');
    }, AppNavigationDrawerService.ANIMATION_TIME_MS);

    // Nếu click vào panel đang active, không cần thay đổi activeIndex và previousActiveIndex
    if (navigation.module === this.activeNavigationBlock()) {
      return;
    }

    // Lưu index hiện tại vào previousActiveIndex
    this.previousActiveNavigationBlock.set(this.activeNavigationBlock());

    // Cập nhật activeIndex
    this.activeNavigationBlock.set(navigation.module);

    // Sau khoảng thời gian animation, reset previousActiveIndex để không render nữa
    this.timeoutPreviousActiveNavigationBlock = setTimeout(() => {
      this.previousActiveNavigationBlock.set('');
    }, AppNavigationDrawerService.ANIMATION_TIME_MS); // Thời gian animation của PrimeNG accordion
  }



  /**
   * Mở drawer và phát sự kiện
   */
  openDrawer(forceOpenBottomSheet?: boolean): void {
    if(forceOpenBottomSheet || !this.viewportService.getCurrentViewport().isDesktop) {
      this.bottomSheetRef = this.bottomSheet.open(AppNavigationDrawerComponent, {
        panelClass: ['bottom-sheet-overflow-visible', 'bottom-sheet-wide-screen']
      });

      // take(1) đảm bảo subscription tự động hủy sau khi nhận được giá trị đầu tiên
      this.bottomSheetRef.afterDismissed().pipe(take(1)).subscribe(() => this.closeDrawer());
    } else {
      this.drawerOpened.emit(true);
    }
  }

  /**
   * Đóng drawer và phát sự kiện
   */
  closeDrawer(): void {
    this.drawerClosed.emit(true);

    if(this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
      // @ts-ignore
      delete this.bottomSheetRef;
    }

    this.activeNavigationBlock.set(this.navigationService.activeNavigation()?.module || '');
  }
}
