import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { VariantFormModalComponent } from './variant-form-modal.component';
import { VariantFormModalData, VariantFormModalResult } from '../../../models/view/variant-form-modal.model';

/**
 * Service để mở modal form thuộc tính sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class VariantFormModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal form thuộc tính sản phẩm
   * @param data Dữ liệu cho modal
   * @returns Promise<VariantFormModalResult> Kết quả thuộc tính sản phẩm hoặc null nếu hủy
   */
  async open(data: VariantFormModalData = {}): Promise<VariantFormModalResult> {
    try {
      const modalConfig = {
        data,
        width: '800px',
        maxWidth: '95vw'
      };

      const result = await this.responsiveModalService.open<
        VariantFormModalComponent,
        VariantFormModalData,
        VariantFormModalResult
      >(VariantFormModalComponent, modalConfig);

      return result || null;
    } catch (error) {
      console.error('Lỗi khi mở modal form thuộc tính sản phẩm:', error);
      return null;
    }
  }
}
