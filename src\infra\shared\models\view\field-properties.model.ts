// src/domain/entities/custom-field.ts
import { Profiles } from './field-permission.model';

export interface BaseCustomField {
  _id: number;
  label: string;
  isPublic?: boolean;
  isRequired?: boolean;
  tooltip?: string;
  profilePermissions?: Profiles;
}

export type CustomField =
  | TextField
  | EmailField
  | PhoneField
  | UrlField
  | TextareaField
  | NumberField
  | DecimalField
  | CurrencyField
  | PercentField
  | DateField
  | DateTimeField
  | PicklistField
  | MultiPicklistField
  | SearchField
  | UserField
  | UploadFileField
  | UploadImageField
  | CheckboxField;

export interface TextField extends BaseCustomField {
  type: 'text';
  value: string;
  constraints?: {
    maxLength?: number;
    unique?: boolean;
  };
}

export interface EmailField extends BaseCustomField {
  type: 'email';
  value: string;
  constraints?: {
    unique?: boolean;
  };
}

export interface PhoneField extends BaseCustomField {
  type: 'phone';
  value: string;
  constraints?: {
    maxLength?: number;
    maxDigits?: number;
    unique?: boolean;
  };
}

export interface UrlField extends BaseCustomField {
  type: 'url';
  value: string;
  constraints?: {
    maxLength?: number;
    unique?: boolean;
  };
}

export interface TextareaField extends BaseCustomField {
  type: 'textarea';
  value: string;
  constraints?: {
    textType: 'small' | 'large' | 'rich';
    maxLength: number;
  };
}

export interface NumberField extends BaseCustomField {
  type: 'number';
  value: string;
  constraints?: {
    maxDigits?: number;
  };
}

export interface DecimalField extends BaseCustomField {
  type: 'decimal';
  value: string;
  constraints?: {
    maxDigits?: number;
    decimalPlaces?: number;
    useNumberSeparator?: boolean;
  };
}

export interface CurrencyField extends BaseCustomField {
  type: 'currency';
  value: string;
  constraints?: {
    maxDigits?: number;
    decimalPlaces?: number;
    rounding?: 'normal' | 'off' | 'up' | 'down';
  };
}

export interface PercentField extends BaseCustomField {
  type: 'percent';
  value: string;
  constraints?: {};
}

export interface DateField extends BaseCustomField {
  type: 'date';
  value: string;
  constraints?: {};
}

export interface DateTimeField extends BaseCustomField {
  type: 'datetime';
  value: string;
  constraints?: {};
}

export interface PicklistField extends BaseCustomField {
  type: 'picklist';
  value: string;
  constraints?: {
    picklistValues: string[];
    sortOrder?: 'input' | 'alphabetical';
    defaultValue?: string;
  };
}

export interface MultiPicklistField extends BaseCustomField {
  type: 'multi-picklist';
  value: string[];
  constraints?: {
    picklistValues: string[];
    sortOrder?: 'input' | 'alphabetical';
    defaultValue?: string[];
  };
}

export interface SearchField extends BaseCustomField {
  type: 'search';
  value: string;
  constraints?: {
    searchModule: 'sales_quotes' | 'contacts' | 'transactions';
  };
}

export interface UserField extends BaseCustomField {
  type: 'user';
  value: string | string[];
  constraints?: {
    userType: 'single' | 'multiple';
  };
}

export interface UploadFileField extends BaseCustomField {
  type: 'upload-file';
  value: string | string[];
  constraints?: {
    allowMultipleFiles?: boolean;
    maxFiles?: number;
  };
}

export interface UploadImageField extends BaseCustomField {
  type: 'upload-image';
  value: string | string[];
  constraints?: {
    maxImages: number;
  };
}

export interface CheckboxField extends BaseCustomField {
  type: 'checkbox';
  value: boolean;
  constraints?: {
    enableByDefault?: boolean;
  };
}

export interface FieldPropertiesData {
  field: CustomField;
  availableModules?: Array<{
    _id: string;
    name: string;
  }>;
}