import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2, ViewChild, forwardRef, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, FormBuilder, FormGroup, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { Warehouse } from '@mock/product_form';
import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';
import { WarehouseLocationPickerModalService } from '@/shared/modals/warehouse/warehouse-location-picker-modal';

/**
 * Interface cho dữ liệu kho hàng
 */
export interface WarehouseSectionData {
  warehouseId: string;
  supplierId?: string;
  locationId?: string;
  quantity: number;
}

/**
 * Component WarehouseSection
 * Quản lý thông tin kho hàng, nhà cung cấp, vị trí và số lượng tồn kho
 */
@Component({
  selector: 'app-warehouse-and-location-picker',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => WarehouseAndLocationPickerComponent),
      multi: true
    }
  ],
  templateUrl: './warehouse-and-location-picker.component.html',
  styleUrls: ['./warehouse-and-location-picker.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WarehouseAndLocationPickerComponent implements ControlValueAccessor, OnInit {
  /**
   * Danh sách kho hàng
   */
  @Input() warehouses: Warehouse[] = [];

  /**
   * Danh sách nhà cung cấp
   */
  @Input() suppliers: Supplier[] = [];

  /**
   * Danh sách vị trí trong kho
   */
  @Input() locations: WarehouseLocation[] = [];

  /**
   * Sự kiện khi người dùng muốn thêm nhà cung cấp mới
   */
  @Output() addSupplier = new EventEmitter<void>();

  /**
   * Sự kiện khi dữ liệu thay đổi
   */
  @Output() dataChange = new EventEmitter<WarehouseSectionData>();

  /**
   * Sự kiện khi vị trí được chọn
   */
  @Output() locationSelected = new EventEmitter<WarehouseLocation>();

  /**
   * Form dữ liệu kho hàng
   */
  warehouseForm: FormGroup;

  /**
   * Hàm callback khi giá trị thay đổi
   */
  private onChange: (value: WarehouseSectionData) => void = () => {};

  /**
   * Hàm callback khi trường input được chạm vào
   */
  private onTouched: () => void = () => {};

  /**
   * Constructor với FormBuilder để tạo form
   */
  constructor(
    private fb: FormBuilder,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private warehouseLocationPickerModalService: WarehouseLocationPickerModalService
  ) {
    this.warehouseForm = this.fb.group({
      warehouseId: ['', Validators.required],
      supplierId: [''],
      locationId: [''],
      quantity: [0, [Validators.required, Validators.min(0)]]
    });
  }

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    // Đăng ký lắng nghe sự kiện thay đổi form
    this.warehouseForm.valueChanges.subscribe(value => {
      this.onChange(value);
      this.dataChange.emit(value);
    });
  }

  /**
   * Mở modal chọn vị trí kho
   */
  async openLocationPickerModal(): Promise<void> {
    const warehouseId = this.warehouseForm.get('warehouseId')?.value;
    if (!warehouseId) {
      console.error('Vui lòng chọn kho hàng trước');
      return;
    }

    try {
      const result = await this.warehouseLocationPickerModalService.open({
        warehouseId,
        locations: this.locations,
        selectedLocationId: this.warehouseForm.get('locationId')?.value
      });

      if (result) {
        this.warehouseForm.patchValue({ locationId: result });
        // Tìm đối tượng location từ ID để emit
        const selectedLocation = this.locations.find(loc => loc._id === result);
        if (selectedLocation) {
          this.locationSelected.emit(selectedLocation);
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn vị trí kho:', error);
    }
  }

  /**
   * Xử lý khi người dùng muốn thêm nhà cung cấp mới
   */
  onAddSupplier(): void {
    this.addSupplier.emit();
  }

  /**
   * Thiết lập giá trị từ bên ngoài
   */
  writeValue(value: WarehouseSectionData): void {
    if (value) {
      this.warehouseForm.patchValue(value, { emitEvent: false });
    }
  }

  /**
   * Đăng ký hàm callback khi giá trị thay đổi
   */
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  /**
   * Đăng ký hàm callback khi trường input được chạm vào
   */
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  /**
   * Thiết lập trạng thái disabled
   */
  setDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.warehouseForm.disable();
    } else {
      this.warehouseForm.enable();
    }
  }

  /**
   * Lấy đường dẫn hiển thị cho vị trí đã chọn
   */
  getLocationPath(): string {
    const locationId = this.warehouseForm.get('locationId')?.value;
    if (!locationId) return '';
    const location = this.locations.find(loc => loc._id === locationId);
    return location ? location.path.join(' > ') : '';
  }
}
