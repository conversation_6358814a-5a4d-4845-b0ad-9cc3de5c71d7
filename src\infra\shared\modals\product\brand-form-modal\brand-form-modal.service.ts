import { Injectable } from '@angular/core';
import { MatDialogConfig } from '@angular/material/dialog';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { BrandFormModalComponent } from './brand-form-modal.component';
import { ProductBrand } from '@mock/product_form';

/**
 * Service để mở modal thêm/sửa thương hiệu
 */
@Injectable({
  providedIn: 'root'
})
export class BrandFormModalService {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal thêm/sửa thương hiệu
   * @param data Dữ liệu thương hiệu (nếu có)
   * @returns Promise với kết quả là thông tin thương hiệu sau khi lưu
   */
  async open(data?: { brand?: ProductBrand }): Promise<ProductBrand | undefined> {
    const modalConfig: MatDialogConfig<{ brand?: ProductBrand }> = {
      data: data || {},
      width: '600px',
      maxWidth: '95vw'
    };

    try {
      // Mở modal với type rõ ràng
      return await this.responsiveModalService.open<
        BrandFormModalComponent,
        { brand?: ProductBrand },
        ProductBrand
      >(BrandFormModalComponent, modalConfig);
    } catch (error) {
      console.error('Lỗi khi mở modal thương hiệu:', error);
      return undefined;
    }
  }
}
