// FieldItemComponent styles với layout 1 dòng ngang và drag & drop toàn bộ area
.field-item-container {
  width: 100%;
  cursor: grab; // Cho phép drag toàn bộ area với cursor grab
  user-select: none; // Ngăn text selection khi drag

  // Dragging state
  &.dragging {
    opacity: 0.7;
    transform: scale(0.98) rotate(2deg);
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    cursor: grabbing;
  }

  // Field row với flexbox layout 1 dòng ngang
  .field-row {
    padding: 0.75rem;
    background-color: var(--bs-white);
    border: 1px solid var(--bs-border-color);
    border-radius: 6px;
    transition: all 0.2s ease;
    min-height: 3rem; // Đảm bảo chiều cao tối thiểu

    // Hover effect - áp dụng cho toàn bộ field item
    &:hover {
      border-color: var(--bs-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px); // Subtle lift effect

      .drag-handle .drag-icon {
        color: var(--bs-primary);
      }
    }

    // Active drag state
    &:active {
      cursor: grabbing;
      transform: scale(0.99);
    }

    // Drag handle styling
    .drag-handle {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: grab;

      .drag-icon {
        font-size: 1.125rem;
        width: 1.125rem;
        height: 1.125rem;
        color: var(--bs-text-muted);
        transition: color 0.2s ease;
      }

      &:hover .drag-icon {
        color: var(--bs-primary);
      }

      &:active {
        cursor: grabbing;
      }
    }

    // Field content container
    .field-content {
      min-width: 0; // Allow text truncation
    }

    // Left side: Label section
    .field-label-section {
      flex: 1;
      min-width: 0; // Allow text truncation

      // Display mode
      .label-display {
        padding: 0.25rem 0;
        border-radius: 4px;
        transition: all 0.2s ease;

        .label-text {
          cursor: text;
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--bs-dark);
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:hover {
            background-color: rgba(var(--bs-primary-rgb), 0.05);
            color: var(--bs-primary);
          }
        }

        .required-indicator {
          color: var(--bs-danger);
          font-weight: bold;
          font-size: 0.875rem;
        }

        .edit-icon {
          font-size: 0.75rem;
          width: 0.75rem;
          height: 0.75rem;
          color: var(--bs-text-muted);
          opacity: 0;
          transition: opacity 0.2s ease;
          cursor: pointer;
        }

        &:hover {
          .edit-icon {
            opacity: 1;
          }
        }
      }

      // Edit mode
      .label-editor {
        .label-input {
          width: 100%;
          max-width: 200px; // Giới hạn chiều rộng khi edit

          ::ng-deep {
            .mat-mdc-form-field-wrapper {
              padding-bottom: 0;
            }

            .mat-mdc-text-field-wrapper {
              height: 2rem;
            }

            .mat-mdc-form-field-infix {
              min-height: 2rem;
              padding: 0.25rem 0;
            }

            .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }
          }
        }
      }
    }

    // Right side: Type và Actions
    .field-right-section {
      flex-shrink: 0; // Không cho phép shrink
    }

    // Field type section với icon và màu sắc đồng bộ với sidebar
    .field-type-section {
      .type-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        font-size: 0.6875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-radius: 4px;
        background-color: var(--bs-light);
        color: var(--bs-text-muted);
        border: 1px solid var(--bs-border-color);
        white-space: nowrap; // Không cho phép wrap text

        // Icon styling
        .type-icon {
          font-size: 0.75rem;
          width: 0.75rem;
          height: 0.75rem;

          // Icon colors đồng bộ với sidebar field selector
          &.field-icon-text { color: #4caf50; }
          &.field-icon-number,
          &.field-icon-integer,
          &.field-icon-decimal { color: #ff9800; }
          &.field-icon-email { color: #2196f3; }
          &.field-icon-phone { color: #9c27b0; }
          &.field-icon-textarea { color: #607d8b; }
          &.field-icon-date { color: #f44336; }
          &.field-icon-datetime { color: #e91e63; }
          &.field-icon-file { color: #795548; }
          &.field-icon-image { color: #ff5722; }
          &.field-icon-checkbox { color: #8bc34a; }
          &.field-icon-radio { color: #cddc39; }
          &.field-icon-select,
          &.field-icon-picklist { color: #00bcd4; }
          &.field-icon-multi-picklist { color: #00bcd4; }
          &.field-icon-url { color: #2196f3; }
          &.field-icon-percent { color: #ff9800; }
          &.field-icon-currency { color: #4caf50; }
          &.field-icon-size { color: #3f51b5; }
          &.field-icon-color { color: #e91e63; }
          &.field-icon-brand { color: #673ab7; }
          &.field-icon-category { color: #009688; }
        }

        // Type-specific background colors (subtle)
        &.type-text,
        &.type-textarea {
          background-color: rgba(76, 175, 80, 0.1);
          border-color: rgba(76, 175, 80, 0.2);
        }

        &.type-integer,
        &.type-decimal,
        &.type-number,
        &.type-percent {
          background-color: rgba(255, 152, 0, 0.1);
          border-color: rgba(255, 152, 0, 0.2);
        }

        &.type-currency {
          background-color: rgba(76, 175, 80, 0.1);
          border-color: rgba(76, 175, 80, 0.2);
        }

        &.type-date {
          background-color: rgba(244, 67, 54, 0.1);
          border-color: rgba(244, 67, 54, 0.2);
        }

        &.type-datetime {
          background-color: rgba(233, 30, 99, 0.1);
          border-color: rgba(233, 30, 99, 0.2);
        }

        &.type-email,
        &.type-url {
          background-color: rgba(33, 150, 243, 0.1);
          border-color: rgba(33, 150, 243, 0.2);
        }

        &.type-phone {
          background-color: rgba(156, 39, 176, 0.1);
          border-color: rgba(156, 39, 176, 0.2);
        }

        &.type-file {
          background-color: rgba(121, 85, 72, 0.1);
          border-color: rgba(121, 85, 72, 0.2);
        }

        &.type-image {
          background-color: rgba(255, 87, 34, 0.1);
          border-color: rgba(255, 87, 34, 0.2);
        }

        &.type-checkbox {
          background-color: rgba(139, 195, 74, 0.1);
          border-color: rgba(139, 195, 74, 0.2);
        }

        &.type-radio {
          background-color: rgba(205, 220, 57, 0.1);
          border-color: rgba(205, 220, 57, 0.2);
        }

        &.type-picklist,
        &.type-multi-picklist,
        &.type-select {
          background-color: rgba(0, 188, 212, 0.1);
          border-color: rgba(0, 188, 212, 0.2);
        }

        &.type-size {
          background-color: rgba(63, 81, 181, 0.1);
          border-color: rgba(63, 81, 181, 0.2);
        }

        &.type-color {
          background-color: rgba(233, 30, 99, 0.1);
          border-color: rgba(233, 30, 99, 0.2);
        }

        &.type-brand {
          background-color: rgba(103, 58, 183, 0.1);
          border-color: rgba(103, 58, 183, 0.2);
        }

        &.type-category {
          background-color: rgba(0, 150, 136, 0.1);
          border-color: rgba(0, 150, 136, 0.2);
        }
      }
    }

    // Actions section
    .field-actions-section {
    }
  }
}

// Menu styles
::ng-deep .field-menu {
  .delete-action {
    color: var(--bs-danger) !important;

    mat-icon {
      color: var(--bs-danger) !important;
    }
  }
}

// Responsive design cho layout 1 dòng ngang
@media (max-width: 768px) {
  .field-item-container {
    .field-row {
      padding: 0.625rem;

      .drag-handle {
        .drag-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }

      .field-label-section {
        .label-display {
          .label-text {
            font-size: 0.8125rem;
          }

          .required-indicator {
            font-size: 0.8125rem;
          }

          .edit-icon {
            font-size: 0.6875rem;
            width: 0.6875rem;
            height: 0.6875rem;
          }
        }

        .label-editor .label-input {
          max-width: 150px; // Giảm chiều rộng trên mobile
        }
      }

      .field-type-section {
        .type-badge {
          font-size: 0.625rem;
          padding: 0.1875rem 0.375rem;
        }
      }

      .field-actions-section {
        .menu-btn {
          width: 1.75rem;
          height: 1.75rem;

          mat-icon {
            font-size: 0.875rem;
            width: 0.875rem;
            height: 0.875rem;
          }
        }
      }
    }
  }
}

// Extra small screens - chuyển sang layout 2 dòng
@media (max-width: 576px) {
  .field-item-container {
    .field-row {
      padding: 0.5rem;

      .field-content {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
      }

      .field-label-section {
        .label-editor .label-input {
          max-width: 100%; // Full width trên extra small screens
        }
      }

      .field-right-section {
        justify-content: space-between;
        width: 100%;
      }

      .field-type-section {
        margin-right: 0;
      }
    }
  }
}


// SortableJS drag states
.sortable-ghost {
  .field-item-container {
    opacity: 0.4;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border: 2px dashed var(--bs-primary);

    .field-row {
      background-color: transparent;
      border: none;
    }
  }
}

.sortable-chosen {
  .field-item-container {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 999;

    .field-row {
      border-color: var(--bs-primary);
      background-color: rgba(var(--bs-primary-rgb), 0.05);
    }
  }
}

.sortable-drag {
  .field-item-container {
    opacity: 0.8;
    transform: rotate(3deg);
    cursor: grabbing;
  }
}

// Global drag feedback
:global(body.field-dragging) {
  cursor: grabbing !important;

  .field-item-container:not(.dragging) {
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}
