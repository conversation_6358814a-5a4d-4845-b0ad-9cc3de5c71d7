import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import {
  ListColumnSelectorModalComponent,
  ListColumnSelectorModalData,
  ListColumnSelectorModalResult
} from './list-column-selector-modal.component';

/**
 * Service để mở modal ghi chú đơn giản
 */
@Injectable({
  providedIn: 'root'
})
export class ListColumnSelectorModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal ghi chú đơn giản
   * @param title Tiêu đề của modal (mặc định là 'COMMON.NOTE')
   * @param note Ghi chú hiện tại (nếu có)
   * @returns Promise<string | undefined> Kết quả từ modal
   */
  async open(data: ListColumnSelectorModalData): Promise<ListColumnSelectorModalResult | undefined> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '95vw'
      };

      const result = await this.responsiveModalService.open<
        ListColumnSelectorModalComponent,
        ListColumnSelectorModalData,
        ListColumnSelectorModalResult
      >(ListColumnSelectorModalComponent, modalConfig);

      return result;
    } catch (error) {
      return undefined;
    }
  }
}
