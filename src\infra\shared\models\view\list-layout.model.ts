import { TemplateRef } from '@angular/core';

export interface Column {
  key: string;
  label: string;
  sortable?: boolean;
}

export interface Filter {
  key: string;
  label: string;
  type: 'search' | 'select' | 'custom';
  options?: { value: string; label: string }[];
  template?: TemplateRef<any>;
}

export interface SortState {
  key: string;
  direction: 'asc' | 'desc' | '';
}

export interface Pager {
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

export interface ListConfig {
  allColumns: Column[]; // Tất cả cột có thể chọn
  columns: Column[]; // Cột hiện tại (subset của allColumns)
  fixedColumns: Column[];
  filters: Filter[];
  items: any[];
  columnOrder?: string[]; // Thứ tự cột hiển thị
  pager?: Pager;
}

export interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  category: string;
}
