import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// Angular Material imports
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectChange } from '@angular/material/select';

import { Field } from '@domain/entities/field.entity';
import {
  BaseFilterInput,
  CheckboxFilterValue
} from '../base-filter-input.interface';

/**
 * Specialized component cho checkbox-based filter inputs
 * Handles: checkbox field types
 * Operators: is (với values: selected, not_selected)
 */
@Component({
  selector: 'app-checkbox-filter-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule
  ],
  templateUrl: './checkbox-filter-input.component.html',
  styleUrls: ['./checkbox-filter-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CheckboxFilterInputComponent implements BaseFilterInput, OnInit, OnDestroy {
  @Input() field!: Field;
  @Input() operator!: string;
  @Input() value?: 'selected' | 'not_selected';
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<'selected' | 'not_selected'>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Signals cho reactive state management
  readonly selectedValue = signal<'selected' | 'not_selected'>('selected');
  readonly isValid = signal<boolean>(true);

  // Checkbox value options
  readonly checkboxOptions = [
    { value: 'selected', labelKey: 'FIELD_FILTERS.CHECKBOX_VALUES.SELECTED' },
    { value: 'not_selected', labelKey: 'FIELD_FILTERS.CHECKBOX_VALUES.NOT_SELECTED' }
  ] as const;

  // Computed properties
  readonly requiresInput = computed(() => {
    // Checkbox operator 'is' luôn cần input để chọn selected/not_selected
    return this.operator === 'is';
  });

  constructor() {
    // Effect để sync với external value
    effect(() => {
      if (this.value) {
        this.selectedValue.set(this.value);
      }
    });

    // Effect để emit changes
    effect(() => {
      const valid = this.validate();
      this.isValid.set(valid);

      const currentValue = this.selectedValue();
      this.valueChange.emit(currentValue);
      this.validationChange.emit(valid);
    });
  }

  ngOnInit(): void {
    // Initialize với default value
    if (this.value) {
      this.selectedValue.set(this.value);
    } else {
      this.selectedValue.set('selected'); // Default to 'selected'
    }
  }

  ngOnDestroy(): void {
    // Cleanup nếu cần
  }

  /**
   * Handle selection change
   */
  onSelectionChange(event: MatSelectChange): void {
    this.selectedValue.set(event.value);
  }

  /**
   * Validate current selection
   */
  validate(): boolean {
    if (!this.requiresInput()) {
      return true;
    }

    // Always valid vì có default value và chỉ có 2 options
    const value = this.selectedValue();
    return value === 'selected' || value === 'not_selected';
  }

  /**
   * Reset selection
   */
  reset(): void {
    this.selectedValue.set('selected');
  }

  /**
   * Get current filter value trong format chuẩn
   */
  getCurrentFilterValue(): CheckboxFilterValue {
    return {
      operator: this.operator,
      value: this.selectedValue()
    };
  }

  /**
   * Get display text cho selected value
   */
  getDisplayText(): string {
    const value = this.selectedValue();
    const option = this.checkboxOptions.find(opt => opt.value === value);
    return option ? option.labelKey : 'FIELD_FILTERS.CHECKBOX_VALUES.SELECTED';
  }

  /**
   * Get icon cho selected value
   */
  getValueIcon(): string {
    return this.selectedValue() === 'selected' ? 'check_box' : 'check_box_outline_blank';
  }

  /**
   * Get color class cho selected value
   */
  getValueColorClass(): string {
    return this.selectedValue() === 'selected' ? 'text-success' : 'text-muted';
  }
}
