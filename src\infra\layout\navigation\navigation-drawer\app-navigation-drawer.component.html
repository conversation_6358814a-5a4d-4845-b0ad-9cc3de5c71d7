<div class="navigation-drawer-header d-flex align-items-center px-lg-3">
  <div class="d-flex align-items-center relative">
    <div
      class="app-header-btn me-3 d-none d-lg-flex"
      (click)="closeDrawer()"
      >
      <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" focusable="false" aria-hidden="true" style="pointer-events: none;">
        <path d="M21 6H3V5h18v1zm0 5H3v1h18v-1zm0 6H3v1h18v-1z"></path>
      </svg>
    </div>

    <div class="app-header-btn me-3 d-flex d-lg-none">
      <i class="ki-filled ki-pencil icon"></i>
    </div>

    <div>
      <img src="/assets/images/logo.svg">
    </div>
  </div>

  <div class="ms-auto">
    <div class="app-header-btn d-none d-lg-flex">
      <i class="ki-filled ki-pencil icon"></i>
    </div>
    <div class="app-header-btn d-flex d-lg-none" (click)="closeDrawer()">
      <i class="ki-filled ki-cross icon"></i>
    </div>
  </div>
</div>

<div class="navigation-drawer-content">
  <div class="navigation-drawer-content-inner px-lg-3" #navigationContentInner>
    <p-accordion
      styleClass="sb-accordion sb-main-accordion"
      [value]="activeNavigationBlock()"
      [multiple]="false"
      >

      @for (navigation of treeUrl; track navigation.module) {
        <p-accordion-panel
          [value]="navigation.module"
          (click)="onAccordionOpen(navigation, 'p-accordion-panel-'+navigation.module)"
          [id]="'p-accordion-panel-'+navigation.module"
          >
          <p-accordion-header>
            <ng-template #toggleicon let-active="active">
              <div class="d-flex ms-auto">
                <div>
                  @if (active) {
                    <i class="ki-filled ki-plus text-2xs text-gray-400"></i>
                  } @else {
                    <i class="ki-filled ki-minus text-2xs text-gray-400"></i>
                  }
                </div>
              </div>
            </ng-template>

            <div
              class="d-flex align-items-center menu-heading text-uppercase"
              [class.active]="activeNavigationBlock() === navigation.module"
              >
              @if(navigation.icon && navigation.icon.set) {
                <i
                  class="ki-filled text-xl menu-heading-icon me-3"
                  [ngClass]="'ki-'+navigation.icon.code"
                  [class.icon-animating]="navigation.module === iconAnimating()"
                  >
                </i>
              }

              <span class="menu-heading-text">
                {{navigation.moduleFullName | translate}}
              </span>

              @if (navigation.countUnread && navigation.countUnread(updateData())) {
                <!-- <span class="count-unread">{{navigation.countUnread(updateData())}}</span> -->
              }
              <!-- <span class="material-symbols-outlined">keyboard_arrow_up</span> -->
            </div>
          </p-accordion-header>

          <p-accordion-content>
            @if (
              navigation.module === activeNavigationBlock() ||
              navigation.module === previousActiveNavigationBlock()
              ) {
            <ng-container *ngTemplateOutlet="drawerNavigationListItems; context: { items: navigation.items }"></ng-container>
            }
          </p-accordion-content>
        </p-accordion-panel>
      }
    </p-accordion>
  </div>
</div>

<ng-template #drawerNavigationListItems let-items="items">
  <ol class="items">
    @for (item of items; track item.routerLink) {
      <li>
        @if (item.items && item.items.length > 0) {
          <!-- Nếu có items, hiển thị nested accordion -->
          <p-accordion
            styleClass="sb-accordion sb-sub-accordion"
            [multiple]="false"
            [value]="item.routerLink"
            >
            <p-accordion-panel [value]="item.routerLink">
              <p-accordion-header>
                  <ng-template #toggleicon let-active="active">
                    <div class="d-flex ms-auto">
                      <div>
                        @if (active) {
                          <i class="ki-filled ki-plus text-2xs text-gray-400"></i>
                        } @else {
                          <i class="ki-filled ki-minus text-2xs text-gray-400"></i>
                        }
                      </div>
                    </div>
                  </ng-template>

                  <div
                    class="d-flex align-items-center menu-heading text-uppercase sub-url"
                    [class.active]="isTopLevelItemActive(item)"
                    >
                    <span class="menu-heading-text">{{item.text | translate}}</span>
                    @if (item.countUnread && item.countUnread(updateData())) {
                      <!-- <span class="count-unread">{{navigation.countUnread(updateData())}}</span> -->
                    }
                    <!-- <span class="material-symbols-outlined">keyboard_arrow_up</span> -->
                  </div>
              </p-accordion-header>
              <p-accordion-content>
                <ng-container 
                  *ngTemplateOutlet="drawerNavigationListItems; context: { items: item.items }">
                </ng-container>
              </p-accordion-content>
            </p-accordion-panel>
          </p-accordion>
        } @else if(!item.hideFromNavigationTree) {
          <!-- Nếu không có items, hiển thị link -->
          <a
            class="navigation-drawer-menu-item d-flex align-items-center top-url"
            [class.active2]="isTopLevelItemActive(item)"
            matRipple
            [routerLink]="item.routerLink"
            (click)="onClickUrl()"
            >

            <span>{{ item.text | translate }}</span>
            @if (item.countUnread && item.countUnread(updateData())) {
              <span class="count-unread">{{ item.countUnread(updateData()) }}</span>
            }
          </a>
        }
      </li>
    }
  </ol>
</ng-template>
