import { NAVIGATION_BLOCK, ROUTER_LINKS, PosInitData, PosUpdateData } from 'salehub_shared_contracts'; // Điều chỉnh đường dẫn nếu cần

export const NAVIGATION_PRODUCT: Navigation = {
  module: 'products',
  moduleFullName: 'NAVIGATION_MODULE.PRODUCTS',
  moduleShortName: 'NAVIGATION_MODULE.PRODUCTS',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.PRODUCTS',
  routerLink: ROUTER_LINKS.product.list,
  icon: {
    set: 'keenicons-filled',
    code: 'cube-2'
  },
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'basket' },
      routerLink: ROUTER_LINKS.product.list,
      text: 'ROUTER_TEXT.PRODUCTS'
    },
    {
      routerLink: ROUTER_LINKS.product.category,
      text: 'ROUTER_TEXT.CLASSIFICATION',
      items: [
        {
          icon: { set: 'keenicons-filled', code: 'category' },
          routerLink: ROUTER_LINKS.product.category,
          text: 'ROUTER_TEXT.CATEGORY'
        },
        {
          icon: { set: 'keenicons-filled', code: 'tag' },
          routerLink: ROUTER_LINKS.product.brand,
          text: 'ROUTER_TEXT.BRAND'
        },
        {
          icon: { set: 'keenicons-filled', code: 'filter' },
          routerLink: ROUTER_LINKS.product.attributes,
          text: 'ROUTER_TEXT.ATTRIBUTES'
        }
      ]
    },
    {
      icon: { set: 'keenicons-filled', code: 'box' },
      routerLink: ROUTER_LINKS.product.batch,
      text: 'ROUTER_TEXT.PRODUCT_BATCH'
    },
    {
      icon: { set: 'keenicons-filled', code: 'element-plus' },
      routerLink: ROUTER_LINKS.product.layouts,
      text: 'ROUTER_TEXT.PRODUCT_LAYOUT'
    },

    /**
     * các url ẩn
     */
    {
      routerLink: ROUTER_LINKS.product.layouts_edit,
      text: 'ROUTER_TEXT.PRODUCTS',
      hideFromNavigationTree: true
    },
    {
      routerLink: ROUTER_LINKS.product.create,
      text: 'ROUTER_TEXT.PRODUCTS',
      hideFromNavigationTree: true
    },
    {
      routerLink: ROUTER_LINKS.product.edit,
      text: 'ROUTER_TEXT.PRODUCTS',
      hideFromNavigationTree: true
    },
  ]
};

export const NAVIGATION_SUPPLY_CHAIN: Navigation = {
  module: 'supply-chain',
  moduleFullName: 'NAVIGATION_MODULE.SUPPLY_CHAIN',
  moduleShortName: 'NAVIGATION_MODULE.SUPPLY_CHAIN',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.SUPPLY_CHAIN',
  icon: {
    set: 'keenicons-filled',
    code: 'delivery-2'
  },
  routerLink: ROUTER_LINKS.supply_chain.suppliers,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'user' },
      routerLink: ROUTER_LINKS.supply_chain.suppliers,
      text: 'ROUTER_TEXT.SUPPLIERS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'purchase' },
      routerLink: ROUTER_LINKS.cashier.purchases,
      text: 'ROUTER_TEXT.PURCHASE_ORDERS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'document' },
      routerLink: ROUTER_LINKS.supply_chain.supplier_contracts,
      text: 'ROUTER_TEXT.SUPPLIER_CONTRACTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart-line' },
      routerLink: ROUTER_LINKS.supply_chain.demand_forecast,
      text: 'ROUTER_TEXT.DEMAND_FORECAST'
    }
  ]
};

export const NAVIGATION_WAREHOUSE: Navigation = {
  module: 'warehouse',
  moduleFullName: 'NAVIGATION_MODULE.WAREHOUSE',
  moduleShortName: 'NAVIGATION_MODULE.WAREHOUSE',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.WAREHOUSE',
  icon: {
    set: 'keenicons-filled',
    code: 'parcel'
  },
  routerLink: ROUTER_LINKS.warehouse.inventory,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'archive' },
      routerLink: ROUTER_LINKS.warehouse.inventory,
      text: 'ROUTER_TEXT.INVENTORY'
    },
    {
      icon: { set: 'keenicons-filled', code: 'archive' },
      routerLink: ROUTER_LINKS.warehouse.goods_receipt,
      text: 'ROUTER_TEXT.GOODS_RECEIPT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'check-square' },
      routerLink: ROUTER_LINKS.warehouse.inventory_check,
      text: 'ROUTER_TEXT.INVENTORY_CHECK'
    },
    {
      icon: { set: 'keenicons-filled', code: 'arrow-left-right' },
      routerLink: ROUTER_LINKS.warehouse.transfer,
      text: 'ROUTER_TEXT.TRANSFER'
    },
    {
      icon: { set: 'keenicons-filled', code: 'history' },
      routerLink: ROUTER_LINKS.warehouse.history,
      text: 'ROUTER_TEXT.HISTORY_XNK'
    },

    {
      icon: { set: 'keenicons-filled', code: 'note' },
      routerLink: ROUTER_LINKS.warehouse.draft,
      text: 'ROUTER_TEXT.DRAFT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'package' },
      routerLink: ROUTER_LINKS.warehouse.product_package,
      text: 'ROUTER_TEXT.PRODUCT_PACKAGE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'limit' },
      routerLink: ROUTER_LINKS.warehouse.inventory_limit,
      text: 'ROUTER_TEXT.INVENTORY_LIMIT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'map' },
      routerLink: ROUTER_LINKS.warehouse.product_location.list,
      text: 'ROUTER_TEXT.PRODUCT_LOCATION',
      items: [
        { icon: { set: 'keenicons-filled', code: 'pin' }, routerLink: ROUTER_LINKS.warehouse.product_location.list, text: 'ROUTER_TEXT.PRODUCT_LOCATION' },
        { icon: { set: 'keenicons-filled', code: 'category' }, routerLink: ROUTER_LINKS.warehouse.product_location.location_list, text: 'ROUTER_TEXT.LOCATION_LIST' },
        { icon: { set: 'keenicons-filled', code: 'document' }, routerLink: ROUTER_LINKS.warehouse.product_location.invoice, text: 'ROUTER_TEXT.LOCATION_INVOICE' },
        { icon: { set: 'keenicons-filled', code: 'basket' }, routerLink: ROUTER_LINKS.warehouse.product_location.product, text: 'ROUTER_TEXT.LOCATION_PRODUCT' },
        { icon: { set: 'keenicons-filled', code: 'chart' }, routerLink: ROUTER_LINKS.warehouse.product_location.report, text: 'ROUTER_TEXT.STORAGE_AREA_REPORT' }
      ]
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart-line' },
      routerLink: ROUTER_LINKS.warehouse.forecast,
      text: 'ROUTER_TEXT.IMPORT_FORECAST'
    },
    {
      icon: { set: 'keenicons-filled', code: 'truck' },
      routerLink: ROUTER_LINKS.warehouse.internal_transport,
      text: 'ROUTER_TEXT.INTERNAL_TRANSPORT'
    }
  ]
};

export const NAVIGATION_SALES: Navigation = {
  module: 'sales',
  moduleFullName: 'NAVIGATION_MODULE.SALES',
  moduleShortName: 'NAVIGATION_MODULE.SALES',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.SALES',
  icon: {
    set: 'keenicons-filled',
    code: 'handcart'
  },
  routerLink: ROUTER_LINKS.cashier.purchases,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'search' },
      routerLink: ROUTER_LINKS.sales.invoice_search,
      text: 'ROUTER_TEXT.INVOICE_SEARCH'
    },
    {
      icon: { set: 'keenicons-filled', code: 'note' },
      routerLink: ROUTER_LINKS.sales.invoice_draft,
      text: 'ROUTER_TEXT.INVOICE_DRAFT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'shop' },
      routerLink: ROUTER_LINKS.sales.order_form,
      text: 'ROUTER_TEXT.RETAIL'
    },
    {
      icon: { set: 'keenicons-filled', code: 'cart' },
      routerLink: ROUTER_LINKS.sales.wholesale,
      text: 'ROUTER_TEXT.WHOLESALE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'truck' },
      routerLink: ROUTER_LINKS.sales.shipping,
      text: 'ROUTER_TEXT.SHIPPING'
    },
    {
      icon: { set: 'keenicons-filled', code: 'arrow-back' },
      routerLink: ROUTER_LINKS.sales.returns,
      text: 'ROUTER_TEXT.RETURNS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'gift' },
      routerLink: ROUTER_LINKS.sales.debt_gift,
      text: 'ROUTER_TEXT.DEBT_GIFT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'phone' },
      routerLink: ROUTER_LINKS.sales.old_device_import,
      text: 'ROUTER_TEXT.OLD_DEVICE_IMPORT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'price-tag' },
      routerLink: ROUTER_LINKS.sales.pricing_policy,
      text: 'ROUTER_TEXT.PRICING_POLICY'
    },
    {
      icon: { set: 'keenicons-filled', code: 'discount' },
      routerLink: ROUTER_LINKS.sales.promotions.discounts,
      text: 'ROUTER_TEXT.PROMOTIONS',
      items: [
        { icon: { set: 'keenicons-filled', code: 'percentage' }, routerLink: ROUTER_LINKS.sales.promotions.discounts, text: 'ROUTER_TEXT.DISCOUNTS' },
        { icon: { set: 'keenicons-filled', code: 'star' }, routerLink: ROUTER_LINKS.sales.promotions.points, text: 'ROUTER_TEXT.POINTS' },
        { icon: { set: 'keenicons-filled', code: 'coupon' }, routerLink: ROUTER_LINKS.sales.promotions.coupons, text: 'ROUTER_TEXT.COUPONS' },
        { icon: { set: 'keenicons-filled', code: 'gift' }, routerLink: ROUTER_LINKS.sales.promotions.gifts, text: 'ROUTER_TEXT.GIFTS' }
      ]
    }
  ]
};

export const NAVIGATION_ORDER: Navigation = {
  module: 'orders',
  icon: {
    set: 'keenicons-filled',
    code: 'cheque'
  },
  routerLink: ROUTER_LINKS.orders.list,
  moduleFullName: 'NAVIGATION_MODULE.ORDERS',
  moduleShortName: 'NAVIGATION_MODULE.ORDERS',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.ORDERS',
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'basket' },
      routerLink: ROUTER_LINKS.orders.list,
      text: 'ROUTER_TEXT.ORDERS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'copy' },
      routerLink: ROUTER_LINKS.orders.duplicate,
      text: 'ROUTER_TEXT.DUPLICATE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'shop' },
      routerLink: ROUTER_LINKS.orders.ecommerce,
      text: 'ROUTER_TEXT.ECOMMERCE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'package' },
      routerLink: ROUTER_LINKS.orders.packing,
      text: 'ROUTER_TEXT.PACKING'
    },
    {
      icon: { set: 'keenicons-filled', code: 'handshake' },
      routerLink: ROUTER_LINKS.orders.handover,
      text: 'ROUTER_TEXT.HANDOVER'
    },
    {
      icon: { set: 'keenicons-filled', code: 'message-question' },
      routerLink: ROUTER_LINKS.orders.complaints,
      text: 'ROUTER_TEXT.COMPLAINTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'trash' },
      routerLink: ROUTER_LINKS.orders.deleted,
      text: 'ROUTER_TEXT.DELETED'
    },
    {
      icon: { set: 'keenicons-filled', code: 'check-square' },
      routerLink: ROUTER_LINKS.orders.reconciliation.cod,
      text: 'ROUTER_TEXT.RECONCILIATION',
      items: [
        { icon: { set: 'keenicons-filled', code: 'wallet' }, routerLink: ROUTER_LINKS.orders.reconciliation.cod, text: 'ROUTER_TEXT.COD_PAYMENT' },
        { icon: { set: 'keenicons-filled', code: 'truck' }, routerLink: ROUTER_LINKS.orders.reconciliation.self_shipping, text: 'ROUTER_TEXT.SELF_SHIPPING' }
      ]
    },
    {
      icon: { set: 'keenicons-filled', code: 'funnel' },
      routerLink: ROUTER_LINKS.orders.source,
      text: 'ROUTER_TEXT.ORDER_SOURCE'
    }
  ]
};

export const NAVIGATION_CRM: Navigation = {
  module: 'customers',
  moduleFullName: 'NAVIGATION_MODULE.CRM',
  moduleShortName: 'NAVIGATION_MODULE.CRM',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.CRM',
  icon: {
    set: 'keenicons-filled',
    code: 'user-square'
  },
  routerLink: ROUTER_LINKS.customers.potential,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'star' },
      routerLink: ROUTER_LINKS.customers.potential,
      text: 'ROUTER_TEXT.POTENTIAL'
    },
    {
      icon: { set: 'keenicons-filled', code: 'handshake' },
      routerLink: ROUTER_LINKS.customers.offer,
      text: 'ROUTER_TEXT.OFFER'
    },
    {
      icon: { set: 'keenicons-filled', code: 'phone' },
      routerLink: ROUTER_LINKS.customers.contacts,
      text: 'ROUTER_TEXT.CONTACTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'gift' },
      routerLink: ROUTER_LINKS.customers.opportunities,
      text: 'ROUTER_TEXT.OPPORTUNITIES'
    },
    {
      icon: { set: 'keenicons-filled', code: 'price-tag' },
      routerLink: ROUTER_LINKS.customers.quotations,
      text: 'ROUTER_TEXT.QUOTATIONS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'funnel' },
      routerLink: ROUTER_LINKS.customers.opportunity_pool,
      text: 'ROUTER_TEXT.OPPORTUNITY_POOL'
    },
    {
      icon: { set: 'keenicons-filled', code: 'campaign' },
      routerLink: ROUTER_LINKS.customers.campaigns,
      text: 'ROUTER_TEXT.CAMPAIGNS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'user' },
      routerLink: ROUTER_LINKS.customers.list,
      text: 'ROUTER_TEXT.CUSTOMERS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'credit-card' },
      routerLink: ROUTER_LINKS.customers.card,
      text: 'ROUTER_TEXT.CUSTOMER_CARD'
    },
    {
      icon: { set: 'keenicons-filled', code: 'heart' },
      routerLink: ROUTER_LINKS.customers.care,
      text: 'ROUTER_TEXT.CUSTOMER_CARE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'level' },
      routerLink: ROUTER_LINKS.customers.levels,
      text: 'ROUTER_TEXT.LEVELS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'users' },
      routerLink: ROUTER_LINKS.customers.groups,
      text: 'ROUTER_TEXT.GROUPS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'message-text' },
      routerLink: ROUTER_LINKS.customers.care_methods,
      text: 'ROUTER_TEXT.CARE_METHODS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'question' },
      routerLink: ROUTER_LINKS.customers.care_reasons,
      text: 'ROUTER_TEXT.CARE_REASONS'
    }
  ]
};

export const NAVIGATION_COMMUNICATION: Navigation = {
  module: 'communication',
  moduleFullName: 'NAVIGATION_MODULE.COMMUNICATION',
  moduleShortName: 'NAVIGATION_MODULE.COMMUNICATION',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.COMMUNICATION',
  icon: {
    set: 'keenicons-filled',
    code: 'notification-status'
  },
  routerLink: ROUTER_LINKS.communication.conversations,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'chat' },
      routerLink: ROUTER_LINKS.communication.conversations,
      text: 'ROUTER_TEXT.CONVERSATIONS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'message-square' },
      routerLink: ROUTER_LINKS.communication.comments,
      text: 'ROUTER_TEXT.COMMENTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'star' },
      routerLink: ROUTER_LINKS.communication.reviews,
      text: 'ROUTER_TEXT.REVIEWS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'note' },
      routerLink: ROUTER_LINKS.communication.post_management,
      text: 'ROUTER_TEXT.POST_MANAGEMENT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'robot' },
      routerLink: ROUTER_LINKS.communication.chatbot_integration,
      text: 'ROUTER_TEXT.CHATBOT_INTEGRATION'
    }
  ]
};

export const NAVIGATION_FINANCE: Navigation = {
  module: 'finance',
  moduleFullName: 'NAVIGATION_MODULE.FINANCE',
  moduleShortName: 'NAVIGATION_MODULE.FINANCE',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.FINANCE',
  icon: {
    set: 'keenicons-filled',
    code: 'financial-schedule'
  },
  routerLink: ROUTER_LINKS.finance.cash,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'money' },
      routerLink: ROUTER_LINKS.finance.cash,
      text: 'ROUTER_TEXT.CASH'
    },
    {
      icon: { set: 'keenicons-filled', code: 'bank' },
      routerLink: ROUTER_LINKS.finance.bank,
      text: 'ROUTER_TEXT.BANK'
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart' },
      routerLink: ROUTER_LINKS.finance.summary,
      text: 'ROUTER_TEXT.SUMMARY'
    },
    {
      icon: { set: 'keenicons-filled', code: 'calculator' },
      routerLink: ROUTER_LINKS.finance.debt_payment,
      text: 'ROUTER_TEXT.DEBT_PAYMENT',
      items: [
        { routerLink: ROUTER_LINKS.finance.entries.list, text: 'ROUTER_TEXT.SUPPLIER_DEBT' },
        { routerLink: ROUTER_LINKS.finance.entries.list, text: 'ROUTER_TEXT.TRANSPORT_SERVICE_DEBT' },
        { routerLink: ROUTER_LINKS.finance.entries.list, text: 'ROUTER_TEXT.CUSTOMER_DEBT' }
      ]
    },
    {
      icon: { set: 'keenicons-filled', code: 'document' },
      routerLink: ROUTER_LINKS.finance.entries.list,
      text: 'ROUTER_TEXT.ENTRIES',
      items: [
        { icon: { set: 'keenicons-filled', code: 'note' }, routerLink: ROUTER_LINKS.finance.entries.list, text: 'ROUTER_TEXT.ENTRIES' },
        { icon: { set: 'keenicons-filled', code: 'arrow-up-down' }, routerLink: ROUTER_LINKS.finance.entries.counterpart, text: 'ROUTER_TEXT.COUNTERPART' },
        { icon: { set: 'keenicons-filled', code: 'calendar' }, routerLink: ROUTER_LINKS.finance.entries.installment, text: 'ROUTER_TEXT.INSTALLMENT' },
        { icon: { set: 'keenicons-filled', code: 'history' }, routerLink: ROUTER_LINKS.finance.entries.history, text: 'ROUTER_TEXT.HISTORY' }
      ]
    },
    {
      icon: { set: 'keenicons-filled', code: 'book' },
      routerLink: ROUTER_LINKS.finance.accounts,
      text: 'ROUTER_TEXT.ACCOUNTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'calendar' },
      routerLink: ROUTER_LINKS.finance.installment_service,
      text: 'ROUTER_TEXT.INSTALLMENT_SERVICE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart-line' },
      routerLink: ROUTER_LINKS.finance.financial_reports,
      text: 'ROUTER_TEXT.FINANCIAL_REPORTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'wallet' },
      routerLink: ROUTER_LINKS.finance.budget_management,
      text: 'ROUTER_TEXT.BUDGET_MANAGEMENT'
    }
  ]
};

export const NAVIGATION_HRM: Navigation = {
  module: 'hr',
  moduleFullName: 'NAVIGATION_MODULE.HR',
  moduleShortName: 'NAVIGATION_MODULE.HR',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.HR',
  icon: {
    set: 'keenicons-filled',
    code: 'badge'
  },
  routerLink: ROUTER_LINKS.hr.employees,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'user' },
      routerLink: ROUTER_LINKS.hr.employees,
      text: 'ROUTER_TEXT.EMPLOYEES'
    },
    {
      icon: { set: 'keenicons-filled', code: 'clock' },
      routerLink: ROUTER_LINKS.hr.attendance,
      text: 'ROUTER_TEXT.ATTENDANCE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'calendar' },
      routerLink: ROUTER_LINKS.hr.shifts,
      text: 'ROUTER_TEXT.SHIFTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'money' },
      routerLink: ROUTER_LINKS.hr.payroll,
      text: 'ROUTER_TEXT.PAYROLL'
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart-line' },
      routerLink: ROUTER_LINKS.hr.kpi,
      text: 'ROUTER_TEXT.KPI'
    },
    {
      icon: { set: 'keenicons-filled', code: 'briefcase' },
      routerLink: ROUTER_LINKS.hr.recruitment,
      text: 'ROUTER_TEXT.RECRUITMENT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'book' },
      routerLink: ROUTER_LINKS.hr.training,
      text: 'ROUTER_TEXT.TRAINING'
    }
  ]
};

export const NAVIGATION_ORG: Navigation = {
  module: 'interactions',
  moduleFullName: 'NAVIGATION_MODULE.ORGANIZATION',
  moduleShortName: 'NAVIGATION_MODULE.ORGANIZATION',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.ORGANIZATION',
  icon: {
    set: 'keenicons-filled',
    code: 'data'
  },
  routerLink: ROUTER_LINKS.organization.departments,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'home-1' },
      routerLink: ROUTER_LINKS.organization.departments,
      text: 'ROUTER_TEXT.DEPARTMENTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'message-square' },
      routerLink: ROUTER_LINKS.organization.comments,
      text: 'ROUTER_TEXT.PERMISSION_GROUPS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'star' },
      routerLink: ROUTER_LINKS.organization.reviews,
      text: 'ROUTER_TEXT.EMPLOYEE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'star' },
      routerLink: ROUTER_LINKS.organization.info,
      text: 'ROUTER_TEXT.COMPANY_INFO'
    }
  ]
};

export const NAVIGATION_BI: Navigation = {
  module: 'business-intelligence',
  moduleFullName: 'NAVIGATION_MODULE.BUSINESS_INTELLIGENCE',
  moduleShortName: 'NAVIGATION_MODULE.BUSINESS_INTELLIGENCE',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.BUSINESS_INTELLIGENCE',
  icon: {
    set: 'keenicons-filled',
    code: 'graph-up'
  },
  routerLink: ROUTER_LINKS.business_intelligence.overview,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'dashboard' },
      routerLink: ROUTER_LINKS.business_intelligence.overview,
      text: 'ROUTER_TEXT.OVERVIEW'
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart-line' },
      routerLink: ROUTER_LINKS.business_intelligence.reports.overview,
      text: 'ROUTER_TEXT.REPORTS',
      items: [
        { icon: { set: 'keenicons-filled', code: 'dashboard' }, routerLink: ROUTER_LINKS.business_intelligence.reports.overview, text: 'ROUTER_TEXT.OVERVIEW' },
        { icon: { set: 'keenicons-filled', code: 'money' }, routerLink: ROUTER_LINKS.business_intelligence.reports.revenue, text: 'ROUTER_TEXT.REVENUE' },
        { icon: { set: 'keenicons-filled', code: 'basket' }, routerLink: ROUTER_LINKS.business_intelligence.reports.orders, text: 'ROUTER_TEXT.ORDERS' },
        { icon: { set: 'keenicons-filled', code: 'shop' }, routerLink: ROUTER_LINKS.business_intelligence.reports.retail, text: 'ROUTER_TEXT.RETAIL_REPORT' },
        { icon: { set: 'keenicons-filled', code: 'cart' }, routerLink: ROUTER_LINKS.business_intelligence.reports.wholesale, text: 'ROUTER_TEXT.WHOLESALE_REPORT' },
        { icon: { set: 'keenicons-filled', code: 'archive' }, routerLink: ROUTER_LINKS.business_intelligence.reports.inventory, text: 'ROUTER_TEXT.INVENTORY_REPORT' },
        { icon: { set: 'keenicons-filled', code: 'box' }, routerLink: ROUTER_LINKS.business_intelligence.reports.products, text: 'ROUTER_TEXT.PRODUCTS_REPORT' },
        { icon: { set: 'keenicons-filled', code: 'users' }, routerLink: ROUTER_LINKS.business_intelligence.reports.customers, text: 'ROUTER_TEXT.CUSTOMERS_REPORT' },
        { icon: { set: 'keenicons-filled', code: 'discount' }, routerLink: ROUTER_LINKS.business_intelligence.reports.promotions, text: 'ROUTER_TEXT.PROMOTIONS_REPORT' },
        { icon: { set: 'keenicons-filled', code: 'book' }, routerLink: ROUTER_LINKS.business_intelligence.reports.accounting, text: 'ROUTER_TEXT.ACCOUNTING' }
      ]
    },
    {
      icon: { set: 'keenicons-filled', code: 'filter' },
      routerLink: ROUTER_LINKS.business_intelligence.analytics.dashboard,
      text: 'ROUTER_TEXT.ANALYTICS',
      items: [
        { icon: { set: 'keenicons-filled', code: 'dashboard' }, routerLink: ROUTER_LINKS.business_intelligence.analytics.dashboard, text: 'ROUTER_TEXT.DASHBOARD' },
        { icon: { set: 'keenicons-filled', code: 'chart-line' }, routerLink: ROUTER_LINKS.business_intelligence.analytics.revenue_forecast, text: 'ROUTER_TEXT.REVENUE_FORECAST' },
        { icon: { set: 'keenicons-filled', code: 'users' }, routerLink: ROUTER_LINKS.business_intelligence.analytics.customer_behavior, text: 'ROUTER_TEXT.CUSTOMER_BEHAVIOR' }
      ]
    }
  ]
};

export const NAVIGATION_MARKETING: Navigation = {
  module: 'marketing',
  moduleFullName: 'NAVIGATION_MODULE.MARKETING',
  moduleShortName: 'NAVIGATION_MODULE.MARKETING',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.MARKETING',
  icon: {
    set: 'keenicons-filled',
    code: 'call'
  },
  routerLink: ROUTER_LINKS.marketing.commissions,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'percentage' },
      routerLink: ROUTER_LINKS.marketing.commissions,
      text: 'ROUTER_TEXT.COMMISSIONS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'link' },
      routerLink: ROUTER_LINKS.marketing.affiliate,
      text: 'ROUTER_TEXT.AFFILIATE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'campaign' },
      routerLink: ROUTER_LINKS.marketing.campaign_management,
      text: 'ROUTER_TEXT.CAMPAIGN_MANAGEMENT'
    },
    {
      icon: { set: 'keenicons-filled', code: 'chart' },
      routerLink: ROUTER_LINKS.marketing.roi_analysis,
      text: 'ROUTER_TEXT.ROI_ANALYSIS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'megaphone' },
      routerLink: ROUTER_LINKS.marketing.advertising,
      text: 'ROUTER_TEXT.ADVERTISING'
    }
  ]
};

export const NAVIGATION_SETTINGS: Navigation = {
  module: 'settings',
  moduleFullName: 'NAVIGATION_MODULE.SETTINGS',
  moduleShortName: 'NAVIGATION_MODULE.SETTINGS',
  moduleTooltip: 'NAVIGATION_MODULE_TOOLTIP.SETTINGS',
  icon: {
    set: 'keenicons-filled',
    code: 'setting-2'
  },
  routerLink: ROUTER_LINKS.settings.general,
  items: [
    {
      icon: { set: 'keenicons-filled', code: 'setting' },
      routerLink: ROUTER_LINKS.settings.general,
      text: 'ROUTER_TEXT.GENERAL'
    },
    {
      icon: { set: 'keenicons-filled', code: 'basket' },
      routerLink: ROUTER_LINKS.settings.sales_inventory,
      text: 'ROUTER_TEXT.SALES_INVENTORY'
    },
    {
      icon: { set: 'keenicons-filled', code: 'document' },
      routerLink: ROUTER_LINKS.settings.orders,
      text: 'ROUTER_TEXT.ORDER_SETTINGS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'truck' },
      routerLink: ROUTER_LINKS.settings.shipping,
      text: 'ROUTER_TEXT.SHIPPING_SETTINGS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'printer' },
      routerLink: ROUTER_LINKS.settings.print_template,
      text: 'ROUTER_TEXT.PRINT_TEMPLATE'
    },
    {
      icon: { set: 'keenicons-filled', code: 'email' },
      routerLink: ROUTER_LINKS.settings.email,
      text: 'ROUTER_TEXT.EMAIL'
    },
    {
      icon: { set: 'keenicons-filled', code: 'message-text' },
      routerLink: ROUTER_LINKS.settings.sms,
      text: 'ROUTER_TEXT.SMS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'building' },
      routerLink: ROUTER_LINKS.settings.branches,
      text: 'ROUTER_TEXT.BRANCHES'
    },
    {
      icon: { set: 'keenicons-filled', code: 'user' },
      routerLink: ROUTER_LINKS.settings.agents,
      text: 'ROUTER_TEXT.AGENTS'
    },
    {
      icon: { set: 'keenicons-filled', code: 'calendar' },
      routerLink: ROUTER_LINKS.settings.expiration,
      text: 'ROUTER_TEXT.EXPIRATION'
    },
    {
      icon: { set: 'keenicons-filled', code: 'sync' },
      routerLink: ROUTER_LINKS.settings.ecommerce_sync,
      text: 'ROUTER_TEXT.ECOMMERCE_SYNC'
    }
  ]
};

export const NAVIGATIONS: Navigation[] = [
  NAVIGATION_SALES,
  NAVIGATION_ORDER,
  NAVIGATION_PRODUCT,
  NAVIGATION_WAREHOUSE,
  NAVIGATION_SUPPLY_CHAIN,
  NAVIGATION_COMMUNICATION,
  NAVIGATION_CRM,
  NAVIGATION_FINANCE,
  NAVIGATION_HRM,
  NAVIGATION_BI,
  NAVIGATION_ORG,
  NAVIGATION_MARKETING,
  NAVIGATION_SETTINGS
];

export type NavigationItemIcon = {
  image?: string;
  set?: 'material' | 'keenicons-filled' | 'keenicons-duotone' | 'keenicons-solid' | 'bootstrap' | 'fontawesome' | 'lineawesome';
  code?: string;
};

export type NavigationItemBase = {
  countUnread?: (d: PosUpdateData) => number;
  activateWhen?: (d: PosInitData) => boolean;
  items?: NavigationItem[];
  icon?: NavigationItemIcon;
  routerLink: string;
};

export type NavigationItem = NavigationItemBase & {
  text: string;
  hideFromNavigationTree?: boolean
};

export type Navigation = NavigationItemBase & {
  module: string;
  /**
   * chữ dài trong sidebar
   */
  moduleFullName: string,

  /**
   * chữ ngắn trên header
   * và trên mobile bottom navigation
   */
  moduleShortName: string,
  moduleTooltip: string,
  items: NavigationItem[];
  icon: NavigationItemIcon;
};
