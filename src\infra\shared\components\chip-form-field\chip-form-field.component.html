<mat-form-field class="w-100" appearance="outline">
  <mat-label>{{ label }}</mat-label>

  <mat-chip-grid #chipGrid aria-label="Nhập giá trị">
    <mat-chip-row *ngFor="let value of values()"
      (removed)="remove(value)">
      {{ value }}
      <button matChipRemove [attr.aria-label]="'Xóa ' + value">
        <mat-icon>cancel</mat-icon>
      </button>
    </mat-chip-row>
  </mat-chip-grid>

  <input
    #chipInput
    [placeholder]="placeholder"
    [matChipInputFor]="chipGrid"
    [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
    (matChipInputTokenEnd)="add($event)"
    [matAutocomplete]="auto"
    [(ngModel)]="inputValue"
    (ngModelChange)="filter()"
    (focus)="filter()"
    class="input"
  />
  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
    <mat-option *ngFor="let suggestion of filteredSuggestions()" [value]="suggestion">
      {{ suggestion }}
    </mat-option>
  </mat-autocomplete>

  <mat-error *ngIf="errorMsg">{{ errorMsg }}</mat-error>
</mat-form-field>
