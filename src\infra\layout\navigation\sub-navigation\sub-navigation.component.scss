@use '../../../shared/styles/_variable' as *;

$subNavModuleTextWidth: 100;
$subNavMobileHeight: 45;
$subNavDesktopHeight: 30;


.sub-nav {
  height: $mobileSubNavHeight+px;
  background-color: #{$bodyBackgroundColor};
  border-bottom: 1px solid #ddd;
  padding: 5px 0.75em 0;

  .swiper {
    position: relative;
    width: 100%; // <PERSON><PERSON><PERSON> bảo Swiper chiếm toàn bộ chiều rộng container
    white-space: nowrap;
    overflow: hidden !important; /* Cho phép slide tràn ra ngoài khi cuộn */
    height: $subNavMobileHeight+px;

    &.swiper-initialized {
      .sub-nav-next,
      .sub-nav-prev {
        display: block;

        &.disabled {
          display: none;
        }
      }
    }
  }
  .swiper-slide {
    width: auto !important; // Quan trọng: Để slide tự động điều chỉnh độ rộng theo nội dung
    display: inline-block;
  }
}

.sub-nav-next,
.sub-nav-prev {
  display: none;
}


.sub-nav-item {
  display: inline-flex; // Sử dụng inline-flex để icon và text thẳng hàng
  align-items: center;
  white-space: nowrap; // Ngăn text xuống dòng
  height: $subNavMobileHeight+px;


  a {
    display: block;
    height: $subNavMobileHeight+px;
    font-size: .9em;
    font-weight: 500;
    color: var(--tw-gray-700);
    padding: .9em .8em;
    margin-right: 0.8em;
    border-radius: 0.5em 0.5em 0 0;
    user-select: none;
    letter-spacing: -0.03em;
    cursor: pointer;


    &.active {
      background: #fbda5f;
      color: #183153;
      font-weight: 600;
    }
  }
}

.app-sub-nav-fixed {
  .sub-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99999;
    // background: #fbda5f;
    // background: var(--tw-gray-100);
  }

  // .sub-nav-item a {
  //   background: #dcdddd;

  //   &.active {
  //     background: #fbda5f;
  //     color: #183153;
  //   }
  // }

  .app-main {
    margin-top: $mobileSubNavHeight+px;
  }
}

.sub-nav-menu {
  .menu-item {
    a {
      &.active {
        background: #fbda5f;
        color: #183153;
        font-weight: 600;
      }
    }
  }
}

// desktop
.main.is-desktop {
  .sub-nav {
    height: $subNavDesktopHeight+px;
    border-bottom: 0;
    padding: 0;
    margin-bottom: 0;

    .swiper {
      width: calc(100% - #{$subNavModuleTextWidth}px); // Đảm bảo Swiper chiếm toàn bộ chiều rộng container
      height: $subNavDesktopHeight+px;
    }
  }

  .sub-nav-item {
    height: $subNavDesktopHeight+px;


    a {
      height: $subNavDesktopHeight+px;
      font-size: 0.9em;
      padding: .35em .7em .35em .7em;
      background: #f5f5f5;
      font-weight: 600;
      border-radius: .5em;


      &:hover {
        background: #dedede;
      }
      &.active {
        background: #fbda5f;
        color: #183153;
        font-weight: 600;
      }
    }
  }

  .sub-nav-module {
    display: block !important;
    position: relative;
    text-align: center;
    width: $subNavModuleTextWidth+px;



    &:after {
      content: '';
      position: absolute;
      top: 0;
      right: 10px;
      width: 1px;
      height: 100%;
      border-right: 1px solid #f1f1f1;
    }

    &-text {
      width: ($subNavModuleTextWidth - 10)+px;
      color: var(--tw-gray-700);
      text-transform: uppercase;
      font-size: 0.7em;
      white-space: nowrap;
      overflow: hidden;
      height: $subNavDesktopHeight+px;
      line-height: $subNavDesktopHeight+px;
    }
  }

  .sub-nav-next,
  .sub-nav-prev {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    border: none;
    cursor: pointer;
    padding: 0;
    background-color: #fff;
    display: none;

    &:before {
      content: "";
      position: absolute;
      top: 0;
      right: -50px;
      height: 100%;
      width: 50px;
      pointer-events: none;
      background: linear-gradient(to left, rgba(255, 255, 255, 0) 20%, #fff 80%);
    }

    i {
      font-size: 16px;
      height: $subNavDesktopHeight+px;
      width: $subNavDesktopHeight+px;
      line-height: $subNavDesktopHeight+px;
      // background: #f5f5f5;
      border-radius: 100%;
      color: var(--tw-gray-500);


      &:hover {
        background: #dedede !important;
        color: var(--tw-gray-700);
      }
    }
  }

  .sub-nav-next {
    right: 0;
    left: auto;

    &:before {
      right: auto;
      left: -50px;
      background: linear-gradient(to left, #fff 20%, rgba(255, 255, 255, 0) 80%);
    }
  }
}


