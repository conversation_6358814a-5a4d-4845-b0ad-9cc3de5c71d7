import { Component, Input, ViewChild, ElementRef, AfterViewInit, OnInit, HostListener, signal, ChangeDetectorRef, ChangeDetectionStrategy, OnDestroy, Output, EventEmitter, SimpleChanges, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModifierGroup, OrderItemBaseDetails, OrderItemDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import Swiper from 'swiper';
import { Navigation, Grid } from 'swiper/modules';
import { MatButtonModule } from '@angular/material/button';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { OrderItemModifierModalService } from '@/shared/modals/sales/order/order-item-modifier-modal/order-item-modifier-modal.service';
import { OrderItemVariantUnitSelectionModalService } from '@/shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { ProductSelectionService, ProductItemExtended, PageConfig, InventorySettings } from './order-product-picker.service';
import { ProductList } from 'salehub_shared_contracts/requests/shared/product';
import { BrandList, CategoryList, WarehouseList } from 'salehub_shared_contracts/requests/shared/list';
import { mockProductModifierGroupList } from '@/mock/shared/product.mock';

@Component({
  selector: 'app-product-selection',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatBottomSheetModule, TranslateModule, FormsModule, ReactiveFormsModule, MatSelectModule, MatFormFieldModule, NgxMatSelectSearchModule],
  templateUrl: './order-product-picker.component.html',
  styleUrl: './order-product-picker.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrderProductPickerComponent implements AfterViewInit, OnInit, OnDestroy, OnChanges {
  /**
   * Danh sách sản phẩm từ mockProductList
   */
  private _list: ProductList = [];

  @Input() set list(value: ProductList) {
    this._list = value;
  }

  get list(): ProductList {
    return this._list;
  }

  /**
   * Danh sách các sản phẩm đã được chọn
   */
  @Input() data: OrderItemBaseDetails[] = [];

  /**
   * Từ khóa tìm kiếm
   */
  @Input() searchTerm = '';

  /**
   * Cấu hình cho phép bán khi hết hàng
   */
  private _settings: { allowSellWhenOutOfStock: boolean } = { allowSellWhenOutOfStock: true };

  @Input() set settings(value: { allowSellWhenOutOfStock: boolean }) {
    this._settings = value;
    this.productSelectionService.updateSettings(value);
  }

  get settings(): { allowSellWhenOutOfStock: boolean } {
    return this._settings;
  }

  /**
   * Kho được chọn để hiển thị tồn kho
   */
  private _selectedWarehouseId: string | null = null;

  @Input() set selectedWarehouseId(value: string | null) {
    this._selectedWarehouseId = value;
    if (value) {
      this.productSelectionService.selectWarehouse(value);
    }
  }

  get selectedWarehouseId(): string | null {
    return this._selectedWarehouseId;
  }

  /**
   * Reference đến container của Swiper
   */
  @ViewChild('swiperContainer') swiperContainer!: ElementRef;

  /**
   * Instance của Swiper
   */
  private swiper!: Swiper;

  /**
   * Cấu hình số lượng items trên mỗi trang theo viewport
   */
  private pageConfig: PageConfig = {
    itemsPerRow: 3,
    rows: 3
  };

  private currentSlideIndex = 0;

  /**
   * Danh sách sản phẩm hiện tại (lọc hoặc đầy đủ)
   */
  private productItems: (ProductList[number] & ProductItemExtended)[] = [];

  /**
   * Trang hiện tại
   */
  currentPage = signal(0);

  /**
   * Tổng số trang
   */
  totalPages = signal(0);

  /**
   * Danh sách các trang
   */
  private pages: (ProductList[number] & ProductItemExtended)[][] = [];

  /**
   * Các trang hiển thị (prev, current, next)
   */
  displayedPages = signal<(ProductList[number] & ProductItemExtended)[][]>([]);

  /**
   * Quản lý subscription
   */
  private subscriptions: Subscription[] = [];

  /**
   * Cài đặt tồn kho
   */
  private inventorySettings: InventorySettings = { allowSellWhenOutOfStock: true };

  /**
   * Danh sách kho
   */
  @Input() warehouseList: WarehouseList = [];

  /**
   * Danh sách danh mục
   */
  @Input() categoryList: CategoryList = [];

  /**
   * Danh sách thương hiệu
   */
  @Input() brandList: BrandList = [];

  /**
   * FormControl cho search kho
   */
  warehouseFilterCtrl = new FormControl<string>('');

  /**
   * FormControl cho search danh mục
   */
  categoryFilterCtrl = new FormControl<string>('');

  /**
   * FormControl cho search thương hiệu
   */
  brandFilterCtrl = new FormControl<string>('');

  /**
   * Kho đã chọn
   */
  selectedWarehouseFilter = new FormControl<string | null>(null);

  /**
   * Danh mục đã chọn
   */
  selectedCategoryFilter = new FormControl<string | null>(null);

  /**
   * Thương hiệu đã chọn
   */
  selectedBrandFilter = new FormControl<string | null>(null);

  /**
   * Dữ liệu kho đã được lọc
   */
  filteredWarehouseList = signal<WarehouseList>([]);

  /**
   * Dữ liệu danh mục đã được lọc
   */
  filteredCategoryList = signal<CategoryList>([]);

  /**
   * Dữ liệu thương hiệu đã được lọc
   */
  filteredBrandList = signal<BrandList>([]);

  /**
   * Tiền tố tên không dấu cho kho, danh mục, thương hiệu
   */
  private nameAsciiPrefix = 'nameAscii_';

  /**
   * Lắng nghe sự kiện resize window để cập nhật layout
   */
  @HostListener('window:resize')
  onResize() {
    this.updatePageConfig();
    this.initializePagination();
    this.updateDisplayedPages();
  }

  @Output() addSelectedItems = new EventEmitter<OrderItemBaseDetails[]>();
  @Output() resetSelectionEvent = new EventEmitter<void>();

  constructor(
    private cdr: ChangeDetectorRef,
    private bottomSheet: MatBottomSheet,
    private productSelectionService: ProductSelectionService,
    private translateService: TranslateService,
    private productModifierFormModalService: OrderItemModifierModalService,
    private orderItemVariantUnitSelectionModalService: OrderItemVariantUnitSelectionModalService
  ) {
    // Khởi tạo thành phần
    this.updatePageConfig();
  }

  ngOnInit() {
    this.productSelectionService.initProductList(this._list, this.data);

    // Đăng ký lắng nghe sự thay đổi của filteredProductList
    this.subscriptions.push(
      this.productSelectionService.filteredProductList$.subscribe(products => {
        this.productItems = products;
        this.initializePagination();
        this.updateDisplayedPages();
        this.cdr.detectChanges();
      })
    );

    // Đăng ký lắng nghe sự thay đổi của cài đặt tồn kho
    this.subscriptions.push(
      this.productSelectionService.settings$.subscribe(settings => {
        this.inventorySettings = settings;
        this.cdr.detectChanges();
      })
    );

    // Lắng nghe sự thay đổi của bộ lọc kho
    this.subscriptions.push(
      this.warehouseFilterCtrl.valueChanges.subscribe(value => {
        this.filterWarehouseList(value || '');
      })
    );

    // Lắng nghe sự thay đổi của bộ lọc danh mục
    this.subscriptions.push(
      this.categoryFilterCtrl.valueChanges.subscribe(value => {
        this.filterCategoryList(value || '');
      })
    );

    // Lắng nghe sự thay đổi của bộ lọc thương hiệu
    this.subscriptions.push(
      this.brandFilterCtrl.valueChanges.subscribe(value => {
        this.filterBrandList(value || '');
      })
    );

    // Lắng nghe sự thay đổi khi chọn kho
    this.subscriptions.push(
      this.selectedWarehouseFilter.valueChanges.subscribe(value => {
        this.applyFilters();
      })
    );

    // Lắng nghe sự thay đổi khi chọn danh mục
    this.subscriptions.push(
      this.selectedCategoryFilter.valueChanges.subscribe(value => {
        this.applyFilters();
      })
    );

    // Lắng nghe sự thay đổi khi chọn thương hiệu
    this.subscriptions.push(
      this.selectedBrandFilter.valueChanges.subscribe(value => {
        this.applyFilters();
      })
    );

    // Khởi tạo danh sách được lọc
    this.initializeFilteredLists();
  }

  /**
   * Theo dõi thay đổi của các @Input
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Nếu searchTerm thay đổi, gọi onSearchChange để trigger filter products
    if (changes.searchTerm && changes.searchTerm.currentValue !== changes.searchTerm.previousValue) {
      this.onSearchChange(this.searchTerm);
    }
  }

  ngOnDestroy() {
    // Hủy đăng ký tất cả subscription khi component bị hủy
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Xử lý sự kiện khi người dùng nhập từ khóa tìm kiếm
   */
  onSearchChange(term: string) {
    this.productSelectionService.setSearchTerm(term);
  }

  /**
   * Xóa từ khóa tìm kiếm
   */
  clearSearch() {
    this.searchTerm = '';
    this.productSelectionService.setSearchTerm('');
  }

  /**
   * Tăng số lượng sản phẩm đã chọn
   * @param item Sản phẩm cần tăng số lượng
   */
  increaseQuantity(item: ProductList[number] & ProductItemExtended): void {
    this.productSelectionService.increaseQuantity(
      item.productId,
      item.variant?.variantId,
      item.unit?.unitName
    );
  }

  /**
   * Giảm số lượng sản phẩm đã chọn
   * @param item Sản phẩm cần giảm số lượng
   */
  decreaseQuantity(item: ProductList[number] & ProductItemExtended): void {
    this.productSelectionService.decreaseQuantity(
      item.productId,
      item.variant?.variantId,
      item.unit?.unitName
    );
  }

  /**
   * Lấy số lượng tồn kho của một sản phẩm
   * @param item Sản phẩm cần lấy số lượng tồn kho
   * @returns Số lượng tồn kho
   */
  getInventoryQuantity(item: ProductList[number] & ProductItemExtended): number {
    return item.inventoryQuantity !== undefined
      ? item.inventoryQuantity
      : this.productSelectionService.getInventoryQuantity(item);
  }

  /**
   * Kiểm tra xem có thể tăng số lượng cho sản phẩm không
   * @param item Sản phẩm cần kiểm tra
   * @returns true nếu có thể tăng số lượng, false nếu không
   */
  canIncreaseQuantity(item: ProductList[number] & ProductItemExtended): boolean {
    return this.productSelectionService.canIncreaseQuantity(item);
  }

  ngAfterViewInit() {
    // Khởi tạo Swiper với grid
    this.swiper = new Swiper(this.swiperContainer.nativeElement, {
      modules: [Navigation, Grid],
      slidesPerView: 1,
      initialSlide: 0,
      spaceBetween: 0,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      on: {
        slideChangeTransitionEnd: () => {
          const newIndex = this.swiper?.activeIndex;
          if(newIndex === undefined) {
            return;
          }
          const direction = (newIndex > this.currentSlideIndex || newIndex === 2) ? 'next' : 'prev';
          let slideTo = 1;
          if(this.currentPage() === 1 && direction === 'prev') {
            slideTo = 0;
          }

          this.currentSlideIndex = newIndex;

          if (direction === 'prev' && this.currentPage() > 0) {
            this.currentPage.set(this.currentPage() - 1);
            this.updateDisplayedPages();
            this.swiper.slideTo(slideTo, 0, false); // Đưa về giữa không animation
          } else if (direction === 'next' && this.currentPage() < this.totalPages() - 1) {
            this.currentPage.set(this.currentPage() + 1);
            this.updateDisplayedPages();
            this.swiper.slideTo(slideTo, 0, false); // Đưa về giữa không animation
          }
          this.swiper.update(); // Cập nhật Swiper sau khi DOM thay đổi
        }
      }
    });
  }

  /**
   * Cập nhật cấu hình số lượng items trên mỗi trang theo viewport
   */
  private updatePageConfig() {
    const width = window.innerWidth;
    if (width >= 1200) {
      this.pageConfig = { itemsPerRow: 3, rows: 3 };
    } else if (width >= 768) {
      this.pageConfig = { itemsPerRow: 2, rows: 3 };
    } else {
      this.pageConfig = { itemsPerRow: 1, rows: 3 };
    }
  }

  /**
   * Khởi tạo phân trang
   */
  private initializePagination() {
    const itemsPerPage = this.pageConfig.itemsPerRow * this.pageConfig.rows;

    // Tính tổng số trang dựa trên số lượng sản phẩm
    this.totalPages.set(Math.ceil(this.productItems.length / itemsPerPage));

    // Reset về trang đầu tiên nếu trang hiện tại lớn hơn tổng số trang
    if (this.currentPage() >= this.totalPages()) {
      this.currentPage.set(Math.max(0, this.totalPages() - 1));
    }

    // Chia danh sách sản phẩm thành các trang
    this.pages = [];
    for (let i = 0; i < this.totalPages(); i++) {
      const startIndex = i * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, this.productItems.length);
      this.pages.push(this.productItems.slice(startIndex, endIndex));
    }
  }

  /**
   * Cập nhật trang hiển thị
   */
  updateDisplayedPages() {
    if(this.totalPages() === 0) {
      this.displayedPages.set([]);
      return;
    }

    const prevPage = Math.max(0, this.currentPage() - 1);
    const nextPage = Math.min(this.totalPages() - 1, this.currentPage() + 1);

    if(this.currentPage() === 0) {
      this.displayedPages.set([
        this.pages[this.currentPage()] || [],
        this.pages[nextPage] || []
      ].filter(page => page.length > 0));
    } else if(this.currentPage() === this.totalPages() - 1) {
      this.displayedPages.set([
        this.pages[prevPage] || [],
        this.pages[this.currentPage()] || []
      ].filter(page => page.length > 0));
    } else {
      this.displayedPages.set([
        this.pages[prevPage] || [],
        this.pages[this.currentPage()] || [],
        this.pages[nextPage] || []
      ].filter(page => page.length > 0));
    }

    this.cdr.detectChanges();
  }

  /**
   * Xử lý sự kiện khi chọn một sản phẩm
   */
  onSelectItem(item: ProductList[number] & ProductItemExtended) {
    // Kiểm tra xem item có variants hoặc units không (dù là clone hay không)
    const hasVariantsOrUnits = (item.variants && item.variants.length > 0) || (item.units && item.units.length > 0);

    if (hasVariantsOrUnits) {
      // Nếu sản phẩm có variants hoặc units, mở bottom sheet để chọn
      // KHÔNG đánh dấu selected ngay lập tức, chỉ mở bottom sheet
      this.openVariantSelector(item);
      return;
    }

    // Nếu sản phẩm không có variants và units, xử lý trực tiếp
    if (!item.selected) {
      // Nếu chưa chọn, đánh dấu là đã chọn và đặt số lượng = 1
      item.selected = true;
      item.quantity = 1;
    } else {
      // Nếu đã chọn, tăng số lượng
      this.increaseQuantity(item);
    }
  }

  /**
   * Mở modal chọn variant và unit
   */
  private async openVariantSelector(productItem: ProductList[number] & ProductItemExtended) {
    // Lưu lại tên gốc của sản phẩm
    const originalName = productItem.originalName || productItem.name;

    // Không đánh dấu selected ngay lập tức
    // Chỉ lưu tên gốc để sử dụng sau này

    // Lấy thông tin variant và unit hiện tại
    const selectedVariant = productItem.variant;
    const selectedUnit = productItem.unit;

    try {
      // Mở modal chọn variant và unit sử dụng service
      const result = await this.orderItemVariantUnitSelectionModalService.open({
        variants: productItem.variants || [],
        currentValue: {
          variant: selectedVariant || {} as any,
          unit: selectedUnit || {} as any
        },
        units: productItem.units || []
      });

      if (result) {
        // Người dùng đã chọn variant/unit, đánh dấu là selected
        productItem.selected = true;
        productItem.quantity = 1;

        // Lưu lại tên gốc nếu chưa có
        if (!productItem.originalName) {
          productItem.originalName = originalName;
        }

        // Cập nhật variant nếu có
        if (result.variant) {
          productItem.variant = result.variant;
        } else {
          productItem.variant = undefined;
        }

        // Cập nhật unit nếu có
        if (result.unit) {
          productItem.unit = result.unit;

          // Cập nhật giá nếu unit có giá
          if (result.unit.price) {
            productItem.price = result.unit.price;
          }
        } else {
          productItem.unit = undefined;
        }

        // Kiểm tra xem đã có clone của sản phẩm này chưa
        const currentList = this.productSelectionService.getProductList();
        const existingCloneIndex = currentList.findIndex(item =>
          'productId' in item && item.productId === productItem.productId &&
          'isClone' in item && item.isClone === true &&
          !item.variant &&
          !item.unit &&
          !item.modifierGroups
        );

        // Nếu chưa có clone, thì tạo mới
        if (existingCloneIndex === -1) {
          // Clone sản phẩm gốc ngay bên cạnh
          const clonedProduct = this.productSelectionService.cloneProductItem({
            ...productItem,
            name: originalName,
            variant: undefined,
            unit: undefined,
            // Đặt lại giá gốc nếu unit đã thay đổi giá
            price: productItem.unit?.price ? productItem.cost : productItem.price,
            selected: false,
            quantity: 0,
            isClone: true
          }, true); // Giữ lại variants và units để clone cũng có thể được chọn

          // Thêm vào danh sách ngay bên cạnh sản phẩm đã chọn
          this.productSelectionService.addProduct(clonedProduct);
        }

        // Cập nhật lại phân trang và hiển thị
        this.initializePagination();
        this.updateDisplayedPages();
      } else {
        // Người dùng đã hủy modal, không đánh dấu selected
        productItem.selected = false;
        productItem.quantity = 0;
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn variant và unit:', error);
      productItem.selected = false;
      productItem.quantity = 0;
    }
  }

  /**
   * Mở modal chọn modifiers
   */
  async openModifiersSheet(item: ProductList[number] & ProductItemExtended): Promise<void> {
    try {
      // Lưu tên gốc của sản phẩm để sử dụng sau này
      const originalName = item.originalName || item.name;

      // Tạo dữ liệu cho OrderItemBaseDetails
      const orderItem: OrderItemBaseDetails = {
        quantity: item.quantity || 1,
        product: {
          productId: item.productId,
          cost: item.cost,
          name: originalName,
          price: item.price
        },
        modifierGroups: item.modifierGroups || []
      };

      // Thêm variant nếu có
      if (item.variant) {
        orderItem.product!.variant = item.variant;
      }

      // Thêm unit nếu có
      if (item.unit) {
        orderItem.product!.unit = item.unit;
      }

      // Mở modal chọn modifier sử dụng service
      const modifierGroups = await this.productModifierFormModalService.open({
        list: mockProductModifierGroupList, // Sử dụng mock data hoặc lấy từ API
        data: orderItem
      });

      if (modifierGroups && modifierGroups.length > 0) {
        // Đảm bảo đã chọn
        item.selected = true;
        if (!item.quantity) {
          item.quantity = 1;
        }

        // Tính tổng số modifier đã chọn
        let totalModifiers = 0;
        modifierGroups.forEach(group => {
          totalModifiers += group.modifiers?.length || 0;
        });

        // Lưu lại tên gốc nếu chưa có
        if (!item.originalName) {
          item.originalName = originalName;
        }

        // Cập nhật item với modifierGroups
        item.modifierGroups = modifierGroups;

        // Cập nhật tên hiển thị với format sử dụng i18n
        if (totalModifiers > 0) {
          item.name = this.translateService.instant('PRODUCT_SELECTION.WITH_OTHER_PRODUCTS', {
            name: originalName,
            count: totalModifiers
          });
        }

        // Kiểm tra xem đã có clone của sản phẩm này chưa
        const currentList = this.productSelectionService.getProductList();
        const existingCloneIndex = currentList.findIndex(i =>
          'productId' in i && i.productId === item.productId &&
          'isClone' in i && i.isClone === true &&
          !i.variant &&
          !i.unit &&
          !i.modifierGroups
        );

        // Nếu chưa có clone, thì tạo mới
        if (existingCloneIndex === -1) {
          // Clone sản phẩm gốc ngay bên cạnh
          const clonedProduct = this.productSelectionService.cloneProductItem({
            ...item,
            name: originalName, // Đặt lại tên gốc cho bản clone
            modifierGroups: undefined, // Bỏ modifierGroups cho bản clone
            variant: undefined,
            unit: undefined,
            selected: false, // Không chọn bản clone
            isClone: true
          }, true); // Giữ lại variants và units để clone cũng có thể được chọn

          // Thêm bản clone vào danh sách ngay bên cạnh sản phẩm gốc
          this.productSelectionService.addProduct(clonedProduct);
        }

        // Cập nhật pagination và hiển thị
        this.initializePagination();
        this.updateDisplayedPages();
      } else if (modifierGroups !== null) {
        // Người dùng đã nhấn "Thêm vào đơn" mà không chọn modifier nào
        // Vẫn đánh dấu là đã chọn
        item.selected = true;
        if (!item.quantity) {
          item.quantity = 1;
        }
      } else {
        // Người dùng đã hủy việc chọn modifier
        // Không thay đổi trạng thái của item nếu nó đã được selected trước đó
        // Nhưng nếu item chỉ mới được selected khi mở modifiers sheet, thì bỏ selected
        const hasNoExistingSelections = !item.variant && !item.unit;
        if (hasNoExistingSelections) {
          item.selected = false;
          item.quantity = 0;
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn modifier:', error);
    }
  }

  openProductForm(): void {
  }

  /**
   * Khởi tạo danh sách được lọc
   */
  private initializeFilteredLists(): void {
    // Tạo phiên bản mới của các danh sách với nameAscii
    const warehouseWithAscii = this.warehouseList.map(item => ({
      ...item,
      nameAscii: this.productSelectionService.convertToAscii(item.name)
    }));

    const categoryWithAscii = this.categoryList.map(item => ({
      ...item,
      nameAscii: this.productSelectionService.convertToAscii(item.name)
    }));

    const brandWithAscii = this.brandList.map(item => ({
      ...item,
      nameAscii: this.productSelectionService.convertToAscii(item.name)
    }));

    this.filteredWarehouseList.set(warehouseWithAscii);
    this.filteredCategoryList.set(categoryWithAscii);
    this.filteredBrandList.set(brandWithAscii);
  }

  /**
   * Lọc danh sách kho theo từ khóa
   */
  private filterWarehouseList(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredWarehouseList.set(this.warehouseList);
      return;
    }

    const asciiSearchTerm = this.productSelectionService.convertToAscii(searchTerm.toLowerCase());
    const filtered = this.warehouseList.filter(warehouse => {
      const nameAscii = (warehouse as any).nameAscii || this.productSelectionService.convertToAscii(warehouse.name);
      return nameAscii.includes(asciiSearchTerm);
    });

    this.filteredWarehouseList.set(filtered);
  }

  /**
   * Lọc danh sách danh mục theo từ khóa
   */
  private filterCategoryList(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredCategoryList.set(this.categoryList);
      return;
    }

    const asciiSearchTerm = this.productSelectionService.convertToAscii(searchTerm.toLowerCase());
    const filtered = this.categoryList.filter(category => {
      const nameAscii = (category as any).nameAscii || this.productSelectionService.convertToAscii(category.name);
      return nameAscii.includes(asciiSearchTerm);
    });

    this.filteredCategoryList.set(filtered);
  }

  /**
   * Lọc danh sách thương hiệu theo từ khóa
   */
  private filterBrandList(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBrandList.set(this.brandList);
      return;
    }

    const asciiSearchTerm = this.productSelectionService.convertToAscii(searchTerm.toLowerCase());
    const filtered = this.brandList.filter(brand => {
      const nameAscii = (brand as any).nameAscii || this.productSelectionService.convertToAscii(brand.name);
      return nameAscii.includes(asciiSearchTerm);
    });

    this.filteredBrandList.set(filtered);
  }

  /**
   * Áp dụng tất cả các bộ lọc
   */
  private applyFilters(): void {
    const warehouseId = this.selectedWarehouseFilter.value;
    const categoryId = this.selectedCategoryFilter.value;
    const brandId = this.selectedBrandFilter.value;

    this.productSelectionService.applyFilters({
      warehouseId,
      categoryId,
      brandId
    });

    // Nếu chọn kho mới, cập nhật cho việc hiển thị tồn kho
    if (warehouseId) {
      this.productSelectionService.selectWarehouse(warehouseId);
    }
  }

  /**
   * Reset tất cả các bộ lọc
   */
  resetFilters(): void {
    this.selectedWarehouseFilter.setValue(null);
    this.selectedCategoryFilter.setValue(null);
    this.selectedBrandFilter.setValue(null);
    this.productSelectionService.applyFilters(null);
  }

  /**
   * Theo dõi item theo id cho virtual scroll
   */
  trackById(index: number, item: any): string {
    return item.productId;
  }

  /**
   * Reset toàn bộ lựa chọn sản phẩm
   */
  resetSelection(): void {
    // Xử lý logic reset selection
    const currentItems = this.productSelectionService.getSelectedProducts();

    if (currentItems.length > 0) {
      // Gửi sự kiện reset trước khi xóa các sản phẩm
      this.resetSelectionEvent.emit();

      // Sử dụng phương thức trong service để reset các lựa chọn
      this.productSelectionService.resetAllSelections();
    }
  }

  /**
   * Thêm sản phẩm đã chọn vào đơn hàng
   */
  addToOrder(): void {
    // Lấy danh sách sản phẩm đã chọn từ service
    const selectedProducts = this.productSelectionService.getSelectedProducts();

    if (selectedProducts.length > 0) {
      // Chuyển đổi sang OrderItemBaseDetails
      const orderItems: OrderItemBaseDetails[] = selectedProducts.map(item => {
        const orderItem: OrderItemBaseDetails = {
          quantity: item.quantity || 0,
          product: {
            productId: item.productId,
            cost: item.cost,
            name: item.name,
            price: item.price,
            userOverride: item.userOverride || {
              name: item.name,
              price: item.price
            }
          }
        };

        // Thêm variant nếu có
        if (item.variant) {
          orderItem.product!.variant = item.variant;
        }

        // Thêm unit nếu có
        if (item.unit) {
          orderItem.product!.unit = item.unit;
        }

        if(item.linkedModifierGroupIds) {
          orderItem.product!.linkedModifierGroupIds = item.linkedModifierGroupIds;
        }

        if(item.modifierGroups) {
          orderItem.modifierGroups = item.modifierGroups;
        }

        return orderItem;
      });

      // Emit sự kiện với danh sách đã chọn
      this.addSelectedItems.emit(orderItems);
    }
  }

  /**
   * Tính tổng tiền của các sản phẩm đã chọn
   */
  getTotalAmount(): number {
    const selectedProducts = this.productSelectionService.getSelectedProducts();
    return selectedProducts.reduce((total, item) =>
      total + (item.price * (item.quantity || 0)), 0);
  }

  /**
   * Format tổng tiền để hiển thị trên nút "Thêm vào đơn"
   */
  getFormattedTotal(): string {
    const total = this.getTotalAmount();
    // Format số tiền theo định dạng tiền tệ Việt Nam (ngàn, triệu, tỷ)
    if (total >= 1000000000) {
      return `${(total / 1000000000).toFixed(1)}tỷ`;
    }
    if (total >= 1000000) {
      return `${(total / 1000000).toFixed(1)}tr`;
    }
    if (total >= 1000) {
      return `${(total / 1000).toFixed(0)}k`;
    }
    return total.toString();
  }
}
