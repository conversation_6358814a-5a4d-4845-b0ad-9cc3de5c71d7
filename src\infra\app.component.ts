import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ViewEncapsulation } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {Mat<PERSON><PERSON>er, MatSidenavModule} from '@angular/material/sidenav';
import { ViewportService } from '@core/services/viewport.service';
import { AppHeaderComponent } from './layout/header/app-header.component';
import { AppNavigationDrawerComponent } from './layout/navigation/navigation-drawer/app-navigation-drawer.component';
import { AppHeaderService } from './layout/header/app-header.service';
import { HttpLoaderService } from './core/services/http_loader.service';
import { AppChannelService } from './core/services/app_channel.service';
import { AppAutoUpdateService } from './core/services/app_auto_update.service';
import { BottomNavigationComponent } from './layout/navigation/navigation-mobile/bottom/bottom-navigation.component';
import { AppNavigationDrawerService } from './layout/navigation/navigation-drawer/app-navigation-drawer.service';
import { Subject } from 'rxjs';
import { NavigationService } from './layout/navigation/navigation.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatProgressBarModule,
    AppHeaderComponent,
    AppNavigationDrawerComponent,
    BottomNavigationComponent,
    MatSidenavModule
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnDestroy {
  hasLoadingBar = false;
  isShowHeader = true;
  viewport$!: ViewportService['viewport$'];
  private destroy$ = new Subject<void>();

  @ViewChild('drawer') drawer!: MatDrawer;

  constructor(
    private loaderService: HttpLoaderService,
    private headerService: AppHeaderService,
    private viewportService: ViewportService,
    appChannel: AppChannelService,
    appAutoUpdateService: AppAutoUpdateService,
    private navigationDrawerService: AppNavigationDrawerService,
    private navigationService: NavigationService
  ) {
    appChannel.setCommunicateFunction();
    appAutoUpdateService.checkIfAppUpdated();
    this.viewport$ = viewportService.viewport$;

    this.navigationService.initialize(this.destroy$);
    this.navigationDrawerService.initialize();
    this.navigationDrawerService.drawerOpened.subscribe(() => {
      this.drawer?.open?.();
    });
    this.navigationDrawerService.drawerClosed.subscribe(() => {
      this.drawer?.close?.();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }


  ngAfterViewInit() {
    this.loaderService.getUpdates().subscribe({
      next: (v) => {
        this.hasLoadingBar = v;
      }
    });

    // setTimeout(() => this.commonService.redirectInAppRouter({
    //   path: '/invoices',
    //   queryParams: {
    //     id: '6770fa906d086514f84cb3bc'
    //   }
    // }), 1000);

    // this.pageMsgService.success('Hello');
    // const notif = {
    //   notification: {
    //     title: 'Bạn có 1 đơn hàng mới',
    //     body: 'Check it out! ✨'
    //   },
    //   data: {
    //     inAppRouter: {
    //       path: '/invoices',
    //       queryParams: {
    //         id: '67749725eb4397dbbf09c5f3'
    //       }
    //     }
    //   }
    // };
    // this.inAppNotificationService.show(notif as any);
    // setTimeout(() => this.inAppNotificationService.show(notif), 1000);
  }

  ngOnInit(): void {
    this.headerService.getDisplay().subscribe(
      {
        next: d => {
          this.isShowHeader = d;
        }
      }
    );
  }

  drawerBackdropClick() {
    this.navigationDrawerService.closeDrawer();
  }
}

/**
 * disable zoom
 */
document.addEventListener('gesturestart', (e) => {
  e.preventDefault();

  if((document as any).body.style?.zoom) {
    (document as any).body.style.zoom = 1;
  }
});

document.addEventListener('gesturechange', (e) => {
  e.preventDefault();

  if((document as any).body.style?.zoom) {
    (document as any).body.style.zoom = 1;
  }
});
document.addEventListener('gestureend', (e) => {
  e.preventDefault();
  if((document as any).body.style?.zoom) {
    (document as any).body.style.zoom = 1;
  }
});

/**
 * communicate with flutter
 * đây là hàm nhận data chuyển từ flutter xuống
 */
// @ts-ignore
window.setReceiveFlutterMsgHandler = (func: Function) => {
// @ts-ignore
  window.receiveFlutterMsgHandler = func;
};

// @ts-ignore
window.sendFromFlutter = (msg: string) => {
  console.log(msg);

  // @ts-ignore
  if(window.receiveFlutterMsgHandler) {
    // @ts-ignore
    window.receiveFlutterMsgHandler(msg);
  }
};
