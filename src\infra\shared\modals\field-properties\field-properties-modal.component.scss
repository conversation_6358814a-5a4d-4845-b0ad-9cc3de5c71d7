.field-properties-content {
  min-width: 500px;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 1rem;

  @media (max-width: 768px) {
    min-width: 300px;
    max-width: 95vw;
    padding: 0.5rem;
  }
}

.field-properties-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.common-fields-section {
  .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--bs-primary);
  }
}

.field-specific-section {
  mat-expansion-panel {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    
    mat-expansion-panel-header {
      mat-panel-title {
        font-weight: 600;
        color: var(--bs-primary);
      }
    }
  }
}

.checkbox-field {
  margin: 0.75rem 0;
  
  mat-checkbox {
    width: 100%;
  }
}

.tooltip-section {
  .checkbox-field {
    margin-bottom: 0.5rem;
  }
}

.w-100 {
  width: 100%;
}

.info-text {
  font-size: 0.875rem;
  color: var(--bs-secondary);
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: var(--bs-light);
  border-radius: 4px;
}

// Picklist specific styles
.picklist-section {
  .picklist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    h4 {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
    }
    
    .picklist-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
  
  .picklist-options {
    min-height: 100px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    padding: 0.5rem;
    
    .picklist-option {
      margin-bottom: 0.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .option-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        .drag-handle {
          cursor: move;
          color: #666;
          display: flex;
          align-items: center;
          
          mat-icon {
            font-size: 1.2rem;
          }
        }
        
        .option-input {
          flex: 1;
        }
      }
    }
    
    &.cdk-drop-list-dragging .picklist-option:not(.cdk-drag-placeholder) {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
    
    .cdk-drag-placeholder {
      opacity: 0;
    }
    
    .cdk-drag-animating {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
  }
  
  .bulk-mode {
    .bulk-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 1rem;
      justify-content: flex-end;
    }
  }
}

// Currency and Decimal specific styles
.row {
  display: flex;
  gap: 1rem;
  margin: 0;
  
  .col-md-6 {
    flex: 1;
    padding: 0;
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0;
  }
}

// Actions
.field-properties-actions {
  padding: 1rem;
  gap: 0.5rem;
  
  button {
    min-width: 80px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .field-properties-content {
    padding: 0.5rem;
  }
  
  .picklist-section {
    .picklist-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
      
      .picklist-actions {
        align-self: flex-end;
      }
    }
    
    .picklist-options {
      .option-controls {
        flex-wrap: wrap;
        gap: 0.25rem;
        
        .option-input {
          min-width: 200px;
        }
      }
    }
  }
}

// Accessibility improvements
.field-properties-form {
  mat-form-field {
    &.ng-invalid.ng-touched {
      .mat-form-field-outline {
        color: var(--bs-danger);
      }
    }
  }
  
  mat-checkbox {
    &:focus {
      outline: 2px solid var(--bs-primary);
      outline-offset: 2px;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .info-text {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
  }
  
  .picklist-options {
    border-color: #555;
  }
}
