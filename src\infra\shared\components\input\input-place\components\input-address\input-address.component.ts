import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, signal, computed, effect, ChangeDetectorRef } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDialog } from '@angular/material/dialog';
import { HttpContext } from '@angular/common/http';
import { HttpService } from '@core/services/http.service';
import { InitDataStore } from '@core/store/init_data.store';
import { HTTP_REQUEST_OPTIONS } from '@core/tokens/http-context-token';
import { Place, StoreConfigs } from 'salehub_shared_contracts';

// Import dữ liệu hành chính
import { provinceList, districtList, wardList } from '@/mock/shared/vn_adminstrative_divisions.mock';
import { normalizeVietnameseStr } from '@shared/utils';
import { EditPlaceDetailsDialogComponent } from './edit-place-details-dialog.component';

@Component({
  selector: 'app-input-address',
  standalone: true,
  imports: [
    FormsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatInputModule
  ],
  templateUrl: './input-address.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InputAddressComponent {
  @Input() placeholder = 'Địa chỉ';
  @Input() oldPlaces!: Place[] | undefined;
  @Input() defaultValue!: Place | undefined;

  @Output() selectedPlace = new EventEmitter<Place>();
  @Output() changingPlace = new EventEmitter<boolean>();

  // Signals
  isSearchingAddress = signal(false);
  isDisabling = signal(false);
  places = signal<Place[]>([]);
  value = signal<Place | undefined>(undefined);
  isOldPlacesEmpty = signal(false);
  inputAddress = signal('');

  private store!: StoreConfigs;
  private timeoutSearchPlace: any | null = null;

  constructor(
    initStore: InitDataStore,
    private http: HttpService,
    private dialog: MatDialog
  ) {
    this.store = initStore.getData()?.store;
  }

  ngOnInit() {
    this.isOldPlacesEmpty.set(!this.oldPlaces?.length);

    if (this.defaultValue) {
      this.value.set(this.defaultValue);
      this.inputAddress.set(this.defaultValue.resolvedAddress ?? this.defaultValue.mainAddress ?? this.defaultValue.fullAddress ?? '');
      this.changeAddress(this.defaultValue);
    }
  }

  onAddressChange(text: string) {
    this.changingPlace.emit();

    if (typeof text !== 'string' || this.isSearchingAddress()) {
      return;
    }

    const regExp = /[a-zA-Z]/g;
    if (!regExp.test(text)) {
      return;
    }

    const filterValue = text.toLowerCase().trim();
    if (filterValue.length > 0) {
      if (this.oldPlaces && this.oldPlaces.length > 0) {
        const filteredPlaces = this.oldPlaces.filter(item =>
          item.fullAddress?.toLowerCase().includes(filterValue)
        );
        this.places.set(filteredPlaces);
      }

      if (!this.places().length) {
        if (this.timeoutSearchPlace) {
          clearTimeout(this.timeoutSearchPlace);
          this.timeoutSearchPlace = null;
        }

        this.timeoutSearchPlace = setTimeout(() => {
          this.isSearchingAddress.set(true);

          this.http.post<Place[]>(
            'cashier',
            'pos_delivery_autocomplete',
            {
              text: filterValue,
              focus: {
                region: this.store?.addressInfo?.region,
                regionName: this.store?.addressInfo?.regionName,
                lat: this.store?.addressInfo?.lat,
                lng: this.store?.addressInfo?.lng
              }
            },
            {
              context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
                useLoader: true
              })
            }
          ).subscribe({
            next: (result) => {
              this.places.set(result ?? []);
              this.isSearchingAddress.set(false);

              if (this.isOldPlacesEmpty()) {
                this.oldPlaces = result;
              }
            },
            error: () => {
              this.isSearchingAddress.set(false);
            }
          });
        }, 700);
      }
    } else if (this.oldPlaces && this.oldPlaces.length > 0) {
      this.places.set(this.oldPlaces);
    }
  }

  editPlaceDetails(event?: Event) {
    if(event) {
      event.stopPropagation();
    }

    const currentValue = this.value();
    if (!currentValue) return;

    const dialogRef = this.dialog.open(EditPlaceDetailsDialogComponent, {
      data: { place: currentValue },
      width: '600px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe((result: Place) => {
      if (result) {
        this.value.set(result);
        this.inputAddress.set(result.resolvedAddress || result.mainAddress || result.fullAddress || '');

        this.changingPlace.emit(true);
        this.selectedPlace.emit(result);
      }
    });
  }

  parsePlaceDetails(place: Place) {
    // Khởi tạo kết quả mặc định
    const result = {
      province: { code: '', name: '', asciiName: '' },
      district: { code: '', name: '', asciiName: '', provinceCode: '' },
      ward: { code: '', name: '', asciiName: '', districtCode: '' }
    };

    try {
      // Chuẩn hóa tên để so sánh
      const normalizeText = (text: string) => (normalizeVietnameseStr(text.toLowerCase()) as string)
        .replace(/thanh pho |tinh |quan |huyen |phuong |xa |thi xa |thi tran /gi, '')
        .replace(/tp\. /gi, '')
        .trim();

      // Hàm tìm mã code từ dữ liệu hành chính
      const findCodes = (wardName?: string, districtName?: string, provinceName?: string) => {
        if (!provinceName) return;

        const normalizedProvinceName = normalizeText(provinceName);
        const normalizedDistrictName = districtName ? normalizeText(districtName) : '';
        const normalizedWardName = wardName ? normalizeText(wardName) : '';

        // Tìm province
        for (const province of provinceList) {
          if (province.asciiName.includes(normalizedProvinceName)) {
            result.province = province;

            // Tìm district
            if (districtName && districtList[province.code]) {
              for (const district of districtList[province.code]) {
                if (district.asciiName.includes(normalizedDistrictName)) {
                  result.district = district;

                  // Tìm ward
                  if (wardName && wardList[district.code]) {
                    for (const ward of wardList[district.code]) {
                      if (ward.asciiName.includes(normalizedWardName)) {
                        result.ward = ward;
                        return; // Tìm thấy ward, thoát sớm
                      }
                    }
                  }
                  return; // Tìm thấy district, thoát sớm
                }
              }
            }
            return; // Tìm thấy province, thoát sớm
          }
        }
      };

      if (place.addressComponents && place.addressComponents?.length > 0) {
        // Parse từ addressComponents
        let province = ''; let district = ''; let ward = '';

        place.addressComponents.forEach(comp => {
          const types = comp.types as string[];
          if (types.includes('administrative_area_level_1')) {
            province = comp.long_name;
            result.province.name = comp.long_name;
          }
          if (types.includes('locality') && !types.includes('administrative_area_level_1')) {
            district = comp.long_name;
            result.district.name = comp.long_name;
          }
          if (types.includes('sublocality') || types.includes('neighborhood')) {
            ward = comp.long_name;
            result.ward.name = comp.long_name;
          }
        });

        // Tìm mã code tương ứng
        findCodes(ward, district, province);
      } else if (place.fullAddress) {
        // Parse từ fullAddress
        const parts = place.fullAddress.split(',').map(part => part.trim());
        const filteredParts = parts.filter(part => !part.toLowerCase().includes('vietnam'));

        if (filteredParts.length >= 3) {
          result.province.name = filteredParts[filteredParts.length - 1];
          result.district.name = filteredParts[filteredParts.length - 2];
          result.ward.name = filteredParts[filteredParts.length - 3];

          // Tìm mã code tương ứng
          findCodes(result.ward.name, result.district.name, result.province.name);
        }
      }

      // Cập nhật thông tin địa chỉ vào place
      place.province = result.province;
      place.district = result.district;
      place.ward = result.ward;

      const addr = place.mainAddress ?? place.fullAddress;
      if(addr) {
        const parts = addr.split(',').map(part => part.trim());
        place.streetAddress = parts[0];

        this.inputAddress.set(place.streetAddress);
      }
    } catch (error) {
      console.error('Lỗi khi parse địa chỉ:', error);
    }

    return place;
  }

  clearAddress(event: Event) {
    event.stopPropagation();

    // Reset tất cả các giá trị
    this.inputAddress.set('');
    this.value.set(undefined);
    this.places.set([]);

    // Emit sự kiện thay đổi
    this.changingPlace.emit(true);
    this.selectedPlace.emit(undefined);
  }

  display(record: Place): string {
    if (typeof record === 'string') {
      return record;
    }
    return record?.mainAddress ?? record?.fullAddress ?? '';
  }

  async changeAddress(place: Place) {
    this.changingPlace.emit();

    if (place.placeId && (!place.lat || !place.lng)) {
      this.isDisabling.set(true);

      try {
        const result: Place = await this.http.promisify(
          this.http.post(
            'cashier',
            'pos_delivery_place_details',
            {
              placeId: place.placeId
            }
          )
        );

        if (result) {
          place.mainAddress = place.mainAddress ?? place.fullAddress;

          if (
            place.mainAddress &&
            result.mainAddress &&
            result.mainAddress !== place.mainAddress
          ) {
            place.secondaryAddress = result.mainAddress;
          }

          place = {
            ...result,
            ...place
          };
        }
      } catch (e) {
        console.error('Error fetching place details:', e);
      }

      this.isDisabling.set(false);
    }

    this.parsePlaceDetails(place);

    const storePlaceId = this.store?.addressInfo?.placeId;
    place.distances ||= {};

    if (
      place.lat &&
      place.lng &&
      storePlaceId &&
      this.store?.addressInfo?.lat &&
      this.store?.addressInfo?.lng
    ) {
      if (!place.distances[storePlaceId]) {
        this.isDisabling.set(true);

        try {
          const result = await this.http.promisify(
            this.http.post(
              'cashier',
              'pos_delivery_distance',
              {
                addr1: {
                  lat: this.store.addressInfo.lat,
                  lng: this.store.addressInfo.lng
                },
                addr2: {
                  lat: place.lat,
                  lng: place.lng
                }
              }
            )
          );

          if (result) {
            place.distances[storePlaceId] = result;
          }
        } catch (e) {
          console.error('Error calculating distance:', e);
        }

        this.isDisabling.set(false);
      }
    }

    if (storePlaceId && place.distances[storePlaceId]) {
      place.distance = place.distances[storePlaceId];
    }

    this.value.set(place);
    this.selectedPlace.emit(place);
  }

  select($event: MatAutocompleteSelectedEvent) {
    const value: Place = $event.option?.value;
    this.changeAddress(value);
  }
}
