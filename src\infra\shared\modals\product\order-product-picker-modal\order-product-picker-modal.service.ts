import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { OrderProductPickerModalComponent } from './order-product-picker-modal.component';
import { OrderProductPickerModalData, OrderProductPickerModalResult } from './models/order-product-picker-modal.model';

/**
 * Service để mở modal chọn sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class OrderProductPickerModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal chọn sản phẩm
   * @param data Dữ liệu cho modal
   * @returns Promise<OrderProductPickerModalResult> Kết quả sản phẩm đã chọn hoặc null nếu hủy
   */
  async open(data: OrderProductPickerModalData): Promise<OrderProductPickerModalResult> {
    try {
      const modalConfig = {
        data,
        width: '90vw',
        maxWidth: '1200px',
        maxHeight: '90vh',
        panelClass: ['custom-dialog', 'initial']
      };
      
      const result = await this.responsiveModalService.open<
        OrderProductPickerModalComponent,
        OrderProductPickerModalData,
        OrderProductPickerModalResult
      >(OrderProductPickerModalComponent, modalConfig);
      
      return result || null;
    } catch (error) {
      console.error('Lỗi khi mở modal chọn sản phẩm:', error);
      return null;
    }
  }
}
