{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.0", "scripts": {"merge-translations": "node scripts/merge-translations.js", "merge-translations:watch": "node scripts/merge-translations.js --watch", "ng": "ng", "start": "concurrently \"npm run merge-translations:watch\" \"ng serve\"", "build": "ng build  --deploy-url / --base-href /", "watch": "npm run merge-translations && ng build --watch --configuration development", "test": "ng test", "eslint": "eslint \"src/**/*\"", "dev": "node scripts/dev.js", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "module-translation-validator": "node scripts/translation-validator/module-final-check/index.js"}, "bin": {"compare-component-translation-keys": "./scripts/translation-validator/bin/bin-compare-component-translation-keys.js", "compare-translations": "./scripts/translation-validator/bin/bin-compare-translations.js", "find-unused-translations": "./scripts/translation-validator/bin/bin-find-unused-translations.js", "export-component-html-translation-keys": "./scripts/translation-validator/bin/bin-export-component-html-translation-keys.js"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/cdk": "^19.2.2", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/fire": "^19.0.0", "@angular/forms": "^19.1.0", "@angular/material": "^19.1.4", "@angular/material-moment-adapter": "^19.1.4", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@angular/service-worker": "^19.1.6", "@anthropic-ai/sdk": "^0.39.0", "@cds/core": "^6.15.1", "@clr/angular": "^17.7.1", "@clr/ui": "^17.7.1", "@danielmoncada/angular-datetime-picker": "^19.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^19.0.9", "bootstrap": "^5.3.3", "boxen": "^8.0.1", "chalk": "^4.1.2", "chokidar": "^4.0.3", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "embla-carousel-angular": "^19.0.0", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "firebase-admin": "^13.1.0", "fuse.js": "^7.0.0", "glob-promise": "^6.0.7", "golden-layout": "^2.6.0", "gradient-string": "^3.0.0", "hammerjs": "^2.0.8", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "inquirer": "^12.5.0", "intersection-observer": "^0.12.2", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "moment": "^2.30.1", "ngx-mask": "^19.0.6", "ngx-mat-select-search": "^8.0.0", "ngx-scrollbar": "^18.0.0", "ngx-sortablejs": "^11.1.0", "ngx-toastr": "^19.0.0", "ngx-translate-extract": "^1.0.0", "openai": "^4.89.0", "ora": "^8.2.0", "primeng": "^19.0.9", "rxjs": "~7.8.0", "salehub_shared_contracts": "git+https://oauth2:<EMAIL>/salehub/salehub_shared_variables.git", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.6", "swiper": "^11.2.6", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.7", "@angular/cli": "^19.1.7", "@angular/compiler-cli": "^19.1.0", "@ngx-i18nsupport/ngx-i18nsupport": "^1.1.6", "@types/jasmine": "~5.1.0", "concurrently": "^9.1.2", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "nodex-eslint-rules": "git+https://oauth2:<EMAIL>/_nodex_/modules/nodex-eslint-rules", "ts-morph": "^25.0.1", "ts-node": "^10.9.2", "typescript": "~5.7.2"}, "type": "module"}