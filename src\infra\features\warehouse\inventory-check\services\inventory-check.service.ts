import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError, tap, map, delay } from 'rxjs';
import {
  ProductListItem,
  EmbeddedProductSerial,
  InventoryCheckItem,
  InventoryCheckItemBatch,
  Warehouse,
  EmbeddedProduct,
  SaveInventoryCheckResult,
  SerialNumberModalData
} from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import {
  InventoryCheckExtended,
  InventoryCheckSummaryExtended
} from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';
import { SerialNumberModalComponent } from '../components/serial-number-modal';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { InventoryCheckUseCase } from '../../../../../application/use-cases/warehouse/inventory-check/inventory-check.usecase';
import { InventoryCheck } from '../../../../../domain/entities/inventory-check.entity';

/**
 * Service quản lý logic nghiệp vụ cho component kiểm kho
 * Sử dụng Clean Architecture để tách biệt logic nghiệp vụ và giao diện
 */
@Injectable()
export class InventoryCheckService {

  // BehaviorSubject để quản lý state
  private inventoryCheckSubject = new BehaviorSubject<InventoryCheckExtended | null>(null);

  // Observable để component có thể subscribe
  inventoryCheck$: Observable<InventoryCheckExtended | null> = this.inventoryCheckSubject.asObservable();

  // BehaviorSubject để quản lý danh sách sản phẩm kiểm kho
  private inventoryItemsSubject = new BehaviorSubject<InventoryCheckItem[]>([]);

  // Observable để component có thể subscribe
  inventoryItems$: Observable<InventoryCheckItem[]> = this.inventoryItemsSubject.asObservable();

  constructor(
    private responsiveModalService: ResponsiveModalService,
    private inventoryCheckUseCase: InventoryCheckUseCase
  ) {
    // Khởi tạo một InventoryCheck mới
    this.initInventoryCheck();
  }

  /**
   * Khởi tạo một InventoryCheck mới với các giá trị mặc định
   */
  initInventoryCheck(): void {
    const domainInventoryCheck = this.inventoryCheckUseCase.initInventoryCheck();

    // Chuyển đổi từ domain entity sang view model
    const viewModelInventoryCheck: InventoryCheckExtended = {
      ...domainInventoryCheck,
      summary: {
        ...domainInventoryCheck.summary,
        stats: {
          checkedItemsCount: 0,
          uncheckedItemsCount: 0,
          totalItemsCount: 0,
          completionPercentage: 0
        }
      }
    };

    this.inventoryCheckSubject.next(viewModelInventoryCheck);
    this.inventoryItemsSubject.next([]);
  }

  /**
   * Tạo ID ngẫu nhiên cho InventoryCheck
   */
  private generateId(): string {
    return 'inv_check_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Lấy danh sách danh mục sản phẩm
   * @returns Danh sách danh mục sản phẩm
   */
  getProductCategories(): Observable<{_id: string, name: string}[]> {
    return this.inventoryCheckUseCase.getProductCategories();
  }

  /**
   * Lấy danh sách vị trí trong kho
   * @param warehouseId ID của kho
   * @returns Danh sách vị trí trong kho
   */
  getWarehouseLocations(warehouseId: string): Observable<{_id: string, name: string, warehouse: { _id: string; name: string; }}[]> {
    return this.inventoryCheckUseCase.getWarehouseLocations(warehouseId);
  }

  /**
   * Tải phiếu kiểm kho từ server theo ID
   * @param id ID của phiếu kiểm kho cần tải
   */
  loadInventoryCheck(id: string): Observable<InventoryCheckExtended> {
    if (!id) {
      return throwError(() => new Error('ID không hợp lệ'));
    }

    // Mô phỏng API call để tải phiếu kiểm kho
    console.log(`Tải phiếu kiểm kho với ID: ${id}`);

    // Tạo dữ liệu mẫu
    const mockInventoryCheck: InventoryCheckExtended = {
      _id: id,
      warehouse: {
        _id: 'WH001',
        name: 'Kho chính'
      },
      items: [
        {
          product: {
            productId: 'P001',
            name: 'Sản phẩm 1',
            sku: 'SKU001',
            cost: 100000,
            price: 120000
          },
          stockQuantity: 10,
          actualQuantity: 8,
          differenceQuantity: -2,
          differenceValue: -200000
        },
        {
          product: {
            productId: 'P002',
            name: 'Sản phẩm 2',
            sku: 'SKU002',
            cost: 150000,
            price: 180000
          },
          stockQuantity: 5,
          actualQuantity: 7,
          differenceQuantity: 2,
          differenceValue: 300000
        }
      ],
      summary: {
        totalIncrease: {
          quantity: 2,
          value: 300000
        },
        totalDecrease: {
          quantity: 2,
          value: 200000
        },
        totalDifference: {
          quantity: 0,
          value: 100000
        },
        stats: {
          checkedItemsCount: 2,
          uncheckedItemsCount: 0,
          totalItemsCount: 2,
          completionPercentage: 100
        }
      },
      status: 'draft',
      createdBy: {
        _id: 'U001',
        name: 'Nhân viên 1'
      },
      createdAt: new Date(),
      adjustmentReason: {
        reasonId: 'R001',
        description: 'Kiểm kho định kỳ'
      }
    };

    return of(mockInventoryCheck).pipe(
      delay(1000), // Giả lập độ trễ mạng
      tap(result => {
        console.log('Đã tải phiếu kiểm kho:', result);
        this.inventoryCheckSubject.next(result);
        this.inventoryItemsSubject.next(result.items);
      })
    );
  }

  /**
   * Tải danh sách phiếu kiểm kho từ server
   * @param params Tham số tìm kiếm
   */
  loadInventoryCheckList(params?: Record<string, unknown>): Observable<{
    items: InventoryCheckExtended[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    // Mô phỏng API call để tải danh sách phiếu kiểm kho
    console.log('Tải danh sách phiếu kiểm kho với tham số:', params);

    // Tạo dữ liệu mẫu
    const mockInventoryCheckList = {
      items: [
        {
          _id: 'IC001',
          warehouse: {
            _id: 'WH001',
            name: 'Kho chính'
          },
          summary: {
            totalIncrease: {
              quantity: 5,
              value: 500000
            },
            totalDecrease: {
              quantity: 3,
              value: 300000
            },
            totalDifference: {
              quantity: 2,
              value: 200000
            }
          },
          status: 'completed' as const,
          createdBy: {
            _id: 'U001',
            name: 'Nhân viên 1'
          },
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 ngày trước
          items: [],
          adjustmentReason: {
            reasonId: 'R001',
            description: 'Kiểm kho định kỳ'
          }
        },
        {
          _id: 'IC002',
          warehouse: {
            _id: 'WH002',
            name: 'Kho phụ'
          },
          summary: {
            totalIncrease: {
              quantity: 2,
              value: 200000
            },
            totalDecrease: {
              quantity: 1,
              value: 100000
            },
            totalDifference: {
              quantity: 1,
              value: 100000
            }
          },
          status: 'draft' as const,
          createdBy: {
            _id: 'U002',
            name: 'Nhân viên 2'
          },
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 ngày trước
          items: [],
          adjustmentReason: {
            reasonId: 'R002',
            description: 'Kiểm kho định kỳ'
          }
        }
      ],
      total: 2,
      page: 1,
      pageSize: 10
    };

    return of(mockInventoryCheckList).pipe(
      delay(1000), // Giả lập độ trễ mạng
      tap(result => {
        console.log('Đã tải danh sách phiếu kiểm kho:', result);
      })
    );
  }

  /**
   * Lấy danh sách kho
   */
  getWarehouses(): Observable<Warehouse[]> {
    return this.inventoryCheckUseCase.getWarehouses();
  }

  /**
   * Lấy danh sách sản phẩm theo warehouseId
   * @param warehouseId ID của kho
   */
  getProductsByWarehouse(warehouseId: string): Observable<ProductListItem[]> {
    // Sử dụng repository để lấy danh sách sản phẩm theo kho
    // Đây là phương thức tạm thời, cần cập nhật repository để hỗ trợ phương thức này
    console.log(`Lấy danh sách sản phẩm theo kho: ${warehouseId}`);
    return of([]);
  }

  /**
   * Thiết lập kho cho InventoryCheck
   */
  setWarehouse(warehouse: { _id: string, name: string }): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (currentInventoryCheck) {
      currentInventoryCheck.warehouse = warehouse;
      this.inventoryCheckSubject.next(currentInventoryCheck);
    }
  }

  /**
   * Thêm sản phẩm vào danh sách kiểm kho
   * @param product Sản phẩm cần thêm vào danh sách kiểm kho
   * @returns Observable<boolean> - true nếu thêm thành công, false nếu không thêm được
   */
  addProductToInventory(product: ProductListItem): Observable<boolean> {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck || !currentInventoryCheck.warehouse._id) {
      return of(false);
    }

    // Kiểm tra xem sản phẩm đã có trong danh sách chưa
    const existingItemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === product.productId
    );

    if (existingItemIndex !== -1) {
      // Sản phẩm đã tồn tại, không thêm nữa
      return of(false);
    }

    // Lấy số lượng tồn kho từ warehouseStock
    const stockQuantity = (product.warehouseStock && product.warehouseStock[currentInventoryCheck.warehouse._id]) || 0;

    // Tạo item mới
    const newItem: InventoryCheckItem = {
      product: {
        productId: product.productId,
        name: product.name,
        sku: product.sku,
        cost: product.cost || 0,
        price: product.price || 0,
        unit: product.baseUnit,
        hasVariants: !!product.variants && product.variants.length > 0,
        hasBatches: !!product.trackByBatch,
        hasSerials: !!product.trackBySerial
      } as EmbeddedProduct, // Sử dụng type assertion để tránh lỗi
      stockQuantity,
      actualQuantity: undefined,
      differenceQuantity: 0,
      differenceValue: 0,
      batchDetails: product.trackByBatch ? [] : undefined,
      serialDetails: product.trackBySerial ? product.serials : undefined
    };

    // Thêm vào danh sách
    const updatedItems = [...currentInventoryCheck.items, newItem];

    // Cập nhật tổng hợp
    currentInventoryCheck.items = updatedItems;
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...updatedItems]);

    // Trả về Observable<boolean> để có thể sử dụng trong forkJoin
    return of(true);
  }

  /**
   * Cập nhật số lượng thực tế cho sản phẩm
   * @param productId ID của sản phẩm cần cập nhật
   * @param actualQuantity Số lượng thực tế mới
   */
  updateActualQuantity(productId: string, actualQuantity: number): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    const item = currentInventoryCheck.items[itemIndex];

    // Kiểm tra xem sản phẩm có quản lý theo lô hoặc serial không
    const hasBatches = item.batchDetails && Array.isArray(item.batchDetails) && item.batchDetails.length > 0;
    const hasSerials = item.serialDetails && Array.isArray(item.serialDetails) && item.serialDetails.length > 0;

    // Nếu sản phẩm quản lý theo lô hoặc serial, không cho phép cập nhật trực tiếp số lượng thực tế
    if (hasBatches || hasSerials) {
      console.warn('Không thể cập nhật trực tiếp số lượng thực tế cho sản phẩm quản lý theo lô hoặc serial');
      return;
    }

    // Cập nhật số lượng thực tế
    item.actualQuantity = actualQuantity;

    // Tính toán chênh lệch
    this.calculateDifference(item as InventoryCheckItem);

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Tính toán chênh lệch cho một item
   * @param item Item cần tính toán chênh lệch
   */
  private calculateDifference(item: InventoryCheckItem): void {
    // Nếu chưa có số lượng thực tế, đặt chênh lệch về 0
    if (item.actualQuantity === undefined || item.actualQuantity === null) {
      item.differenceQuantity = 0;
      item.differenceValue = 0;
      return;
    }

    // Tính toán chênh lệch số lượng
    item.differenceQuantity = item.actualQuantity - item.stockQuantity;

    // Xác định giá để tính toán giá trị chênh lệch
    // Ưu tiên sử dụng giá vốn (cost) nếu có, nếu không thì sử dụng giá bán (price)
    // Nếu có unit, sử dụng unit.price
    const unitPrice = item.product.cost ||
                     (item.product.unit ? item.product.unit.price : item.product.price) ||
                     0;

    // Tính toán giá trị chênh lệch
    item.differenceValue = item.differenceQuantity * unitPrice;

    // Nếu sản phẩm có quản lý theo lô, tính toán chênh lệch cho từng lô
    if (item.batchDetails && Array.isArray(item.batchDetails) && item.batchDetails.length > 0) {
      item.batchDetails.forEach((batchItem: InventoryCheckItemBatch) => {
        // Nếu chưa có số lượng thực tế, đặt chênh lệch về 0
        if (batchItem.actualQuantity === undefined || batchItem.actualQuantity === null) {
          batchItem.differenceQuantity = 0;
          batchItem.differenceValue = 0;
          return;
        }

        // Tính toán chênh lệch số lượng cho lô
        batchItem.differenceQuantity = batchItem.actualQuantity - batchItem.stockQuantity;

        // Tính toán giá trị chênh lệch cho lô
        batchItem.differenceValue = batchItem.differenceQuantity * unitPrice;
      });
    }
  }

  /**
   * Tính toán tổng lệch cho InventoryCheck
   * @param items Danh sách sản phẩm cần tính toán tổng lệch
   * @returns Đối tượng chứa thông tin tổng lệch và thống kê
   */
  calculateSummary(items: InventoryCheckItem[]): InventoryCheckSummaryExtended {
    // Khởi tạo các biến tổng hợp
    let totalIncreaseQuantity = 0;
    let totalIncreaseValue = 0;
    let totalDecreaseQuantity = 0;
    let totalDecreaseValue = 0;

    // Tính tổng số sản phẩm đã kiểm
    const checkedItemsCount = items.filter(
      item => item.actualQuantity !== undefined && item.actualQuantity !== null
    ).length;

    // Tính tổng số sản phẩm chưa kiểm
    const uncheckedItemsCount = items.length - checkedItemsCount;

    // Tính phần trăm hoàn thành
    const completionPercentage = items.length > 0
      ? Math.round((checkedItemsCount / items.length) * 100)
      : 0;

    // Tính tổng lệch tăng và tổng lệch giảm
    items.forEach(item => {
      // Chỉ tính cho các sản phẩm đã kiểm
      if (item.actualQuantity !== undefined && item.actualQuantity !== null) {
        if (item.differenceQuantity > 0) {
          // Lệch tăng
          totalIncreaseQuantity += item.differenceQuantity;
          totalIncreaseValue += item.differenceValue;
        } else if (item.differenceQuantity < 0) {
          // Lệch giảm (lưu ý: lưu trữ giá trị tuyệt đối)
          totalDecreaseQuantity += Math.abs(item.differenceQuantity);
          totalDecreaseValue += Math.abs(item.differenceValue);
        }
      }
    });

    // Trả về đối tượng tổng hợp
    return {
      totalIncrease: {
        quantity: totalIncreaseQuantity,
        value: totalIncreaseValue
      },
      totalDecrease: {
        quantity: totalDecreaseQuantity,
        value: totalDecreaseValue
      },
      totalDifference: {
        quantity: totalIncreaseQuantity - totalDecreaseQuantity,
        value: totalIncreaseValue - totalDecreaseValue
      },
      // Thêm thông tin thống kê
      stats: {
        checkedItemsCount,
        uncheckedItemsCount,
        totalItemsCount: items.length,
        completionPercentage
      }
    };
  }

  /**
   * Cập nhật tổng hợp cho InventoryCheck
   * @param inventoryCheck Đối tượng InventoryCheck cần cập nhật tổng hợp
   */
  private updateSummary(inventoryCheck: InventoryCheckExtended): void {
    // Sử dụng phương thức calculateSummary để tính toán tổng lệch
    inventoryCheck.summary = this.calculateSummary(inventoryCheck.items);

    // Đảm bảo rằng summary là một đối tượng mới để trigger change detection
    inventoryCheck.summary = { ...inventoryCheck.summary };
  }

  /**
   * Xóa sản phẩm khỏi danh sách kiểm kho
   */
  removeProductFromInventory(productId: string): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    // Xóa sản phẩm
    currentInventoryCheck.items.splice(itemIndex, 1);

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Cập nhật ghi chú cho sản phẩm
   */
  updateProductNote(productId: string, note: string): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    // Tạo một bản sao của item để thay đổi tham chiếu
    const updatedItem: InventoryCheckItem = { ...currentInventoryCheck.items[itemIndex] };

    // Cập nhật ghi chú
    updatedItem.note = note;

    // Thay thế item cũ bằng item mới
    currentInventoryCheck.items[itemIndex] = updatedItem;

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Lấy thông tin sản phẩm theo ID
   * @param productId ID của sản phẩm
   */
  getProductById(productId: string): Observable<ProductListItem | undefined> {
    // Sử dụng repository để lấy thông tin sản phẩm theo ID
    // Đây là phương thức tạm thời, cần cập nhật repository để hỗ trợ phương thức này
    console.log(`Lấy thông tin sản phẩm theo ID: ${productId}`);
    return of(undefined);
  }

  /**
   * Cập nhật danh sách serial cho sản phẩm
   */
  updateProductSerials(productId: string, serials: EmbeddedProductSerial[]): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    // Cập nhật danh sách serial
    currentInventoryCheck.items[itemIndex].serialDetails = serials;

    // Tính toán số lượng thực tế dựa trên trạng thái serial
    const actualQuantity = serials.filter(serial =>
      serial.status === 'in_stock' || serial.status === 'assigned'
    ).length;

    // Cập nhật số lượng thực tế
    currentInventoryCheck.items[itemIndex].actualQuantity = actualQuantity;

    // Tính toán chênh lệch
    this.calculateDifference(currentInventoryCheck.items[itemIndex] as InventoryCheckItem);

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Cập nhật variant và đơn vị tính cho sản phẩm
   * @param productId ID của sản phẩm cần cập nhật
   * @param variantId ID của variant được chọn
   * @param unitId ID của đơn vị tính được chọn (tùy chọn)
   */
  updateProductVariant(productId: string, variantId: string, unitId?: string): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    // Tạo bản sao của item để thay đổi tham chiếu (giúp cho OnPush ChangeDetection)
    const productItem: InventoryCheckItem = { ...currentInventoryCheck.items[itemIndex] };

    // Cập nhật variant nếu có
    if (variantId) {
      // Trong phiên bản Clean Architecture, chúng ta sẽ sử dụng use case để lấy thông tin variant
      // Tạm thời, chúng ta sẽ sử dụng thông tin từ item hiện tại

      // Lưu trữ thông tin variant
      (productItem.product as any).variant = { variantId };
    }

    // Cập nhật đơn vị tính nếu có
    if (unitId) {
      // Trong phiên bản Clean Architecture, chúng ta sẽ sử dụng use case để lấy thông tin đơn vị tính
      // Tạm thời, chúng ta sẽ sử dụng thông tin từ item hiện tại

      if (productItem.product.unit) {
        // Lưu thông tin unitId vào một thuộc tính tạm
        (productItem.product as any)._selectedUnitId = unitId;
      }
    }

    // Tính toán lại chênh lệch nếu đã nhập số lượng thực tế
    if (productItem.actualQuantity !== undefined) {
      this.calculateDifference(productItem);
    }

    // Cập nhật item trong danh sách
    currentInventoryCheck.items[itemIndex] = productItem;

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    this.inventoryCheckSubject.next({ ...currentInventoryCheck });
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Cập nhật số lượng thực tế của lô
   */
  updateBatchQuantity(productId: string, batchId: string, quantity: number): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    const item = currentInventoryCheck.items[itemIndex];

    // Kiểm tra xem sản phẩm có quản lý theo lô không
    if (!item.batchDetails || !Array.isArray(item.batchDetails)) return;

    // Tìm lô cần cập nhật
    const batchIndex = item.batchDetails.findIndex(
      (batch: InventoryCheckItemBatch) => batch.batch._id === batchId
    );

    if (batchIndex === -1) return;

    // Cập nhật số lượng thực tế của lô
    item.batchDetails[batchIndex].actualQuantity = quantity;

    // Tính tổng số lượng thực tế từ tất cả các lô
    const totalActualQuantity = item.batchDetails.reduce(
      (total: number, batch: InventoryCheckItemBatch) => total + (batch.actualQuantity || 0),
      0
    );

    // Cập nhật số lượng thực tế của sản phẩm
    item.actualQuantity = totalActualQuantity;

    // Tính toán chênh lệch
    this.calculateDifference(item as InventoryCheckItem);

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Thêm lô mới cho sản phẩm
   */
  addBatch(productId: string, batchCode: string, expiryDate: Date | null, quantity: number | null): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    const item = currentInventoryCheck.items[itemIndex];

    // Khởi tạo mảng batchDetails nếu chưa có
    if (!item.batchDetails) {
      item.batchDetails = [];
    }

    // Kiểm tra xem quantity có hợp lệ không
    const validQuantity = quantity !== null ? quantity : 0;

    // Tạo đối tượng batch mới
    const newBatch: InventoryCheckItemBatch = {
      batch: {
        _id: batchCode,
        batchCode: batchCode,
        expiryDate: expiryDate || new Date(),
        manufactureDate: new Date()
      },
      stockQuantity: 0,
      actualQuantity: validQuantity,
      differenceQuantity: validQuantity,
      differenceValue: validQuantity * (item.product.cost ||
                                  (item.product.unit ? item.product.unit.price : item.product.price) ||
                                  0)
    };

    // Thêm lô mới vào danh sách
    item.batchDetails.push(newBatch);

    // Tính tổng số lượng thực tế từ tất cả các lô
    const totalActualQuantity = item.batchDetails.reduce(
      (total: number, b: InventoryCheckItemBatch) => total + (b.actualQuantity || 0),
      0
    );

    // Cập nhật số lượng thực tế của sản phẩm
    item.actualQuantity = totalActualQuantity;

    // Tính toán chênh lệch
    this.calculateDifference(item as InventoryCheckItem);

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
    this.inventoryItemsSubject.next([...currentInventoryCheck.items]);
  }

  /**
   * Thiết lập nhân viên cho InventoryCheck
   */
  setEmployee(employee: { _id: string, name: string }): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    currentInventoryCheck.createdBy = employee;
    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
  }

  /**
   * Thiết lập ngày kiểm kho cho InventoryCheck
   */
  setDate(date: Date): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    currentInventoryCheck.createdAt = date;
    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
  }

  /**
   * Thiết lập ghi chú cho InventoryCheck
   */
  setNote(note: string): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    if (!currentInventoryCheck.adjustmentReason) {
      currentInventoryCheck.adjustmentReason = {
        reasonId: 'manual_adjustment',
        description: note
      };
    } else {
      currentInventoryCheck.adjustmentReason.description = note;
    }

    // Cập nhật tổng hợp
    this.updateSummary(currentInventoryCheck);

    // Tạo bản sao mới để đảm bảo change detection hoạt động
    const updatedInventoryCheck = { ...currentInventoryCheck };
    this.inventoryCheckSubject.next(updatedInventoryCheck);
  }

  /**
   * Lấy thông tin InventoryCheck hiện tại
   */
  getInventoryCheck(): InventoryCheckExtended {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) {
      // Trả về một đối tượng InventoryCheck mặc định nếu không có dữ liệu
      return {
        _id: this.generateId(),
        warehouse: {
          _id: '',
          name: ''
        },
        items: [],
        summary: {
          totalIncrease: {
            quantity: 0,
            value: 0
          },
          totalDecrease: {
            quantity: 0,
            value: 0
          },
          totalDifference: {
            quantity: 0,
            value: 0
          },
          stats: {
            checkedItemsCount: 0,
            uncheckedItemsCount: 0,
            totalItemsCount: 0,
            completionPercentage: 0
          }
        },
        status: 'draft',
        createdBy: {
          _id: '',
          name: ''
        },
        createdAt: new Date(),
        adjustmentReason: {
          reasonId: '',
          description: ''
        }
      };
    }
    return currentInventoryCheck;
  }

  /**
   * Lấy thông tin InventoryCheck hiện tại (có thể trả về null)
   */
  getCurrentInventoryCheck(): InventoryCheckExtended | null {
    return this.inventoryCheckSubject.value;
  }

  /**
   * Cập nhật danh sách sản phẩm kiểm kho
   * @param items Danh sách sản phẩm kiểm kho mới
   */
  updateInventoryItems(items: InventoryCheckItem[]): void {
    this.inventoryItemsSubject.next([...items]);
  }

  /**
   * Lưu phiếu kiểm kho
   * @param status Trạng thái của phiếu kiểm kho ('draft' hoặc 'completed')
   */
  saveInventoryCheck(status: 'draft' | 'completed'): Observable<SaveInventoryCheckResult> {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) {
      return throwError(() => new Error('Không có dữ liệu kiểm kho'));
    }

    // Chuyển đổi từ view model sang domain entity
    const domainInventoryCheck: InventoryCheck = {
      ...currentInventoryCheck,
      summary: {
        totalIncrease: currentInventoryCheck.summary.totalIncrease,
        totalDecrease: currentInventoryCheck.summary.totalDecrease,
        totalDifference: currentInventoryCheck.summary.totalDifference
      }
    };

    return this.inventoryCheckUseCase.saveInventoryCheck(domainInventoryCheck, status).pipe(
      tap(result => {
        // Cập nhật ID và thời gian sau khi lưu thành công
        if (result && result._id) {
          currentInventoryCheck._id = result._id;
          if (result.createdAt) {
            currentInventoryCheck.createdAt = new Date(result.createdAt);
          }
          if (result.updatedAt) {
            currentInventoryCheck.updatedAt = new Date(result.updatedAt);
          }

          // Cập nhật trạng thái
          currentInventoryCheck.status = status;

          // Cập nhật state
          this.inventoryCheckSubject.next({...currentInventoryCheck});
        }
      })
    );
  }



  /**
   * In phiếu kiểm kho
   */
  printInventoryCheck(): void {
    const currentInventoryCheck = this.inventoryCheckSubject.value;
    if (!currentInventoryCheck) return;

    // Tạo cửa sổ in mới
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Tạo nội dung HTML cho phiếu in
    const printContent = `
      <html>
        <head>
          <title>Phiếu kiểm kho</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; }
            .header { display: flex; justify-content: space-between; margin-bottom: 20px; }
            .info-row { margin-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .footer { margin-top: 30px; display: flex; justify-content: space-between; }
            .signature { width: 30%; text-align: center; }
          </style>
        </head>
        <body>
          <h1>PHIẾU KIỂM KHO</h1>

          <div class="header">
            <div>
              <div class="info-row"><strong>Mã phiếu:</strong> ${currentInventoryCheck._id || 'KK' + new Date().getTime()}</div>
              <div class="info-row"><strong>Kho:</strong> ${currentInventoryCheck.warehouse?.name || ''}</div>
              <div class="info-row"><strong>Ngày kiểm:</strong> ${new Date(currentInventoryCheck.createdAt).toLocaleDateString('vi-VN')}</div>
            </div>
            <div>
              <div class="info-row"><strong>Người kiểm:</strong> ${currentInventoryCheck.createdBy?.name || ''}</div>
              <div class="info-row"><strong>Ghi chú:</strong> ${currentInventoryCheck.adjustmentReason?.description || ''}</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã SP</th>
                <th>Tên sản phẩm</th>
                <th>Đơn vị</th>
                <th>Tồn kho</th>
                <th>Thực tế</th>
                <th>Chênh lệch</th>
                <th>Giá trị lệch</th>
              </tr>
            </thead>
            <tbody>
              ${currentInventoryCheck.items.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.product.sku || ''}</td>
                  <td>${item.product.name || ''}</td>
                  <td>${item.product.unit || ''}</td>
                  <td>${item.stockQuantity || 0}</td>
                  <td>${item.actualQuantity !== undefined ? item.actualQuantity : ''}</td>
                  <td>${item.differenceQuantity !== undefined ? item.differenceQuantity : ''}</td>
                  <td>${item.differenceValue !== undefined ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(item.differenceValue) : ''}</td>
                </tr>
              `).join('')}
            </tbody>
            <tfoot>
              <tr>
                <th colspan="6" style="text-align: right;">Tổng cộng:</th>
                <th>${currentInventoryCheck.summary?.totalDifference?.quantity || 0}</th>
                <th>${currentInventoryCheck.summary?.totalDifference?.value !== undefined ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(currentInventoryCheck.summary.totalDifference.value) : ''}</th>
              </tr>
            </tfoot>
          </table>

          <div class="footer">
            <div class="signature">
              <p><strong>Người lập phiếu</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
            <div class="signature">
              <p><strong>Thủ kho</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
            <div class="signature">
              <p><strong>Kế toán trưởng</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
          </div>
        </body>
      </html>
    `;

    // Ghi nội dung vào cửa sổ in
    printWindow.document.open();
    printWindow.document.body.innerHTML = printContent;
    printWindow.document.close();

    // In tài liệu
    setTimeout(() => {
      printWindow.print();
    }, 500);
  }

  /**
   * Mở modal quản lý số serial
   * @param product Thông tin sản phẩm
   * @param serials Danh sách serial
   * @returns Promise với danh sách serial sau khi cập nhật
   */
  async openSerialNumberModal(product: ProductListItem, serials: EmbeddedProductSerial[]): Promise<EmbeddedProductSerial[] | undefined> {
    const modalData: SerialNumberModalData = {
      product,
      serials
    };

    return this.responsiveModalService.open<
      SerialNumberModalComponent,
      SerialNumberModalData,
      EmbeddedProductSerial[]
    >(SerialNumberModalComponent, { data: modalData });
  }
}
