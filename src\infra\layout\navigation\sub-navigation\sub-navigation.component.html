@if (topLevelItems() && topLevelItems().length > 0) {
  @if (viewport$ | async; as viewport) {
  <div class="sub-nav">
    <div class="d-flex align-items-center flex-nowrap">
      <div class="sub-nav-module d-none">
        <div
          class="sub-nav-module-text"
          matTooltip="{{ (activeNavigation()?.moduleTooltip || '') | translate }}"
          >
          {{ (activeNavigation()?.moduleShortName || '') | translate }}
        </div>
      </div>

      <div #swiperContainer class="swiper">
        <div class="swiper-wrapper swiper-container">
            <!-- Sử dụng swiper-slide -->
            @for (item of topLevelItems(); track trackByTopItem($index, item)) {
              @if(!item.hideFromNavigationTree) { 
              <div class="swiper-slide sub-nav-item">
                <a
                  [routerLink]="(!item.items || item.items.length === 0) ? item.routerLink : null"
                  (click)="(!viewport.isDesktop && item.items && item.items.length > 0) ? openSubSheet(item, $event) : null"
                  [matMenuTriggerFor]="(viewport.isDesktop && item.items && item.items.length > 0) ? appMenu : null"
                  [matMenuTriggerData]="(viewport.isDesktop && item.items && item.items.length > 0) ? {items: item.items} : null"
                  [class.active]="isNavigationItemActive(item)"
                  [attr.aria-label]="item.text | translate"
                >
                  <!-- Text -->
                  <span>{{ item.text | translate }}</span>

                  <!-- Arrow indicator (using simple text/symbol for now) -->
                  <i
                    *ngIf="item.items && item.items.length > 0"
                    class="ms-2 fa-light fa-chevron-down">
                  </i>

                </a>
              </div>
              }
            }
        </div>

        <button class="sub-nav-next"><i class="fa-light fa-chevron-right"></i></button>
        <button class="sub-nav-prev"><i class="fa-light fa-chevron-left"></i></button>
      </div>
    </div>
  </div>

  <mat-menu
    #appMenu="matMenu"
    class="dropdown-content menu-default light:border-gray-300 sub-nav-menu" >
    <ng-template
      matMenuContent
      let-items="items"
      >
      @for (item of items; track trackByTopItem($index, item)) {
        @if(!item.hideFromNavigationTree) { 
        <div class="menu-item">
          <a
            [routerLink]="item.routerLink"
            mat-menu-item
            [class.active]="isNavigationItemActive(item)"
          >
            <span>{{ item.text | translate }}</span>
          </a>
        </div>
        }
      }
    </ng-template>
  </mat-menu>
  }
}
