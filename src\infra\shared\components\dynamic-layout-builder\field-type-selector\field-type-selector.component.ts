import { Component, EventEmitter, Input, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import Sortable from 'sortablejs';
import { LayoutField, DragData, TrackByFunction } from '@shared/models/view/dynamic-layout-builder.model';

/**
 * Component để chọn loại field khi thêm field mới vào section
 * Hiển thị dropdown hoặc danh sách button để chọn loại field
 * Hỗ trợ drag & drop để kéo field type vào section
 */
@Component({
  selector: 'app-field-type-selector',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatListModule,
    MatDividerModule,
    TranslateModule
  ],
  templateUrl: './field-type-selector.component.html',
  styleUrls: ['./field-type-selector.component.scss']
})
export class FieldTypeSelectorComponent {

  /**
   * Danh sách field types có sẵn
   */
  @Input() availableFieldTypes: LayoutField[] = [];

  /**
   * Event được emit khi người dùng chọn field type
   */
  @Output() fieldTypeSelected = new EventEmitter<LayoutField>();

  /**
   * Signal cho basic field types (text, number, email, etc.)
   */
  fieldTypes = signal<LayoutField[]>([]);


  /**
   * Signal để track drag state cho visual feedback
   */
  isDragging = signal<boolean>(false);

  /**
   * Field type hiện tại đang được drag
   */
  currentDraggedField = signal<LayoutField | null>(null);

  ngOnInit(): void {
    this.categorizeFieldTypes();
  }

  ngOnChanges(): void {
    this.categorizeFieldTypes();
  }

  /**
   * Phân loại field types thành các nhóm
   */
  private categorizeFieldTypes(): void {
    this.fieldTypes.set(this.availableFieldTypes);
  }

  /**
   * Tạo drag data cho field type với clone behavior
   * Đảm bảo field type được clone thay vì move
   */
  createDragData(fieldType: LayoutField): DragData {
    return {
      type: 'field-type',
      fieldType: { ...fieldType } // Clone field type để tránh reference issues
    };
  }

  /**
   * Xử lý khi bắt đầu drag
   * Cập nhật visual state và track dragged field
   */
  onDragStarted(fieldType: LayoutField): void {
    this.isDragging.set(true);
    this.currentDraggedField.set(fieldType);

    // Thêm CSS class cho visual feedback
    document.body.classList.add('field-dragging');

    console.log('🚀 Drag started for field:', fieldType.label);
    console.log('  - Field type:', fieldType.type);
    console.log('  - Drag data will be:', this.createDragData(fieldType));
  }

  /**
   * Xử lý khi kết thúc drag
   * Reset visual state
   */
  onDragEnded(): void {
    this.isDragging.set(false);
    this.currentDraggedField.set(null);

    // Remove CSS class
    document.body.classList.remove('field-dragging');

    console.log('🏁 Drag ended');
  }

  /**
   * Xử lý khi có item được drop vào field type selector (không nên xảy ra)
   */
  onFieldTypeDropped(event: DragEvent): void {
    console.log('⚠️ Field type dropped back to selector (should not happen):', event);
    // Không làm gì cả vì đây là source container
  }

  /**
   * HTML5 Drag Start Event Handler
   */
  onDragStart(event: DragEvent, fieldType: LayoutField): void {
    console.log('🚀 HTML5 Drag started for field:', fieldType.label);

    // Set drag data
    const dragData = {
      type: 'field-type-clone',
      fieldType: fieldType,
      sourceComponent: 'field-type-selector'
    };

    if (event.dataTransfer) {
      event.dataTransfer.setData('application/json', JSON.stringify(dragData));
      event.dataTransfer.effectAllowed = 'copy';

      // Set drag image (optional)
      const dragImage = event.target as HTMLElement;
      if (dragImage) {
        event.dataTransfer.setDragImage(dragImage, 0, 0);
      }
    }

    // Visual feedback
    this.isDragging.set(true);
    this.currentDraggedField.set(fieldType);
    document.body.classList.add('field-dragging');
  }

  /**
   * HTML5 Drag End Event Handler
   */
  onDragEnd(_event: DragEvent): void {
    console.log('🏁 HTML5 Drag ended');

    // Reset visual state
    this.isDragging.set(false);
    this.currentDraggedField.set(null);
    document.body.classList.remove('field-dragging');
  }

  /**
   * Lấy icon cho field type
   */
  getFieldIcon(fieldType: string): string {
    const iconMap: { [key: string]: string } = {
      'text': 'text_fields',
      'number': 'numbers',
      'email': 'email',
      'phone': 'phone',
      'textarea': 'notes',
      'date': 'calendar_today',
      'datetime': 'schedule',
      'file': 'attach_file',
      'image': 'image',
      'checkbox': 'check_box',
      'radio': 'radio_button_checked',
      'select': 'arrow_drop_down',
      'size': 'straighten',
      'color': 'palette',
      'brand': 'business',
      'category': 'category'
    };
    return iconMap[fieldType] || 'help_outline';
  }





  /**
   * TrackBy function cho field types
   */
  trackByFieldType(_index: number, fieldType: LayoutField): string | number {
    return fieldType.id || fieldType.type;
  }
}
