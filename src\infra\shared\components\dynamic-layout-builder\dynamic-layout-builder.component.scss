// Dynamic Layout Builder Styles
.dynamic-layout-builder-container {
  height: 100vh;
  display: flex;
  flex-direction: column;

  // Toolbar styles
  .layout-toolbar {
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    z-index: 10;

    .toolbar-title {
      font-size: 1.5rem;
      font-weight: 500;
      color: #333;
    }

    .toolbar-subtitle {
      font-size: 0.875rem;
      color: #666;
      margin-left: 1rem;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .toolbar-actions {
      display: flex;
      gap: 0.5rem;
      align-items: center;

      button {
        &.active {
          background-color: #e3f2fd;
          color: #1976d2;
        }
      }
    }
  }

  // Main content area
  .layout-content {
    flex: 1;
    display: flex;

    &.preview-mode {
      .fields-sidebar {
        width: 200px !important;
      }
    }
  }

  // Flexbox container
  .layout-flex-container {
    width: 100%;
    display: flex;

    // Sidebar styles
    .fields-sidebar {
      width: 300px;
      min-width: 300px;
      background-color: #fff;
      border-right: 1px solid #e0e0e0;
      padding: 1rem;
      overflow-y: auto;

      .sidebar-header {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e0e0e0;

        h3 {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 500;
          color: #333;
        }
      }

      // New section button
      .new-section-container {
        margin-bottom: 1.5rem;

        .new-section-btn {
          width: 100%;
          height: 60px;
          font-size: 1rem;
          font-weight: 500;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }

          mat-icon {
            margin-right: 0.5rem;
          }
        }
      }

      // Field types list
      .field-types-container {
        .field-types-list {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .field-type-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: grab;
            transition: all 0.2s ease;

            &:hover {
              background-color: #e9ecef;
              border-color: #dee2e6;
              transform: translateX(4px);
            }

            &:active {
              cursor: grabbing;
            }

            mat-icon {
              margin-right: 0.5rem;
              color: #6c757d;
              font-size: 1rem;
            }

            span {
              font-size: 0.875rem;
              font-weight: 500;
              color: #495057;
            }
          }
        }
      }
    }

    // Main content area
    .layout-main-content {
      flex: 1;
      padding: 1rem;

      // Loading state
      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 50vh;
        gap: 1rem;

        p {
          color: #666;
          font-size: 0.875rem;
        }
      }

      // Empty state
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 50vh;
        text-align: center;
        color: #666;

        .empty-icon {
          font-size: 4rem;
          width: 4rem;
          height: 4rem;
          margin-bottom: 1rem;
          color: #ccc;
        }

        h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1.25rem;
          font-weight: 500;
        }

        p {
          margin: 0 0 1.5rem 0;
          font-size: 0.875rem;
        }
      }

      // Sections container
      .sections-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        min-height: 200px;

        .section-wrapper {
          .section-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            mat-card-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding-bottom: 0.5rem;
              border-bottom: 1px solid #e0e0e0;

              mat-card-title {
                font-size: 1.125rem;
                font-weight: 500;
                color: #333;
                margin: 0;
              }

              .section-actions {
                display: flex;
                gap: 0.25rem;

                button {
                  width: 32px;
                  height: 32px;
                  line-height: 32px;

                  mat-icon {
                    font-size: 1rem;
                    width: 1rem;
                    height: 1rem;
                  }
                }
              }
            }

            mat-card-content {
              padding-top: 1rem;

              .fields-container {
                min-height: 100px;

                .field-item {
                  display: grid;
                  grid-template-columns: 1fr auto auto;
                  gap: 1rem;
                  align-items: center;
                  padding: 0.75rem;
                  margin-bottom: 0.5rem;
                  background-color: #f8f9fa;
                  border: 1px solid #e9ecef;
                  border-radius: 6px;
                  transition: all 0.2s ease;

                  &:hover {
                    background-color: #e9ecef;
                    border-color: #dee2e6;
                  }

                  .field-label {
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: #333;

                    .required-indicator {
                      color: #dc3545;
                      margin-left: 0.25rem;
                    }
                  }

                  .field-type {
                    font-size: 0.75rem;
                    color: #666;
                    text-transform: uppercase;
                    font-weight: 500;
                    background-color: #e3f2fd;
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                  }
                }

                .empty-fields {
                  text-align: center;
                  padding: 2rem;
                  color: #666;

                  p {
                    margin: 0;
                    font-size: 0.875rem;
                    font-style: italic;
                  }
                }
              }
            }
          }
        }
      }

      // Preview panel
      .preview-panel {
        position: fixed;
        top: 64px;
        right: 0;
        width: 400px;
        height: calc(100vh - 64px);
        background-color: #fff;
        border-left: 1px solid #e0e0e0;
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        z-index: 5;
        overflow-y: auto;
        padding: 1rem;

        .preview-card {
          .preview-stats {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-top: 1rem;

            p {
              margin: 0;
              font-size: 0.875rem;
              color: #666;
            }
          }
        }
      }
    }
  }
}

// CDK Drag Drop styles
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

// Responsive design
@media (max-width: 768px) {
  .dynamic-layout-builder-container {
    .layout-content {
      .layout-flex-container {
        flex-direction: column;

        .fields-sidebar {
          width: 100%;
          min-width: auto;
          padding: 0.75rem;
          border-right: none;
          border-bottom: 1px solid #e0e0e0;
          max-height: 300px;
        }

        .layout-main-content {
          padding: 0.75rem;

          .preview-panel {
            width: 100%;
            position: relative;
            top: 0;
            right: 0;
            height: auto;
            border-left: none;
            border-top: 1px solid #e0e0e0;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}
