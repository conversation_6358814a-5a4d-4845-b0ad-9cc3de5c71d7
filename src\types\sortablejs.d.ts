declare module 'sortablejs' {
  interface SortableEvent {
    oldIndex?: number;
    newIndex?: number;
    item: HTMLElement;
    from: HTMLElement;
    to: HTMLElement;
    clone: HTMLElement;
  }

  interface SortableOptions {
    group?: string | {
      name: string;
      pull?: boolean | string | ((to: any, from: any, dragEl: HTMLElement, evt: Event) => boolean);
      put?: boolean | string[] | ((to: any, from: any, dragEl: HTMLElement, evt: Event) => boolean);
    };
    animation?: number;
    ghostClass?: string;
    chosenClass?: string;
    dragClass?: string;
    handle?: string;
    onStart?: (evt: SortableEvent) => void;
    onEnd?: (evt: SortableEvent) => void;
    onAdd?: (evt: SortableEvent) => void;
    onUpdate?: (evt: SortableEvent) => void;
    onRemove?: (evt: SortableEvent) => void;
    onSort?: (evt: SortableEvent) => void;
  }

  class Sortable {
    constructor(el: HTMLElement, options?: SortableOptions);
    destroy(): void;
    static create(el: HTMLElement, options?: SortableOptions): Sortable;
    static Options: SortableOptions;
    static SortableEvent: SortableEvent;
  }

  namespace Sortable {
    export interface Options extends SortableOptions {}
    export interface SortableEvent {
      oldIndex?: number;
      newIndex?: number;
      item: HTMLElement;
      from: HTMLElement;
      to: HTMLElement;
      clone: HTMLElement;
    }
  }

  export = Sortable;
}
