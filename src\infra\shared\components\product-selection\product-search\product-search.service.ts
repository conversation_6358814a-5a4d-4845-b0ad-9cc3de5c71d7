import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { mockProductList } from '@/mock/shared/product.mock';

/**
 * Service xử lý logic tìm kiếm sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class ProductSearchService {
  constructor() { }

  /**
   * Tìm kiếm sản phẩm theo từ khóa
   * @param keyword Từ khóa tìm kiếm
   * @returns Observable danh sách sản phẩm
   */
  searchProducts(keyword: string): Observable<any[]> {
    if (!keyword || keyword.trim() === '') {
      return of([]);
    }

    // Chuyển đổi keyword thành không dấu và lowercase để tìm kiếm
    const normalizedKeyword = this.convertToAscii(keyword.toLowerCase().trim());

    // Lọ<PERSON> sản phẩm từ mockProductList
    const filteredProducts = mockProductList
      .filter(product => {
        const nameMatch = product.name && this.convertToAscii(product.name.toLowerCase()).includes(normalizedKeyword);
        const skuMatch = product.sku && this.convertToAscii(product.sku.toLowerCase()).includes(normalizedKeyword);
        return nameMatch || skuMatch;
      })
      .slice(0, 10) // Giới hạn 10 kết quả
      .map(product => ({
        productId: product.productId,
        name: product.name,
        sku: product.sku,
        price: product.price,
        cost: product.cost
      }));

    return of(filteredProducts);
  }

  /**
   * Chuyển đổi chuỗi thành ASCII để tìm kiếm không dấu
   * @param str Chuỗi cần chuyển đổi
   * @returns Chuỗi đã chuyển đổi
   */
  convertToAscii(str: string): string {
    if (!str) return '';
    return str.normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/đ/g, 'd').replace(/Đ/g, 'D')
      .toLowerCase();
  }
}
