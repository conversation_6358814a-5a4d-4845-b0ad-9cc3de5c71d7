export function hideKeyboard(element: HTMLInputElement) {
  setTimeout(() => {
    if(element) {
      element.blur();
    }

    // @ts-ignore
    if(navigator?.virtualKeyboard?.hide) {
    // @ts-ignore
      navigator.virtualKeyboard.hide();
    };
  }, 50);
}
export function scrollTop() {
  window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
}
export function getElementTotalOffsetTop(element: HTMLElement) {
  if(!element) {
    return 0;
  }

  let appMainMarginTop = 0;
  const appMainElement = document.querySelector('.app-main') as HTMLElement;
  if(appMainElement) {
    const _appMainMarginTop = window.getComputedStyle(appMainElement).marginTop;
    if(_appMainMarginTop) {
      appMainMarginTop = parseInt(_appMainMarginTop);
    }
  }

  return appMainMarginTop + element.offsetTop;
}

export function getElementMaxHeightToFit100vh(element: HTMLElement, additionalHeight = 0) {
  const offsetTop = getElementTotalOffsetTop(element) + additionalHeight;
  return window.innerHeight - offsetTop;
}

export * from './map.util';
export * from './object.util';
export * from './simple_encrypt.util';
export * from './string.util';
export * from './time.util';
