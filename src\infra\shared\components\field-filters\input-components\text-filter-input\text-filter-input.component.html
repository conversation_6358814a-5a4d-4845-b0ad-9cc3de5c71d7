<!-- Text Filter Input Component -->
<div class="text-filter-input" *ngIf="requiresInput()">
  <mat-form-field appearance="outline" class="w-100">
    <mat-label>{{ 'FIELD_FILTERS.LABELS.VALUE' | translate }}</mat-label>

    <!-- Textarea cho large text fields -->
    <textarea
      *ngIf="isTextarea(); else regularInput"
      matInput
      [placeholder]="placeholder() | translate"
      [value]="inputValue()"
      [disabled]="disabled"
      (input)="onInputChange($event)"
      (blur)="onInputChange($event)"
      rows="3"
      class="text-filter-textarea">
    </textarea>

    <!-- Regular input cho text, email, phone, url -->
    <ng-template #regularInput>
      <input
        matInput
        [type]="inputType()"
        [placeholder]="placeholder() | translate"
        [value]="inputValue()"
        [disabled]="disabled"
        (input)="onInputChange($event)"
        (blur)="onInputChange($event)"
        class="text-filter-input-field">
    </ng-template>

    <!-- Error messages -->
    <mat-error *ngIf="!isValid()">
      <span *ngIf="field?.type === 'email'">
        {{ 'FIELD_FILTERS.VALIDATION.INVALID_EMAIL' | translate }}
      </span>
      <span *ngIf="field?.type === 'url'">
        {{ 'FIELD_FILTERS.VALIDATION.INVALID_URL' | translate }}
      </span>
      <span *ngIf="field?.type !== 'email' && field?.type !== 'url'">
        {{ 'FIELD_FILTERS.VALIDATION.REQUIRED_VALUE' | translate }}
      </span>
    </mat-error>
  </mat-form-field>
</div>

<!-- No input required message cho is_empty/is_not_empty operators -->
<div class="no-input-message" *ngIf="!requiresInput()">
  <small class="text-muted">
    {{ 'FIELD_FILTERS.MESSAGES.NO_INPUT_REQUIRED' | translate }}
  </small>
</div>
