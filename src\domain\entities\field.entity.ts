export interface BaseField {
  _id: number;
  label: string;
  isPublic?: boolean;
  isRequired?: boolean;
  tooltip?: string;
  profilePermissions?: Array<{
    _id: string;
    name: string;
    permission: 'read_write' | 'read' | 'none';
  }>;
}

export type Field =
  | TextField
  | EmailField
  | PhoneField
  | UrlField
  | TextareaField
  | NumberField
  | DecimalField
  | CurrencyField
  | PercentField
  | DateField
  | DateTimeField
  | PicklistField
  | MultiPicklistField
  | SearchField
  | UserField
  | UploadFileField
  | UploadImageField
  | CheckboxField;

export interface TextField extends BaseField {
  type: 'text';
  value: string;
  constraints?: {
    maxLength?: number;
    unique?: boolean;
  };
}

export interface EmailField extends BaseField {
  type: 'email';
  value: string;
  constraints?: {
    unique?: boolean;
  };
}

export interface PhoneField extends BaseField {
  type: 'phone';
  value: string;
  constraints?: {
    maxLength?: number;
    maxDigits?: number;
    unique?: boolean;
  };
}

export interface UrlField extends BaseField {
  type: 'url';
  value: string;
  constraints?: {
    maxLength?: number;
    unique?: boolean;
  };
}

export interface TextareaField extends BaseField {
  type: 'textarea';
  value: string;
  constraints?: {
    textType: 'small' | 'large' | 'rich';
    maxLength: number;
  };
}

export interface NumberField extends BaseField {
  type: 'number';
  value: string;
  constraints?: {
    maxDigits?: number;
  };
}

export interface DecimalField extends BaseField {
  type: 'decimal';
  value: string;
  constraints?: {
    maxDigits?: number;
    decimalPlaces?: number;
    useNumberSeparator?: boolean;
  };
}

export interface CurrencyField extends BaseField {
  type: 'currency';
  value: string;
  constraints?: {
    maxDigits?: number;
    decimalPlaces?: number;
    rounding?: 'normal' | 'off' | 'up' | 'down';
  };
}

export interface PercentField extends BaseField {
  type: 'percent';
  value: string;
  constraints?: {};
}

export interface DateField extends BaseField {
  type: 'date';
  value: string;
  constraints?: {};
}

export interface DateTimeField extends BaseField {
  type: 'datetime';
  value: string;
  constraints?: {};
}

export interface PicklistField extends BaseField {
  type: 'picklist';
  value: string;
  constraints?: {
    picklistValues: string[];
    sortOrder?: 'input' | 'alphabetical';
    defaultValue?: string;
  };
}

export interface MultiPicklistField extends BaseField {
  type: 'multi-picklist';
  value: string[];
  constraints?: {
    picklistValues: string[];
    sortOrder?: 'input' | 'alphabetical';
    defaultValue?: string[];
  };
}

export interface SearchField extends BaseField {
  type: 'search';
  value: string;
  constraints?: {
    searchModule: 'sales_quotes' | 'contacts' | 'transactions';
  };
}

export interface UserField extends BaseField {
  type: 'user';
  value: string | string[];
  constraints?: {
    userType: 'single' | 'multiple';
  };
}

export interface UploadFileField extends BaseField {
  type: 'upload-file';
  value: string | string[];
  constraints?: {
    allowMultipleFiles?: boolean;
    maxFiles?: number;
  };
}

export interface UploadImageField extends BaseField {
  type: 'upload-image';
  value: string | string[];
  constraints?: {
    maxImages: number;
  };
}

export interface CheckboxField extends BaseField {
  type: 'checkbox';
  value: boolean;
  constraints?: {
    enableByDefault?: boolean;
  };
}

export interface FieldPropertiesData {
  field: Field;
  availableModules?: Array<{
    _id: string;
    name: string;
  }>;
}