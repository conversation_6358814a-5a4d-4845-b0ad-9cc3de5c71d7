import { Injectable } from '@angular/core';
import { MatDialogConfig } from '@angular/material/dialog';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { SupplierFormModalComponent } from './supplier-form-modal.component';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';

/**
 * Service để mở modal thêm/sửa nhà cung cấp
 */
@Injectable({
  providedIn: 'root'
})
export class SupplierFormModalService {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal thêm/sửa nhà cung cấp
   * @param data Dữ liệu nhà cung cấp (nếu có)
   * @returns Promise với kết quả là thông tin nhà cung cấp sau khi lưu
   */
  async open(data?: { supplier?: Supplier }): Promise<Supplier | undefined> {
    const modalConfig: MatDialogConfig<{ supplier?: Supplier }> = {
      data: data || {},
      width: '800px',
      maxWidth: '95vw'
    };

    try {
      // Mở modal với type rõ ràng
      return await this.responsiveModalService.open<
        SupplierFormModalComponent,
        { supplier?: Supplier },
        Supplier
      >(SupplierFormModalComponent, modalConfig);
    } catch (error) {
      console.error('Lỗi khi mở modal nhà cung cấp:', error);
      return undefined;
    }
  }
}
