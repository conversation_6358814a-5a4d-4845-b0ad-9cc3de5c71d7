<!-- Checkbox Filter Input Component -->
<div class="checkbox-filter-input" *ngIf="requiresInput()">
  <mat-form-field appearance="outline" class="w-100">
    <mat-label>{{ 'FIELD_FILTERS.LABELS.CHECKBOX_STATE' | translate }}</mat-label>
    
    <mat-select
      [value]="selectedValue()"
      [disabled]="disabled"
      (selectionChange)="onSelectionChange($event)"
      class="checkbox-select">
      
      <mat-option
        *ngFor="let option of checkboxOptions"
        [value]="option.value"
        class="checkbox-option">
        
        <!-- Option content với icon -->
        <div class="option-content d-flex align-items-center">
          <mat-icon 
            [class]="option.value === 'selected' ? 'text-success' : 'text-muted'"
            class="me-2">
            {{ option.value === 'selected' ? 'check_box' : 'check_box_outline_blank' }}
          </mat-icon>
          <span>{{ option.labelKey | translate }}</span>
        </div>
      </mat-option>
    </mat-select>

    <!-- Selected value preview -->
    <mat-hint class="selected-preview">
      <div class="d-flex align-items-center">
        <mat-icon 
          [class]="getValueColorClass()"
          class="me-1 small-icon">
          {{ getValueIcon() }}
        </mat-icon>
        <small>{{ getDisplayText() | translate }}</small>
      </div>
    </mat-hint>
  </mat-form-field>

  <!-- Value explanation -->
  <div class="value-explanation">
    <small class="text-muted">
      <span *ngIf="selectedValue() === 'selected'">
        {{ 'FIELD_FILTERS.EXPLANATIONS.CHECKBOX_SELECTED' | translate }}
      </span>
      <span *ngIf="selectedValue() === 'not_selected'">
        {{ 'FIELD_FILTERS.EXPLANATIONS.CHECKBOX_NOT_SELECTED' | translate }}
      </span>
    </small>
  </div>
</div>

<!-- No input required message (không có case này cho checkbox) -->
<div class="no-input-message" *ngIf="!requiresInput()">
  <small class="text-muted">
    {{ 'FIELD_FILTERS.MESSAGES.NO_INPUT_REQUIRED' | translate }}
  </small>
</div>
