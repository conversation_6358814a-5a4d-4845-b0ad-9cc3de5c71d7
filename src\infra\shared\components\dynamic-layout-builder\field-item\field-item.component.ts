import { Component, Input, Output, EventEmitter, signal, inject, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { LayoutField, FieldValue } from '@shared/models/view/dynamic-layout-builder.model';

// Modal Services
import { FieldPropertiesModalService } from '@shared/modals/field-properties/field-properties-modal.service';
import { FieldPermissionModalService } from '@shared/modals/field-permission/field-permission-modal.service';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal/confirm-modal.service';

// Modal Data Types
import { FieldPropertiesData, CustomField } from '@shared/models/view/field-properties.model';
import { FieldPermissionModalData } from '@shared/modals/field-permission/field-permission-modal.component';
import { Profile } from '@shared/models/view/field-permission.model';
import { ConfirmModalData } from '@shared/modals/common/confirm-modal/confirm-modal.component';

/**
 * Component hiển thị một field item trong bảng
 * Hỗ trợ inline editing cho label và menu actions
 */
@Component({
  selector: 'app-field-item',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatInputModule,
    MatFormFieldModule,
    MatDividerModule,
    TranslateModule
  ],
  templateUrl: './field-item.component.html',
  styleUrls: ['./field-item.component.scss']
})
export class FieldItemComponent implements OnInit, OnChanges {

  /**
   * Inject modal services
   */
  private fieldPropertiesModalService = inject(FieldPropertiesModalService);
  private fieldPermissionModalService = inject(FieldPermissionModalService);
  private confirmModalService = inject(ConfirmModalService);

  /**
   * Field data input
   */
  @Input() fieldData: LayoutField = {
    id: 0,
    _id: 0,
    type: '',
    label: '',
    value: null,
    order: 1
  };

  /**
   * Field signal để reactive update UI
   */
  field = signal<LayoutField>({
    id: 0,
    _id: 0,
    type: '',
    label: '',
    value: null,
    order: 1
  });

  /**
   * Event khi label thay đổi
   */
  @Output() labelChanged = new EventEmitter<{ field: LayoutField; newLabel: string }>();

  /**
   * Event khi toggle required status
   */
  @Output() requiredToggled = new EventEmitter<{ field: LayoutField; isRequired: boolean }>();

  /**
   * Event khi edit properties
   */
  @Output() propertiesEdit = new EventEmitter<LayoutField>();

  /**
   * Event khi set permission
   */
  @Output() permissionSet = new EventEmitter<LayoutField>();

  /**
   * Event khi delete field
   */
  @Output() fieldDeleted = new EventEmitter<LayoutField>();

  /**
   * Event khi bắt đầu drag field
   */
  @Output() dragStarted = new EventEmitter<LayoutField>();

  /**
   * Event khi kết thúc drag field
   */
  @Output() dragEnded = new EventEmitter<LayoutField>();

  /**
   * Signal để quản lý trạng thái đang edit label
   */
  isEditingLabel = signal(false);

  /**
   * Signal để quản lý trạng thái đang drag
   */
  isDragging = signal(false);

  /**
   * Label đang được edit
   */
  editingLabel = '';

  /**
   * Lifecycle: Component initialization
   */
  ngOnInit(): void {
    // Sync field signal với fieldData input lần đầu
    this.field.set(this.fieldData);
  }

  /**
   * Lifecycle: Input changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Sync field signal khi fieldData input thay đổi
    if (changes['fieldData'] && changes['fieldData'].currentValue) {
      this.field.set(changes['fieldData'].currentValue);
      console.log('🔄 Field data updated:', changes['fieldData'].currentValue.label);
    }
  }

  /**
   * Bắt đầu edit label
   */
  startEditLabel(): void {
    this.editingLabel = this.field().label;
    this.isEditingLabel.set(true);

    // Focus vào input sau khi render
    setTimeout(() => {
      const input = document.querySelector('.label-input input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    });
  }

  /**
   * Lưu label mới
   */
  saveLabel(): void {
    if (this.editingLabel.trim() && this.editingLabel.trim() !== this.field().label) {
      this.labelChanged.emit({
        field: this.field(),
        newLabel: this.editingLabel.trim()
      });
    }
    this.isEditingLabel.set(false);
  }

  /**
   * Hủy edit label
   */
  cancelEditLabel(): void {
    this.editingLabel = '';
    this.isEditingLabel.set(false);
  }

  /**
   * Toggle required status
   */
  onToggleRequired(): void {
    this.requiredToggled.emit({
      field: this.field(),
      isRequired: !this.field().isRequired
    });
  }

  /**
   * Mở modal edit properties
   */
  async onEditProperties(): Promise<void> {
    try {
      // Chuyển đổi LayoutField sang CustomField format
      const customField = {
        _id: this.field().id || this.field()._id || 0,
        label: this.field().label,
        type: this.field().type,
        value: this.convertFieldValue(this.field().value),
        isPublic: this.field().isPublic || false,
        isRequired: this.field().isRequired || this.field().required || false,
        tooltip: this.field().description || '',
        constraints: this.field().constraints || {}
      } as CustomField;

      // Tạo dữ liệu cho modal
      const modalData: FieldPropertiesData = {
        field: customField,
        availableModules: [
          { _id: 'sales_quotes', name: 'Sales Quotes' },
          { _id: 'contacts', name: 'Contacts' },
          { _id: 'transactions', name: 'Transactions' }
        ]
      };

      // Mở modal và nhận kết quả
      const result = await this.fieldPropertiesModalService.open(modalData);

      if (result) {
        // Cập nhật field hiện tại với properties mới
        const updatedField: LayoutField = {
          ...this.field(),
          label: result.label,
          type: result.type,
          value: result.value,
          isRequired: result.isRequired,
          isPublic: result.isPublic,
          description: result.tooltip,
          constraints: result.constraints
        };

        // Cập nhật field signal để trigger UI update
        this.field.set(updatedField);

        // Cập nhật fieldData input
        this.fieldData = updatedField;

        // Emit event với field đã được cập nhật
        this.propertiesEdit.emit(updatedField);

        console.log('✅ Field properties updated:', updatedField);
      } else {
        console.log('❌ Field properties edit cancelled');
      }
    } catch (error) {
      console.error('❌ Error opening field properties modal:', error);
    }
  }

  /**
   * Mở modal set permission
   */
  async onSetPermission(): Promise<void> {
    try {
      // Tạo mock profiles cho testing (trong thực tế sẽ lấy từ API)
      const mockProfiles: Profile[] = [
        { _id: 'admin', name: 'Administrator', permission: 'read_write' },
        { _id: 'manager', name: 'Manager', permission: 'read_write' },
        { _id: 'user', name: 'Standard User', permission: 'read' },
        { _id: 'guest', name: 'Guest User', permission: 'none' }
      ];

      // Tạo dữ liệu cho modal
      const modalData: FieldPermissionModalData = {
        fieldName: this.field().label,
        profiles: mockProfiles
      };

      // Mở modal và nhận kết quả
      const result = await this.fieldPermissionModalService.open(modalData);

      if (result && result.length > 0) {
        // Cập nhật field hiện tại với permissions mới
        const updatedField: LayoutField = {
          ...this.field(),
          // Lưu permissions vào constraints hoặc một property khác
          constraints: {
            ...this.field().constraints,
            permissions: result.map(profile => ({
              profileId: profile._id,
              permission: 'read' // Default permission, có thể customize sau
            }))
          }
        };

        // Cập nhật field signal để trigger UI update
        this.field.set(updatedField);

        // Cập nhật fieldData input
        this.fieldData = updatedField;

        // Emit event với field đã được cập nhật
        this.permissionSet.emit(updatedField);

        console.log('✅ Field permissions updated:', result);
      } else {
        console.log('❌ Field permission setup cancelled');
      }
    } catch (error) {
      console.error('❌ Error opening field permission modal:', error);
    }
  }

  /**
   * Xóa field
   */
  async onDeleteField(): Promise<void> {
    try {
      // Tạo dữ liệu cho modal xác nhận
      const confirmData: ConfirmModalData = {
        title: 'DYNAMIC_LAYOUT_BUILDER.CONFIRM_DELETE_FIELD_TITLE',
        message: `DYNAMIC_LAYOUT_BUILDER.CONFIRM_DELETE_FIELD_MESSAGE`,
        confirmText: 'COMMON.DELETE',
        cancelText: 'COMMON.CANCEL',
        confirmColor: 'warn'
      };

      // Mở modal xác nhận và nhận kết quả
      const confirmed = await this.confirmModalService.confirm(confirmData);

      if (confirmed) {
        // User xác nhận xóa - emit event với field hiện tại
        this.fieldDeleted.emit(this.field());
        console.log('✅ Field deleted:', this.field().label);
      } else {
        // User hủy bỏ - không làm gì
        console.log('❌ Field deletion cancelled');
      }
    } catch (error) {
      console.error('❌ Error opening confirm modal:', error);
    }
  }

  /**
   * Bắt đầu drag field
   * Được gọi khi user bắt đầu kéo field item
   */
  onDragStart(): void {
    this.isDragging.set(true);
    this.dragStarted.emit(this.field());
    console.log('🚀 Field drag started:', this.field().label);
  }

  /**
   * Kết thúc drag field
   * Được gọi khi user thả field item
   */
  onDragEnd(): void {
    this.isDragging.set(false);
    this.dragEnded.emit(this.field());
    console.log('🏁 Field drag ended:', this.field().label);
  }

  /**
   * Lấy icon cho field type (đồng bộ với sidebar field selector)
   */
  getFieldIcon(fieldType: string): string {
    const iconMap: { [key: string]: string } = {
      'text': 'text_fields',
      'number': 'numbers',
      'integer': 'numbers',
      'decimal': 'numbers',
      'percent': 'percent',
      'currency': 'attach_money',
      'email': 'email',
      'phone': 'phone',
      'textarea': 'notes',
      'date': 'calendar_today',
      'datetime': 'schedule',
      'file': 'attach_file',
      'image': 'image',
      'checkbox': 'check_box',
      'radio': 'radio_button_checked',
      'select': 'arrow_drop_down',
      'picklist': 'arrow_drop_down',
      'multi-picklist': 'checklist',
      'url': 'link',
      'size': 'straighten',
      'color': 'palette',
      'brand': 'business',
      'category': 'category'
    };
    return iconMap[fieldType] || 'help_outline';
  }

  /**
   * Lấy label hiển thị cho field type
   */
  getFieldTypeLabel(type: string): string {
    const typeMap: { [key: string]: string } = {
      'text': 'Text',
      'integer': 'Integer',
      'decimal': 'Decimal',
      'percent': 'Percent',
      'currency': 'Currency',
      'date': 'Date',
      'datetime': 'DateTime',
      'email': 'Email',
      'phone': 'Phone',
      'picklist': 'Picklist',
      'multi-picklist': 'Multi Picklist',
      'url': 'URL',
      'textarea': 'Text Area',
      'checkbox': 'Checkbox'
    };

    return typeMap[type] || type;
  }

  /**
   * Chuyển đổi FieldValue sang format phù hợp với CustomField
   */
  private convertFieldValue(value: FieldValue): string | boolean | string[] {
    if (value === null || value === undefined) {
      return '';
    }

    if (typeof value === 'string' || typeof value === 'boolean') {
      return value;
    }

    if (Array.isArray(value)) {
      return value.map(v => String(v));
    }

    if (value instanceof Date) {
      return value.toISOString();
    }

    return String(value);
  }
}
