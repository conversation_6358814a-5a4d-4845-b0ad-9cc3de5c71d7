import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, AfterViewInit, ViewChild, ElementRef, ChangeDetectorRef, ViewEncapsulation, ViewChildren, QueryList } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NavigationEnd, RouterModule } from '@angular/router';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { TranslateModule } from '@ngx-translate/core';

// Import Swiper types
import { SwiperContainer } from 'swiper/element/bundle';
import { Swiper } from 'swiper';
import { FreeMode } from 'swiper/modules';

import { NavigationItem } from '@config/navigation.config';
import { ViewportService } from '@/core/services/viewport.service';
import { MatMenuModule } from '@angular/material/menu';
import { Subject } from 'rxjs';
import { NavigationService } from '../navigation.service';
import { RouterEventService } from '@/core/services/router_events.service';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-sub-navigation',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatBottomSheetModule,
    MatMenuModule,
    MatTooltipModule
  ],
  templateUrl: './sub-navigation.component.html',
  styleUrls: ['./sub-navigation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class SubNavigationComponent implements AfterViewInit, OnDestroy {
  // ViewChild trỏ tới swiper-container
  @ViewChild('swiperContainer') swiperContainer?: ElementRef<SwiperContainer>;
  viewport$!: ViewportService['viewport$'];

  private swiper!: Swiper;
  private destroy$ = new Subject<void>();

  private lastNavigationModule!: string | undefined;

  get topLevelItems() { return this.navigationService.topLevelItems; }
  get activeNavigation() { return this.navigationService.activeNavigation; }


  constructor(
    private navigationService: NavigationService,
    private routerEventService: RouterEventService,
    private viewportService: ViewportService
  ) {
    this.viewport$ = viewportService.viewport$;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if(this.swiper) {
      this.swiper.destroy();
    }
  }


  ngAfterViewInit(): void {
    this.initSwiper();
    this.listen(this.destroy$);
  }


  /**
   * Khởi tạo Swiper
   */
  initSwiper(): void {
    if(this.swiper) {
      return;
    }

    if (this.swiperContainer?.nativeElement) {
      this.swiper = new Swiper(this.swiperContainer?.nativeElement, {
        modules: [FreeMode],
        slidesPerView: 'auto',
        spaceBetween: 0,
        freeMode: true,
        navigation: {
          disabledClass: 'disabled',
          nextEl: '.sub-nav-next',
          prevEl: '.sub-nav-prev',
        },
        on: {
          init: () => {
            if (this.swiperContainer?.nativeElement) {
              this.swiperContainer.nativeElement.classList.add('swiper-initialized');
            }
          }
        }
      });

      /**
       * không setTimeout hàm này sẽ không ổn định
       * lúc được lúc không dù đã vào sự kiện init
       */
      setTimeout(() => {
        this.swiper?.update?.();
        this.updateView();
      }, 100);


    }
  }

  updateView() {
    /**
     * khi vào 1 page không có sub nav
     * sau đó chuyển sang 1 page có sub nav
     * nó sẽ không có this.swiperContainer?.nativeElement để init
     */
    if(!this.swiper) {
      return this.initSwiper();
    }

    if(this.lastNavigationModule !== this.navigationService.activeNavigation()?.module) {
      this.lastNavigationModule = this.navigationService.activeNavigation()?.module;
      this.swiper?.update?.();
    }

    this.scrollToActiveSlide();
  }

  /**
   * Lắng nghe sự kiện thay đổi route
   */
  listen(destroy$: Subject<void>): void {
    this.routerEventService.observeNavigationEvent(destroy$).subscribe(event => {
      if(event instanceof NavigationEnd) {
        setTimeout(() => {
          this.updateView();
        }, 100);
      }
    });

    this.viewportService.observeViewport(destroy$).subscribe(event => {
      setTimeout(() => {
        this.updateView();
      }, 100);
    });
  }


  /**
   * Cuộn đến slide đang active
   */
  scrollToActiveSlide(): void {
    const topLevelItemLength = this.navigationService.topLevelItems()?.length || 0;

    if (!this.swiper || topLevelItemLength === 0) {
      return;
    }

    let scrollToIndex = this.navigationService.topLevelItems()
      .findIndex(item => this.navigationService.isNavigationItemActive(item));


    if(
      scrollToIndex !== -1 &&
      topLevelItemLength > 3 &&
      scrollToIndex > 0 &&
      scrollToIndex < (topLevelItemLength - 2)
    ) {
      scrollToIndex -= 1;
    }


    if (scrollToIndex !== -1) {
      this.swiper.slideTo(scrollToIndex, 300);
    }
  }

  /**
   * Kiểm tra xem một item có đang active hay không
   * @param item Item cần kiểm tra
   * @returns true nếu item đang active
   */
  isNavigationItemActive(item: NavigationItem): boolean {
    return this.navigationService.isNavigationItemActive(item);
  }

  /**
   * Mở bottom sheet hiển thị các sub-navigation
   * @param items Danh sách các item con
   * @param event Sự kiện click
   */
  openSubSheet(navigation: NavigationItem, event: MouseEvent): void {
    event.preventDefault();
    this.navigationService.openSubNavigationSheet(navigation.items, navigation);
  }

  /**
   * Hàm trackBy cho *ngFor
   * @param index Index của item
   * @param item Item navigation
   * @returns Khóa duy nhất cho item
   */
  trackByTopItem(index: number, item: NavigationItem): string {
    return this.navigationService.trackByFn(index, item);
  }
}
