@use './shared/styles/_variable' as *;

html {
  --mat-option-label-text-font: $font;
  --mdc-plain-tooltip-supporting-text-font: $font;
}
.material-symbols-outlined {
  font-family: 'Material Symbols Outlined' !important;
}

body {
  margin: 0;
  background-color: $bodyBackgroundColor;
  font-family: $font;
  color: $bodyTextColor;
}
a {
  text-decoration: none;
  color: var(--tw-gray-700);
}
.btn {
  border-radius: 3rem;

  &-primary {
    color: #000;
    background-color: #fbda5f;

    &:hover, 
    &:focus, 
    &:active, 
    &.active {
      color: #000;
      background-color: #fbda5f;
    }
  }
}



app-root,
main {
  display: block;
  position: relative;
  width: 100%;
}

main {
  width: 100%;
  margin: auto;
  padding-bottom: #{$mobileBottomNavHeight + 20}px;
}



.app-main {
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .content {
    padding: 20px 20px 20px 20px;

    li {
      display: block;
      padding: 10px 0;
    }

    .icon {
      min-width: 25px;
      color: #969c9f;
    }

    a {
      color: #384753;
      font-weight: 700;
      font-size: 16px;
      text-transform: uppercase;
    }

    .indent {
      margin: 10px 0 0 25px;

      a {
        color: #6b6c6d;
        padding: 7px 0;
        font-weight: 400;
        text-transform: capitalize;

        &:hover {
          color: #384753;
        }
      }
      .icon {
        font-size: 14px;
        min-width: 10px;
      }
    }
  }
}


@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  .main {
    padding-bottom: 0;
  }
  .main-container {
    width: calc(100vw - 30px);
    margin: auto;
  }

  .app-main {
    background-color: #fff !important;
    margin-right: 15px;
    margin-top: #{$desktopHeaderHeight + 20}px;
    // margin-bottom: 15px;


    .mat-drawer {
      top: 30px;
      border-radius: 0 !important;
    }
  }
  .app-main-box {
    // border: 1px solid #dbdfe9;
    // box-shadow: 0 0 4px 0 rgba(0, 0, 0, .04), 0 4px 14px 0 rgba(33, 50, 166, .06);
    box-shadow: 0 -2px .875rem 0 rgba(38, 43, 67, .16);
    scrollbar-color: #e8e8e8 transparent;
    border-radius: 8px 8px 0 0;
    overflow-y: auto;
  }
}

@media screen and (min-width: #{$breakpointMaxWidth}px) {
  .main-container {
    width: #{$breakpointMaxWidth - 20}+px;
    margin: auto;
  }
}
