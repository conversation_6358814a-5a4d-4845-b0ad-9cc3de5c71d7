import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { SimpleNoteModalComponent } from './simple-note-modal.component';
import { SimpleNoteModalData, SimpleNoteModalResult } from './models/simple-note-modal.model';

/**
 * Service để mở modal ghi chú đơn giản
 */
@Injectable({
  providedIn: 'root'
})
export class SimpleNoteModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal ghi chú đơn giản
   * @param data Dữ liệu cho modal (nội dung ghi chú hiện tại)
   * @returns Promise<SimpleNoteModalResult> Kết quả ghi chú hoặc undefined nếu hủy
   */
  async open(data: SimpleNoteModalData = ''): Promise<SimpleNoteModalResult> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '95vw'
      };
      
      const result = await this.responsiveModalService.open<
        SimpleNoteModalComponent,
        SimpleNoteModalData,
        SimpleNoteModalResult
      >(SimpleNoteModalComponent, modalConfig);
      
      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú đơn giản:', error);
      return undefined;
    }
  }
}
