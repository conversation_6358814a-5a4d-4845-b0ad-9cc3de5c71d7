import { ChangeDetectionStrategy, Component, Inject, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

/**
 * Interface cho dữ liệu lô hàng
 */
export interface BatchData {
  batchNumber: string;
  manufacturingDate: Date;
  expiryDate: Date | null;
  quantity: number | null;
}


/**
 * Component modal quản lý thông tin lô hàng
 */
@Component({
  selector: 'app-batch-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    TranslateModule,
  ],
  templateUrl: './batch-modal.component.html',
  styleUrls: ['./batch-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BatchModalComponent implements OnInit {
  // Form group quản lý dữ liệu lô hàng
  batchForm!: FormGroup;

  // Dữ liệu đầu vào
  data: BatchData | null = null;

  private fb = inject(FormBuilder);

  constructor(
    @Inject(MAT_DIALOG_DATA) private dialogData: BatchData | null,
    @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: BatchData | null,
    private dialogRef?: MatDialogRef<BatchModalComponent>,
    private bottomSheetRef?: MatBottomSheetRef<BatchModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialog hoặc bottom sheet
    this.data = dialogData || bottomSheetData;
  }

  ngOnInit(): void {
    // Khởi tạo form với các validators
    this.initForm();

    // Nếu có dữ liệu được truyền vào, cập nhật form
    if (this.data) {
      this.batchForm.patchValue(this.data);
    }
  }

  /**
   * Khởi tạo form với các validators
   */
  private initForm(): void {
    this.batchForm = this.fb.group({
      batchNumber: ['', Validators.required],
      manufacturingDate: [new Date()], // Mặc định là ngày hôm nay
      expiryDate: [null, Validators.required],
      quantity: [null, [Validators.required, Validators.min(1)]]
    });
  }

  /**
   * Đóng modal và trả về dữ liệu lô hàng nếu form hợp lệ
   */
  save(): void {
    if (this.batchForm.valid) {
      const result: BatchData = this.batchForm.value;
      if (this.dialogRef) {
        this.dialogRef.close(result);
      } else if (this.bottomSheetRef) {
        this.bottomSheetRef.dismiss(result);
      }
    } else {
      // Đánh dấu tất cả các trường là đã touched để hiển thị lỗi nếu có
      this.batchForm.markAllAsTouched();
    }
  }

  /**
   * Đóng modal mà không lưu dữ liệu
   */
  cancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }
}
