import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError, delay, tap, switchMap, forkJoin, map } from 'rxjs';
import { InventoryCheckRepository } from '../../../../../domain/repositories/warehouse/inventory-check.repository';
import { InventoryCheck } from '../../../../../domain/entities/inventory-check.entity';

// Import mock data
import { mockWarehouseList, mockEmployeeList, mockWarehouseLocations,  mockCategoryList } from '@mock/shared/list.mock';
import { mockProductList } from '@/mock/shared/product.mock';

/**
 * Implementation của InventoryCheckRepository
 * Triển khai các phương thức abstract từ repository
 */
@Injectable()
export class InventoryCheckRepositoryImpl implements InventoryCheckRepository {
  // Mock data
  private warehouseList = mockWarehouseList;
  private employeeList = mockEmployeeList;
  private warehouseLocations = mockWarehouseLocations;
  private productList = mockProductList;

  /**
   * Khởi tạo một phiếu kiểm kho mới
   */
  initInventoryCheck(): InventoryCheck {
    return {
      _id: this.generateId(),
      warehouse: {
        _id: '',
        name: ''
      },
      items: [],
      summary: {
        totalIncrease: {
          quantity: 0,
          value: 0
        },
        totalDecrease: {
          quantity: 0,
          value: 0
        },
        totalDifference: {
          quantity: 0,
          value: 0
        }
      },
      status: 'draft',
      createdBy: {
        _id: '',
        name: ''
      },
      createdAt: new Date(),
      adjustmentReason: {
        reasonId: '',
        description: ''
      }
    };
  }

  /**
   * Tải phiếu kiểm kho theo ID
   * @param id ID của phiếu kiểm kho
   */
  loadInventoryCheck(id: string): Observable<InventoryCheck> {
    if (!id) {
      return throwError(() => new Error('ID không hợp lệ'));
    }

    // Mô phỏng API call để tải phiếu kiểm kho
    console.log(`Tải phiếu kiểm kho với ID: ${id}`);

    // Tạo dữ liệu mẫu
    const mockInventoryCheck: InventoryCheck = {
      _id: id,
      warehouse: {
        _id: 'WH001',
        name: 'Kho chính'
      },
      items: [
        {
          product: {
            productId: 'P001',
            name: 'Sản phẩm 1',
            sku: 'SKU001',
            cost: 100000,
            price: 120000
          },
          stockQuantity: 10,
          actualQuantity: 8,
          differenceQuantity: -2,
          differenceValue: -200000
        },
        {
          product: {
            productId: 'P002',
            name: 'Sản phẩm 2',
            sku: 'SKU002',
            cost: 150000,
            price: 180000
          },
          stockQuantity: 5,
          actualQuantity: 7,
          differenceQuantity: 2,
          differenceValue: 300000
        }
      ],
      summary: {
        totalIncrease: {
          quantity: 2,
          value: 300000
        },
        totalDecrease: {
          quantity: 2,
          value: 200000
        },
        totalDifference: {
          quantity: 0,
          value: 100000
        }
      },
      status: 'draft',
      createdBy: {
        _id: 'U001',
        name: 'Nhân viên 1'
      },
      createdAt: new Date(),
      adjustmentReason: {
        reasonId: 'R001',
        description: 'Kiểm kho định kỳ'
      }
    };

    return of(mockInventoryCheck).pipe(
      delay(1000) // Giả lập độ trễ mạng
    );
  }

  /**
   * Lấy danh sách phiếu kiểm kho
   * @param page Số trang
   * @param pageSize Số lượng item trên mỗi trang
   */
  getInventoryCheckList(page: number, pageSize: number): Observable<{
    items: InventoryCheck[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    // Mô phỏng API call để lấy danh sách phiếu kiểm kho
    console.log(`Lấy danh sách phiếu kiểm kho - Trang: ${page}, Số lượng: ${pageSize}`);

    // Tạo dữ liệu mẫu
    const mockInventoryCheckList = {
      items: [
        {
          _id: 'IC001',
          warehouse: {
            _id: 'WH001',
            name: 'Kho chính'
          },
          summary: {
            totalIncrease: {
              quantity: 5,
              value: 500000
            },
            totalDecrease: {
              quantity: 3,
              value: 300000
            },
            totalDifference: {
              quantity: 2,
              value: 200000
            }
          },
          status: 'completed' as const,
          createdBy: {
            _id: 'U001',
            name: 'Nhân viên 1'
          },
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 ngày trước
          items: [],
          adjustmentReason: {
            reasonId: 'R001',
            description: 'Kiểm kho định kỳ'
          }
        },
        {
          _id: 'IC002',
          warehouse: {
            _id: 'WH002',
            name: 'Kho phụ'
          },
          summary: {
            totalIncrease: {
              quantity: 2,
              value: 200000
            },
            totalDecrease: {
              quantity: 1,
              value: 100000
            },
            totalDifference: {
              quantity: 1,
              value: 100000
            }
          },
          status: 'draft' as const,
          createdBy: {
            _id: 'U002',
            name: 'Nhân viên 2'
          },
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 ngày trước
          items: [],
          adjustmentReason: {
            reasonId: 'R002',
            description: 'Kiểm kho định kỳ'
          }
        }
      ],
      total: 2,
      page: 1,
      pageSize: 10
    };

    return of(mockInventoryCheckList).pipe(
      delay(1000) // Giả lập độ trễ mạng
    );
  }

  /**
   * Lưu phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho cần lưu
   * @param status Trạng thái của phiếu kiểm kho
   */
  saveInventoryCheck(
    inventoryCheck: InventoryCheck,
    status: 'draft' | 'completed'
  ): Observable<{
    _id: string;
    createdAt?: Date;
    updatedAt?: Date;
  }> {
    // Cập nhật trạng thái
    const updatedInventoryCheck = {
      ...inventoryCheck,
      status,
      updatedAt: new Date()
    };

    // Nếu trạng thái là completed, cập nhật tồn kho
    if (status === 'completed') {
      // Trước khi gửi API, cập nhật trạng thái kho
      return this.updateWarehouseStock(updatedInventoryCheck).pipe(
        switchMap(() => {
          // Sau khi cập nhật kho thành công, lưu phiếu kiểm kho
          return this.saveInventoryCheckToServer(updatedInventoryCheck);
        })
      );
    }

    // Nếu trạng thái là draft, chỉ lưu phiếu kiểm kho
    return this.saveInventoryCheckToServer(updatedInventoryCheck);
  }

  /**
   * Thêm sản phẩm vào phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   */
  addProductToInventory(
    inventoryCheck: InventoryCheck,
    productId: string
  ): Observable<boolean> {
    // Tìm sản phẩm trong danh sách sản phẩm
    const product = this.productList.find(p => p.productId === productId);
    if (!product) {
      return throwError(() => new Error('Không tìm thấy sản phẩm'));
    }

    // Kiểm tra xem sản phẩm đã tồn tại trong danh sách kiểm kho chưa
    const existingItemIndex = inventoryCheck.items.findIndex(item =>
      item.product.productId === productId
    );

    if (existingItemIndex !== -1) {
      return of(false); // Sản phẩm đã tồn tại
    }

    // Mô phỏng API call để lấy thông tin tồn kho của sản phẩm
    return of(10).pipe( // Giả sử tồn kho là 10
      delay(500), // Giả lập độ trễ mạng
      map(stockQuantity => {
        // Tạo item mới
        const newItem = {
          product: {
            productId: product.productId,
            name: product.name,
            sku: product.sku,
            cost: product.cost,
            price: product.price
          },
          stockQuantity,
          differenceQuantity: 0,
          differenceValue: 0
        };

        // Thêm vào danh sách
        inventoryCheck.items.push(newItem);

        return true;
      })
    );
  }

  /**
   * Xóa sản phẩm khỏi phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   */
  removeProductFromInventory(
    inventoryCheck: InventoryCheck,
    productId: string
  ): boolean {
    const initialLength = inventoryCheck.items.length;
    inventoryCheck.items = inventoryCheck.items.filter(item =>
      item.product.productId !== productId
    );

    return initialLength !== inventoryCheck.items.length;
  }

  /**
   * Cập nhật số lượng thực tế của sản phẩm
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   * @param quantity Số lượng thực tế
   */
  updateActualQuantity(
    inventoryCheck: InventoryCheck,
    productId: string,
    quantity: number
  ): boolean {
    const itemIndex = inventoryCheck.items.findIndex(item =>
      item.product.productId === productId
    );

    if (itemIndex === -1) {
      return false;
    }

    const item = inventoryCheck.items[itemIndex];
    item.actualQuantity = quantity;
    item.differenceQuantity = quantity - item.stockQuantity;
    item.differenceValue = item.differenceQuantity * (item.product.cost || 0);

    return true;
  }

  /**
   * Lấy danh sách kho
   */
  getWarehouses(): Observable<Array<{
    _id: string;
    name: string;
  }>> {
    return of(this.warehouseList);
  }

  /**
   * Lấy danh sách vị trí kho theo warehouseId
   * @param warehouseId ID của kho
   */
  getWarehouseLocations(warehouseId: string): Observable<Array<{
    _id: string;
    name: string;
    warehouse: { _id: string; name: string; };
  }>> {
    // Lọc các vị trí theo kho
    const filteredLocations = mockWarehouseLocations.filter(loc => loc.warehouse._id === warehouseId);

    return of(filteredLocations).pipe(
      delay(500) // Giả lập độ trễ mạng
    );
  }

  /**
   * Lấy danh sách danh mục sản phẩm
   */
  getProductCategories(): Observable<Array<{
    _id: string;
    name: string;
  }>> {
    return of(mockCategoryList).pipe(
      delay(500) // Giả lập độ trễ mạng
    );
  }

  /**
   * Cập nhật tồn kho dựa trên kết quả kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   */
  updateWarehouseStock(inventoryCheck: InventoryCheck): Observable<{
    success: boolean;
    message: string;
  }> {
    if (!inventoryCheck || !inventoryCheck.warehouse || !inventoryCheck._id) {
      return throwError(() => new Error('Thông tin kiểm kho không hợp lệ'));
    }

    // Kiểm tra xem có sản phẩm nào cần cập nhật không
    const itemsToUpdate = inventoryCheck.items.filter(item =>
      item.actualQuantity !== undefined &&
      item.actualQuantity !== null &&
      item.differenceQuantity !== 0
    );

    if (itemsToUpdate.length === 0) {
      // Không có sản phẩm nào cần cập nhật
      return of({ success: true, message: 'Không có sản phẩm nào cần cập nhật tồn kho' });
    }

    // Mô phỏng API call để cập nhật tồn kho
    return of({ success: true, message: 'Cập nhật tồn kho thành công' }).pipe(
      delay(1000) // Giả lập độ trễ mạng
    );
  }

  /**
   * Lưu phiếu kiểm kho lên server
   * @param inventoryCheck Phiếu kiểm kho cần lưu
   */
  private saveInventoryCheckToServer(inventoryCheck: InventoryCheck): Observable<{
    _id: string;
    createdAt?: Date;
    updatedAt?: Date;
  }> {
    // Mô phỏng API call để lưu phiếu kiểm kho
    console.log('Lưu phiếu kiểm kho:', inventoryCheck);

    // Kiểm tra xem đã có _id chưa để quyết định tạo mới hay cập nhật
    if (inventoryCheck._id && inventoryCheck._id !== '') {
      // Cập nhật phiếu kiểm kho đã tồn tại
      return of({
        _id: inventoryCheck._id,
        createdAt: inventoryCheck.createdAt,
        updatedAt: new Date()
      }).pipe(
        delay(1000) // Giả lập độ trễ mạng
      );
    } else {
      // Tạo mới phiếu kiểm kho
      const newId = `IC${new Date().getTime()}`;
      return of({
        _id: newId,
        createdAt: new Date(),
        updatedAt: new Date()
      }).pipe(
        delay(1000) // Giả lập độ trễ mạng
      );
    }
  }

  /**
   * Tạo ID ngẫu nhiên
   */
  private generateId(): string {
    return `temp_${new Date().getTime()}_${Math.floor(Math.random() * 1000)}`;
  }
}
