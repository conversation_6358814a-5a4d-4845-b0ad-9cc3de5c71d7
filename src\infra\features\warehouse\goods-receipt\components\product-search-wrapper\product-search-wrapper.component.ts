import { ChangeDetectionStrategy, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { SharedProductSearchComponent } from '@/shared/components/product-selection/product-search/product-search.component';
import { EmbeddedProduct } from '../../models/api/goods-receipt.dto';
import { ProductSearchWrapperService } from './product-search-wrapper.service';

/**
 * Component bao bọc tìm kiếm sản phẩm cho goods-receipt
 */
@Component({
  selector: 'app-product-search-wrapper',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    SharedProductSearchComponent
  ],
  templateUrl: './product-search-wrapper.component.html',
  styleUrls: ['./product-search-wrapper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductSearchWrapperComponent implements OnInit {
  /**
   * Sự kiện khi chọn sản phẩm
   */
  @Output() productSelected = new EventEmitter<EmbeddedProduct>();

  /**
   * Sự kiện khi nhấn nút thêm từ danh mục
   */
  @Output() addFromCategory = new EventEmitter<void>();

  /**
   * Sự kiện khi nhấn nút in
   */
  @Output() printList = new EventEmitter<void>();

  constructor(private productSearchWrapperService: ProductSearchWrapperService) { }

  ngOnInit(): void {
  }

  /**
   * Xử lý khi chọn sản phẩm từ shared component
   */
  onProductSelected(product: any): void {
    // Chuyển đổi product thành EmbeddedProduct nếu cần
    const embeddedProduct: EmbeddedProduct = {
      productId: product.productId,
      name: product.name,
      sku: product.sku,
      price: product.price,
      cost: product.cost
    };

    this.productSelected.emit(embeddedProduct);
  }

  /**
   * Xử lý khi nhấn nút thêm từ danh mục
   */
  onAddFromCategory(): void {
    this.addFromCategory.emit();
  }

  /**
   * Xử lý khi nhấn nút in
   */
  onPrintList(): void {
    // Gọi hàm mock in phiếu nhập kho
    this.productSearchWrapperService.printReceipt();
    this.printList.emit();
  }
}
