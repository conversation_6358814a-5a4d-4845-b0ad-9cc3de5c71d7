<!-- Header -->
<h2 mat-dialog-title>
  {{ 'EDIT_FIELD_PROPERTIES.TITLE' | translate: { fieldType: fieldTypeName() } }}
</h2>

<!-- Body -->
<mat-dialog-content class="field-properties-content">
  <form [formGroup]="form" class="field-properties-form">

    <!-- Common Fields Section -->
    <div class="common-fields-section">
      <h3 class="section-title">{{ 'COMMON.GENERAL' | translate }}</h3>

      <!-- Label -->
      <mat-form-field appearance="fill" class="w-100">
        <mat-label>{{ 'EDIT_FIELD_PROPERTIES.LABEL' | translate }}</mat-label>
        <input matInput formControlName="label"
               [attr.aria-label]="'EDIT_FIELD_PROPERTIES.LABEL' | translate">
        <mat-error *ngIf="form.get('label')?.hasError('required')">
          {{ 'EDIT_FIELD_PROPERTIES.LABEL_REQUIRED' | translate }}
        </mat-error>
        <mat-error *ngIf="form.get('label')?.hasError('maxlength')">
          {{ 'EDIT_FIELD_PROPERTIES.LABEL_MAX_LENGTH' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- Public Field Checkbox -->
      <div class="checkbox-field">
        <mat-checkbox formControlName="isPublic"
                      [attr.aria-label]="'EDIT_FIELD_PROPERTIES.IS_PUBLIC' | translate">
          {{ 'EDIT_FIELD_PROPERTIES.IS_PUBLIC' | translate }}
        </mat-checkbox>
      </div>

      <!-- Required Field Checkbox -->
      <div class="checkbox-field">
        <mat-checkbox formControlName="isRequired"
                      [attr.aria-label]="'EDIT_FIELD_PROPERTIES.IS_REQUIRED' | translate">
          {{ 'EDIT_FIELD_PROPERTIES.IS_REQUIRED' | translate }}
        </mat-checkbox>
      </div>

      <!-- Unique Values Checkbox (only for supported field types) -->
      <div class="checkbox-field" *ngIf="supportsUnique()">
        <mat-checkbox formControlName="unique"
                      [attr.aria-label]="'EDIT_FIELD_PROPERTIES.UNIQUE' | translate">
          {{ 'EDIT_FIELD_PROPERTIES.UNIQUE' | translate }}
        </mat-checkbox>
      </div>

      <!-- Tooltip Section -->
      <div class="tooltip-section">
        <div class="checkbox-field">
          <mat-checkbox formControlName="showTooltip"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.SHOW_TOOLTIP' | translate">
            {{ 'EDIT_FIELD_PROPERTIES.SHOW_TOOLTIP' | translate }}
          </mat-checkbox>
        </div>

        <mat-form-field appearance="fill" class="w-100" *ngIf="showTooltipInput()">
          <mat-label>{{ 'EDIT_FIELD_PROPERTIES.TOOLTIP' | translate }}</mat-label>
          <input matInput formControlName="tooltip"
                 [attr.aria-label]="'EDIT_FIELD_PROPERTIES.TOOLTIP' | translate">
          <mat-error *ngIf="form.get('tooltip')?.hasError('maxlength')">
            {{ 'EDIT_FIELD_PROPERTIES.TOOLTIP_MAX_LENGTH' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Field-Specific Options Section -->
    <div class="field-specific-section" *ngIf="hasFieldSpecificOptions()">
      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ 'EDIT_FIELD_PROPERTIES.FIELD_SPECIFIC_OPTIONS' | translate }}
          </mat-panel-title>
        </mat-expansion-panel-header>

        <!-- Text/Phone/URL: Maximum Length -->
        <ng-container *ngIf="data.field.type === 'text' || data.field.type === 'phone' || data.field.type === 'url'">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.MAX_LENGTH' | translate }}</mat-label>
            <input matInput type="number" formControlName="maxLength"
                   [attr.aria-label]="'EDIT_FIELD_PROPERTIES.MAX_LENGTH' | translate">
            <mat-error *ngIf="form.get('maxLength')?.hasError('min') || form.get('maxLength')?.hasError('max')">
              {{ 'EDIT_FIELD_PROPERTIES.MAX_LENGTH_VALIDATION' | translate }}
            </mat-error>
          </mat-form-field>
        </ng-container>

        <!-- Number: Maximum Digits -->
        <ng-container *ngIf="data.field.type === 'number'">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.MAX_DIGITS' | translate }}</mat-label>
            <input matInput type="number" formControlName="maxDigits"
                   [attr.aria-label]="'EDIT_FIELD_PROPERTIES.MAX_DIGITS' | translate">
            <mat-error *ngIf="form.get('maxDigits')?.hasError('min') || form.get('maxDigits')?.hasError('max')">
              {{ 'EDIT_FIELD_PROPERTIES.MAX_DIGITS_VALIDATION' | translate }}
            </mat-error>
          </mat-form-field>
        </ng-container>

        <!-- Textarea: Type Selection -->
        <ng-container *ngIf="data.field.type === 'textarea'">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.TEXT_TYPE' | translate }}</mat-label>
            <mat-select formControlName="textType"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.TEXT_TYPE' | translate">
              <mat-option value="small">{{ 'EDIT_FIELD_PROPERTIES.TEXT_TYPE_SMALL' | translate }}</mat-option>
              <mat-option value="large">{{ 'EDIT_FIELD_PROPERTIES.TEXT_TYPE_LARGE' | translate }}</mat-option>
              <mat-option value="rich">{{ 'EDIT_FIELD_PROPERTIES.TEXT_TYPE_RICH' | translate }}</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Character limit info -->
          <div class="info-text" *ngIf="form.get('textType')?.value">
            <ng-container [ngSwitch]="form.get('textType')?.value">
              <span *ngSwitchCase="'small'">{{ 'EDIT_FIELD_PROPERTIES.MAX_LENGTH_2000' | translate }}</span>
              <span *ngSwitchCase="'large'">{{ 'EDIT_FIELD_PROPERTIES.MAX_LENGTH_32000' | translate }}</span>
              <span *ngSwitchCase="'rich'">{{ 'EDIT_FIELD_PROPERTIES.MAX_LENGTH_50000' | translate }}</span>
            </ng-container>
          </div>
        </ng-container>

        <!-- Picklist/MultiPicklist Options -->
        <ng-container *ngIf="isPicklistField()">
          <div class="picklist-section">
            <div class="picklist-header">
              <h4>{{ 'EDIT_FIELD_PROPERTIES.PICKLIST_OPTIONS' | translate }}</h4>
              <div class="picklist-actions">
                <button mat-icon-button type="button" (click)="addPicklistOption()"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.ADD_OPTION' | translate">
                  <mat-icon>add</mat-icon>
                </button>
                <button mat-icon-button type="button" [matMenuTriggerFor]="optionsMenu"
                        [attr.aria-label]="'COMMON.OPTIONS' | translate">
                  <mat-icon>settings</mat-icon>
                </button>
                <mat-menu #optionsMenu="matMenu">
                  <button mat-menu-item (click)="enterBulkMode()">
                    <mat-icon>playlist_add</mat-icon>
                    <span>{{ 'EDIT_FIELD_PROPERTIES.ADD_BULK' | translate }}</span>
                  </button>
                </mat-menu>
              </div>
            </div>

            <!-- Normal Mode: Individual Options -->
            <div class="picklist-options" *ngIf="!isBulkMode()"
                 cdkDropList (cdkDropListDropped)="onPicklistDrop($event)">
              <div class="picklist-option"
                   *ngFor="let option of picklistOptions(); let i = index; trackBy: trackByOption"
                   cdkDrag>
                <div class="option-controls">
                  <div class="drag-handle" cdkDragHandle>
                    <mat-icon>drag_indicator</mat-icon>
                  </div>
                  <mat-form-field appearance="fill" class="option-input">
                    <input matInput [value]="option"
                           (input)="updatePicklistOption(i, $any($event.target).value)"
                           placeholder="Option">
                  </mat-form-field>
                  <button mat-icon-button type="button" (click)="addPicklistOption(i)"
                          [attr.aria-label]="'EDIT_FIELD_PROPERTIES.ADD_OPTION' | translate">
                    <mat-icon>add</mat-icon>
                  </button>
                  <button mat-icon-button type="button" (click)="removePicklistOption(i)"
                          [attr.aria-label]="'COMMON.DELETE' | translate">
                    <mat-icon>remove</mat-icon>
                  </button>
                </div>
              </div>
            </div>

            <!-- Bulk Mode: Textarea -->
            <div class="bulk-mode" *ngIf="isBulkMode()">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>{{ 'EDIT_FIELD_PROPERTIES.BULK_PLACEHOLDER' | translate }}</mat-label>
                <textarea matInput rows="10"
                          [value]="bulkText()"
                          (input)="bulkText.set($any($event.target).value)"></textarea>
              </mat-form-field>
              <div class="bulk-actions">
                <button mat-raised-button color="primary" type="button" (click)="saveBulkOptions()">
                  {{ 'EDIT_FIELD_PROPERTIES.BULK_SAVE' | translate }}
                </button>
                <button mat-button type="button" (click)="exitBulkMode()">
                  {{ 'EDIT_FIELD_PROPERTIES.BULK_CANCEL' | translate }}
                </button>
              </div>
            </div>

            <!-- Default Value -->
            <mat-form-field appearance="fill" class="w-100">
              <mat-label>{{ 'EDIT_FIELD_PROPERTIES.DEFAULT_VALUE' | translate }}</mat-label>
              <mat-select formControlName="defaultValue"
                          [multiple]="data.field.type === 'multi-picklist'"
                          [attr.aria-label]="'EDIT_FIELD_PROPERTIES.DEFAULT_VALUE' | translate">
                <mat-option *ngFor="let option of picklistOptions()" [value]="option">
                  {{ option }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <!-- Sort Order -->
            <mat-form-field appearance="fill" class="w-100">
              <mat-label>{{ 'EDIT_FIELD_PROPERTIES.SORT_ORDER' | translate }}</mat-label>
              <mat-select formControlName="sortOrder"
                          [attr.aria-label]="'EDIT_FIELD_PROPERTIES.SORT_ORDER' | translate">
                <mat-option value="input">{{ 'EDIT_FIELD_PROPERTIES.SORT_ORDER_INPUT' | translate }}</mat-option>
                <mat-option value="alphabetical">{{ 'EDIT_FIELD_PROPERTIES.SORT_ORDER_ALPHABETICAL' | translate }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </ng-container>

        <!-- Search: Module Selection -->
        <ng-container *ngIf="data.field.type === 'search'">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.SEARCH_MODULE' | translate }}</mat-label>
            <mat-select formControlName="searchModule"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.SEARCH_MODULE' | translate">
              <ng-container *ngIf="data.availableModules && data.availableModules.length > 0; else defaultModules">
                <mat-option *ngFor="let module of data.availableModules" [value]="module._id">
                  {{ module.name }}
                </mat-option>
              </ng-container>
              <ng-template #defaultModules>
                <mat-option value="sales_quotes">{{ 'EDIT_FIELD_PROPERTIES.SEARCH_MODULE_SALES_QUOTES' | translate }}</mat-option>
                <mat-option value="contacts">{{ 'EDIT_FIELD_PROPERTIES.SEARCH_MODULE_CONTACTS' | translate }}</mat-option>
                <mat-option value="transactions">{{ 'EDIT_FIELD_PROPERTIES.SEARCH_MODULE_TRANSACTIONS' | translate }}</mat-option>
              </ng-template>
            </mat-select>
          </mat-form-field>
        </ng-container>

        <!-- User: Type Selection -->
        <ng-container *ngIf="data.field.type === 'user'">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.USER_TYPE' | translate }}</mat-label>
            <mat-select formControlName="userType"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.USER_TYPE' | translate">
              <mat-option value="single">{{ 'EDIT_FIELD_PROPERTIES.USER_TYPE_SINGLE' | translate }}</mat-option>
              <mat-option value="multiple">{{ 'EDIT_FIELD_PROPERTIES.USER_TYPE_MULTIPLE' | translate }}</mat-option>
            </mat-select>
          </mat-form-field>
        </ng-container>

        <!-- Upload File: Multiple Files -->
        <ng-container *ngIf="data.field.type === 'upload-file'">
          <div class="checkbox-field">
            <mat-checkbox formControlName="allowMultipleFiles"
                          [attr.aria-label]="'EDIT_FIELD_PROPERTIES.ALLOW_MULTIPLE_FILES' | translate">
              {{ 'EDIT_FIELD_PROPERTIES.ALLOW_MULTIPLE_FILES' | translate }}
            </mat-checkbox>
          </div>

          <mat-form-field appearance="fill" class="w-100" *ngIf="form.get('allowMultipleFiles')?.value">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.MAX_FILES' | translate }}</mat-label>
            <input matInput type="number" formControlName="maxFiles"
                   [attr.aria-label]="'EDIT_FIELD_PROPERTIES.MAX_FILES' | translate">
            <mat-error *ngIf="form.get('maxFiles')?.hasError('min') || form.get('maxFiles')?.hasError('max')">
              {{ 'EDIT_FIELD_PROPERTIES.MAX_FILES_VALIDATION' | translate }}
            </mat-error>
          </mat-form-field>
        </ng-container>

        <!-- Upload Image: Maximum Images -->
        <ng-container *ngIf="data.field.type === 'upload-image'">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.MAX_IMAGES' | translate }}</mat-label>
            <mat-select formControlName="maxImages"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.MAX_IMAGES' | translate">
              <mat-option *ngFor="let num of [1,2,3,4,5,6,7,8,9,10]" [value]="num">
                {{ num }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </ng-container>

        <!-- Currency: Digits, Decimal Places, Rounding -->
        <ng-container *ngIf="data.field.type === 'currency'">
          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>{{ 'EDIT_FIELD_PROPERTIES.MAX_DIGITS' | translate }}</mat-label>
                <input matInput type="number" formControlName="maxDigits"
                       [attr.aria-label]="'EDIT_FIELD_PROPERTIES.MAX_DIGITS' | translate">
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>{{ 'EDIT_FIELD_PROPERTIES.DECIMAL_PLACES' | translate }}</mat-label>
                <input matInput type="number" formControlName="decimalPlaces"
                       [attr.aria-label]="'EDIT_FIELD_PROPERTIES.DECIMAL_PLACES' | translate">
                <mat-error *ngIf="form.get('decimalPlaces')?.hasError('min') || form.get('decimalPlaces')?.hasError('max')">
                  {{ 'EDIT_FIELD_PROPERTIES.DECIMAL_PLACES_VALIDATION_CURRENCY' | translate }}
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <mat-form-field appearance="fill" class="w-100">
            <mat-label>{{ 'EDIT_FIELD_PROPERTIES.ROUNDING' | translate }}</mat-label>
            <mat-select formControlName="rounding"
                        [attr.aria-label]="'EDIT_FIELD_PROPERTIES.ROUNDING' | translate">
              <mat-option value="normal">{{ 'EDIT_FIELD_PROPERTIES.ROUNDING_NORMAL' | translate }}</mat-option>
              <mat-option value="off">{{ 'EDIT_FIELD_PROPERTIES.ROUNDING_OFF' | translate }}</mat-option>
              <mat-option value="up">{{ 'EDIT_FIELD_PROPERTIES.ROUNDING_UP' | translate }}</mat-option>
              <mat-option value="down">{{ 'EDIT_FIELD_PROPERTIES.ROUNDING_DOWN' | translate }}</mat-option>
            </mat-select>
          </mat-form-field>
        </ng-container>

        <!-- Decimal: Digits, Decimal Places, Number Separator -->
        <ng-container *ngIf="data.field.type === 'decimal'">
          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>{{ 'EDIT_FIELD_PROPERTIES.MAX_DIGITS' | translate }}</mat-label>
                <input matInput type="number" formControlName="maxDigits"
                       [attr.aria-label]="'EDIT_FIELD_PROPERTIES.MAX_DIGITS' | translate">
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>{{ 'EDIT_FIELD_PROPERTIES.DECIMAL_PLACES' | translate }}</mat-label>
                <input matInput type="number" formControlName="decimalPlaces"
                       [attr.aria-label]="'EDIT_FIELD_PROPERTIES.DECIMAL_PLACES' | translate">
                <mat-error *ngIf="form.get('decimalPlaces')?.hasError('min') || form.get('decimalPlaces')?.hasError('max')">
                  {{ 'EDIT_FIELD_PROPERTIES.DECIMAL_PLACES_VALIDATION_DECIMAL' | translate }}
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <div class="checkbox-field">
            <mat-checkbox formControlName="useNumberSeparator"
                          [attr.aria-label]="'EDIT_FIELD_PROPERTIES.USE_NUMBER_SEPARATOR' | translate">
              {{ 'EDIT_FIELD_PROPERTIES.USE_NUMBER_SEPARATOR' | translate }}
            </mat-checkbox>
          </div>
        </ng-container>

        <!-- Checkbox: Enable by Default -->
        <ng-container *ngIf="data.field.type === 'checkbox'">
          <div class="checkbox-field">
            <mat-checkbox formControlName="enableByDefault"
                          [attr.aria-label]="'EDIT_FIELD_PROPERTIES.ENABLE_BY_DEFAULT' | translate">
              {{ 'EDIT_FIELD_PROPERTIES.ENABLE_BY_DEFAULT' | translate }}
            </mat-checkbox>
          </div>
        </ng-container>

      </mat-expansion-panel>
    </div>

  </form>
</mat-dialog-content>

<!-- Footer -->
<mat-dialog-actions align="end" class="field-properties-actions">
  <button mat-button type="button" (click)="onCancel()"
          [attr.aria-label]="'COMMON.CANCEL' | translate">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" type="button" (click)="onSave()"
          [disabled]="form.invalid"
          [attr.aria-label]="'COMMON.SAVE' | translate">
    {{ 'COMMON.SAVE' | translate }}
  </button>
</mat-dialog-actions>
