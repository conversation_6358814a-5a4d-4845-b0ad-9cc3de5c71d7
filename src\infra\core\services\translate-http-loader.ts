import { HttpClient, HttpContext } from '@angular/common/http';
import { TranslateLoader, TranslateService } from '@ngx-translate/core';
import { Observable, Subject, forkJoin, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { NAVIGATION_BLOCK } from 'salehub_shared_contracts';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { mergeDeep } from '@/shared/utils';
import { Location } from '@angular/common';
import { Injectable, Injector, OnDestroy } from '@angular/core';
import { RouterEventService } from './router_events.service';

/**
 * Custom TranslateHttpLoader
 * Tải file i18n từ shared, first path và full path của URL
 */
export class TranslateHttpLoader implements TranslateLoader {
  private destroy$ = new Subject<void>();

  constructor(
    private http: HttpClient,
    private location: Location,
    private routerEventService: RouterEventService,
    private injector: Injector,
    private prefix: string = '/assets/i18n/',
    private suffix: string = '.json'
  ) {
    // this.routerEventService.getNavigationEnd().subscribe(() => {
    //   const translateService = this.injector.get(TranslateService);
    //   translateService.reloadLang(translateService.currentLang || 'vi');
    // });
  }



  /**
   * Lấy bản dịch cho ngôn ngữ dựa trên first path và full path của URL
   * @param lang Mã ngôn ngữ (ví dụ: 'en', 'vi')
   */
  public getTranslation(lang: string): Observable<Object> {
    return this.http.get(`${this.prefix}${lang}${this.suffix}`);

    // Tải file i18n chung (shared)
    const sharedFile = `${this.prefix}shared/${lang}${this.suffix}`;
    const sharedRequest = this.http.get<Record<string, any>>(sharedFile, {
      context: new HttpContext().set(HTTP_REQUEST_OPTIONS, { hideError: true })
    }).pipe(
      map((translations) => translations || {}),
      catchError(() => {
        console.error(`Shared translation file not found: ${sharedFile}`);
        return of({});
      })
    );

    // Lấy first path và full path từ URL
    const { firstPath, fullPath } = this.getPaths();

    // Nếu không có firstPath hợp lệ hoặc không thuộc NAVIGATION_BLOCK, chỉ trả về bản dịch shared
    if (!firstPath || !NAVIGATION_BLOCK.includes(firstPath as any)) {
      return sharedRequest;
    }

    // Tải file first path
    const firstPathFile = `${this.prefix}${firstPath}/${lang}${this.suffix}`;
    const firstPathRequest = this.http.get<Record<string, any>>(firstPathFile, {
      context: new HttpContext().set(HTTP_REQUEST_OPTIONS, { hideError: true })
    }).pipe(
      map((translations) => translations || {}),
      catchError(() => {
        return of({});
      })
    );

    // Tải file full path (nếu có)
    const requests: Observable<Record<string, any>>[] = [sharedRequest, firstPathRequest];
    if (fullPath) {
      const fullPathFile = `${this.prefix}${fullPath}/${lang}${this.suffix}`;
      const fullPathRequest = this.http.get<Record<string, any>>(fullPathFile, {
        context: new HttpContext().set(HTTP_REQUEST_OPTIONS, { hideError: true })
      }).pipe(
        map((translations) => translations || {}),
        catchError(() => {
          return of({});
        })
      );
      requests.push(fullPathRequest);
    }

    // Hợp nhất tất cả bản dịch
    return forkJoin(requests).pipe(
      map((translations) => (
        mergeDeep(
          mergeDeep(translations[0], translations[1]),
          translations[2]
        ) as Record<string, any>
      )
    ));
  }

  /**
   * Lấy first path và full path từ URL hiện tại
   * @returns { firstPath: string | undefined, fullPath: string | undefined }
   */
  private getPaths(): { firstPath: string | undefined; fullPath: string | undefined } {
    const locationPath = this.location.path();
    if (!locationPath) {
      console.warn('Router URL is not available, falling back to shared translations');
      return { firstPath: undefined, fullPath: undefined };
    }

    const url = locationPath.split('?')[0]; // Loại bỏ query params

    const segments = url.split('/').filter(segment => segment); // Loại bỏ segment rỗng
    const firstPath = segments[0]; // Lấy first path (ví dụ: 'cashier')
    const fullPath = segments.length > 1 ? segments.join('/') : undefined; // Lấy full path (ví dụ: 'cashier/order')

    return { firstPath, fullPath };
  }
}

/**
 * Factory function để tạo TranslateHttpLoader
 * @param http HttpClient
 * @param router Router
 */
export function createTranslateLoader(
  http: HttpClient,
  location: Location,
  routerEvent: RouterEventService,
  injector: Injector
): TranslateLoader {
  return new TranslateHttpLoader(http, location, routerEvent, injector, '/assets/i18n/');
}