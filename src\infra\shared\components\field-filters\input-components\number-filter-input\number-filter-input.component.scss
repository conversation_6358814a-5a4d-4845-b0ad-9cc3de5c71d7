.number-filter-input {
  width: 100%;

  // Single value input styles
  .single-value-input {
    width: 100%;
  }

  // Range input styles
  .range-input {
    width: 100%;
    
    .range-error {
      margin-top: 4px;
      
      small {
        font-size: 0.75rem;
        color: #dc3545;
      }
    }
  }

  // Material form field customization
  ::ng-deep .mat-mdc-form-field {
    width: 100%;
    
    .mat-mdc-form-field-flex {
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      min-height: 40px;
      padding: 8px 0;
    }

    // Number input specific styles
    .number-input {
      font-size: 0.875rem;
      line-height: 1.4;
      text-align: right; // Right align cho numbers
      
      &:focus {
        outline: none;
      }
      
      // Remove spinner arrows trong Chrome/Safari
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      
      // Remove spinner arrows trong Firefox
      &[type=number] {
        -moz-appearance: textfield;
      }
    }

    // Suffix styles (currency/percent symbols)
    .mat-mdc-form-field-text-suffix {
      font-size: 0.875rem;
      color: #6c757d;
      font-weight: 500;
      margin-left: 4px;
    }

    // Label styles
    .mat-mdc-form-field-label {
      font-size: 0.875rem;
      color: #6c757d;
    }

    // Error styles
    .mat-mdc-form-field-error {
      font-size: 0.75rem;
      color: #dc3545;
      margin-top: 4px;
    }

    // Disabled state
    &.mat-form-field-disabled {
      .mat-mdc-form-field-flex {
        background-color: #f8f9fa;
      }
      
      .number-input {
        color: #6c757d;
        cursor: not-allowed;
      }
    }

    // Focus state
    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        border-color: #007bff;
      }
    }

    // Invalid state
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline-thick {
        border-color: #dc3545;
      }
    }
  }
}

.no-input-message {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  text-align: center;
  
  small {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }
}

// Field type specific styles
.number-filter-input {
  // Currency field styling
  &.currency-field {
    ::ng-deep .mat-mdc-form-field-text-suffix {
      color: #28a745;
      font-weight: 600;
    }
  }

  // Percent field styling
  &.percent-field {
    ::ng-deep .mat-mdc-form-field-text-suffix {
      color: #17a2b8;
      font-weight: 600;
    }
  }

  // Decimal field styling
  &.decimal-field {
    ::ng-deep .number-input {
      letter-spacing: 0.5px;
    }
  }
}

// Responsive design
@media (max-width: 576px) {
  .number-filter-input {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-infix {
        min-height: 36px;
        padding: 6px 0;
      }
      
      .number-input {
        font-size: 0.8rem;
      }
      
      .mat-mdc-form-field-label {
        font-size: 0.8rem;
      }
      
      .mat-mdc-form-field-text-suffix {
        font-size: 0.8rem;
      }
    }
    
    .range-input {
      .range-error {
        margin-top: 2px;
        
        small {
          font-size: 0.7rem;
        }
      }
    }
  }
  
  .no-input-message {
    padding: 8px 12px;
    
    small {
      font-size: 0.7rem;
    }
  }
}
