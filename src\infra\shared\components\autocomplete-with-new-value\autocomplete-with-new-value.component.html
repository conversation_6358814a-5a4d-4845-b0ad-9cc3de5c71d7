<mat-form-field class="w-100" appearance="outline">
  <mat-label>{{ label }}</mat-label>
  <input type="text"
         matInput
         [placeholder]="placeholder"
         [(ngModel)]="inputValue"
         (ngModelChange)="filter()"
         (blur)="onBlur()"
         [matAutocomplete]="auto"
         [required]="required">
  <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onOptionSelected($event)">
    <mat-option *ngFor="let option of filteredOptions()" [value]="option">
      {{ option.value }}
    </mat-option>
  </mat-autocomplete>
  <mat-error *ngIf="errorMsg">{{ errorMsg }}</mat-error>
</mat-form-field>
