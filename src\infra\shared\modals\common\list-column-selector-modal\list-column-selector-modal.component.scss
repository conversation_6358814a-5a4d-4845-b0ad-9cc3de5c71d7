.column-selector-container {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.left-panel, .right-panel {
  flex: 1;
  padding: 16px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.left-panel {
  .mat-mdc-list-item-unscoped-content,
  .mat-mdc-checkbox,
  .mdc-form-field,
  .mdc-label {
    width: 100%;
  }

  .mat-mdc-checkbox,
  .mat-mdc-list-item-unscoped-content {
    display: block;
  }

  .mat-internal-form-field {
    display: flex; 
  }
  .mat-mdc-checkbox label {
    display: inline-flex;
  }

}

.mat-mdc-card {
  box-shadow: none;
}

.left-panel h3, .right-panel h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
}

.section {
  margin-bottom: 16px;
}

.section h4 {
  margin: 0 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.column-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mdc-list-item.column-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #F5F5F5;
  border-radius: 7px;
  margin-bottom: 5px;
}

.mat-mdc-checkbox .mat-internal-form-field,
.mdc-list-item__primary-text {
  font-size: 1em !important;
}

.column-item.pinned {
  background: #e3f2fd;
}

.column-item:hover {
  background: #f0f0f0;
}

.handle {
  cursor: grab;
  font-size: 16px;
  margin-right: 8px;
  color: #666;
}

.pin-button {
  margin-left: auto;
}

.pin-button mat-icon {
  color: #666;
}

.pin-button mat-icon.pinned {
  color: #1976d2;
}

.cdk-drag-preview {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translate3d(0, 0, 0);
  transition: transform 0.1s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cdk-drag-preview.pinned {
  background: #e3f2fd;
}

.cdk-drag-preview .pin-button {
  display: none;
}

.cdk-drag-animating {
  transition: transform 0.1s cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: #e0e0e0;
  border: 1px dashed #666;
}

@media (max-width: 768px) {
  .column-selector-container {
    flex-direction: column;
  }

  .left-panel, .right-panel {
    flex: none;
    width: 100%;
    max-height: 300px;
  }
}

.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
  width: 8px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover {
  background: #666;
}