import { ChangeDetectionStrategy, Component, Inject, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface SimpleNoteModalData {
  title: string;
  note: string;
}

/**
 * Kiểu dữ liệu trả về từ modal
 */
export type SimpleNoteModalResult = string | undefined;

/**
 * Component modal đơn giản để nhập ghi chú
 */
@Component({
  selector: 'app-simple-note-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule
  ],
  templateUrl: './simple-note-modal.component.html',
  styleUrls: ['./simple-note-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SimpleNoteModalComponent {
  /**
   * Nội dung ghi chú
   */
  note: string;

  /**
   * Dữ liệu đầu vào cho modal
   */
  data: SimpleNoteModalData;

  constructor(
    @Optional() private dialogRef?: MatDialogRef<SimpleNoteModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SimpleNoteModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: SimpleNoteModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: SimpleNoteModalData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      title: 'COMMON.NOTE',
      note: ''
    };

    this.note = this.data.note || '';
  }

  /**
   * Xử lý khi nhấn nút Hủy
   */
  onCancel(): void {
    this.close(undefined);
  }

  /**
   * Xử lý khi nhấn nút Xác nhận
   */
  onConfirm(): void {
    this.close(this.note);
  }

  /**
   * Đóng modal
   */
  private close(result: string | undefined): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
