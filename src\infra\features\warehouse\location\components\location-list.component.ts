import { Component, OnInit, ChangeDetectionStrategy, ViewChild, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { TranslateModule } from '@ngx-translate/core';
import { LocationTreeComponent } from './location-tree/location-tree.component';
import { LocationFormModalComponent, LocationFormModalData } from './location-form-modal/location-form-modal.component';
import { LocationService } from '../services/location.service';
import { LocationNode } from '../models/view/location-view.model';
import { Location } from '../models/api/location.dto';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';

@Component({
  selector: 'app-location-list',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    TranslateModule,
    LocationTreeComponent
  ],
  templateUrl: './location-list.component.html',
  styleUrls: ['./location-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationListComponent implements OnInit {
  @ViewChild(LocationTreeComponent) locationTree!: LocationTreeComponent;

  selectedLocation = signal<LocationNode | null>(null);

  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    private locationService: LocationService,
    private responsiveModalService: ResponsiveModalService
  ) { /* Constructor implements dependency injection */ }

  ngOnInit(): void {
    // Initialization code if needed
  }

  onSelectLocation(location: LocationNode): void {
    this.selectedLocation.set(location);
  }

  onAddLocation(): void {
    const queryParams = this.selectedLocation()
      ? {
        parentId: this.selectedLocation()?.key,
        level: (this.selectedLocation()?.data?.level || 0) + 1
      }
      : {};

    this.router.navigateByUrl('/warehouse/location/create');
  }

  onEditLocation(): void {
    if (!this.selectedLocation()) {
      this.snackBar.open('Please select a location to edit', 'Close', { duration: 3000 });
      return;
    }

    const locationId = this.selectedLocation()?.key;
    if (locationId) {
      this.router.navigateByUrl(`/warehouse/location/edit/${locationId}`);
    }
  }

  onDeleteLocation(): void {
    if (!this.selectedLocation()) {
      this.snackBar.open('Please select a location to delete', 'Close', { duration: 3000 });
      return;
    }

    const locationId = this.selectedLocation()?.key;
    const locationName = this.selectedLocation()?.data.name;

    if (locationId && locationName) {
      // Using MatDialog for confirmation instead of window.confirm()
      this.openDeleteConfirmationDialog(locationId, locationName);
    }
  }

  private openDeleteConfirmationDialog(locationId: string, locationName: string): void {
    // Normally we would use a proper confirmation dialog component
    // But for now we'll use a simple confirmation via snackbar
    const snackBarRef = this.snackBar.open(
      `Are you sure you want to delete ${locationName}?`,
      'Delete',
      { duration: 10000 }
    );

    snackBarRef.onAction().subscribe(() => {
      this.locationService.deleteLocation(locationId).subscribe({
        next: () => {
          this.snackBar.open('Location deleted successfully', 'Close', { duration: 3000 });
          this.selectedLocation.set(null);
          this.locationTree.refreshTree();
        },
        error: (error) => {
          this.snackBar.open(
            error.message || 'Error deleting location. It may have children.',
            'Close',
            { duration: 5000 }
          );
        }
      });
    });
  }

  async openLocationFormModal(data: LocationFormModalData): Promise<void> {
    try {
      const result = await this.responsiveModalService.open<
        LocationFormModalComponent,
        LocationFormModalData,
        Location | null
      >(LocationFormModalComponent, {
        data,
        width: '900px',
        maxWidth: '95vw',
        maxHeight: '90vh'
      });

      if (result) {
        this.snackBar.open(
          `Location ${data.editMode ? 'updated' : 'created'} successfully`,
          'Close',
          { duration: 3000 }
        );
        this.locationTree.refreshTree();
      }
    } catch (error) {
      console.error('Error opening location form modal:', error);
    }
  }
}
