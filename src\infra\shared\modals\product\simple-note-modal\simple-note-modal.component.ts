import { Component, EventEmitter, Inject, Optional, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleNoteModalData } from './models/simple-note-modal.model';

/**
 * SimpleNoteModalComponent - Modal đơn giản để chỉnh sửa ghi chú
 * 
 * Component này cho phép người dùng xem và chỉnh sửa một ghi chú đơn giản.
 * Nó nhận vào một ghi chú hiện tại và trả về ghi chú đã được cập nhật.
 */
@Component({
  selector: 'app-simple-note-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule
  ],
  templateUrl: './simple-note-modal.component.html',
  styleUrls: ['./simple-note-modal.component.scss']
})
export class SimpleNoteModalComponent {
  // Output để emit ghi chú đã cập nhật
  @Output() noteUpdated = new EventEmitter<string>();

  // Nội dung ghi chú
  noteContent: string = '';
  
  // Dữ liệu từ inject
  data: SimpleNoteModalData;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: SimpleNoteModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: SimpleNoteModalData,
    @Optional() private dialogRef?: MatDialogRef<SimpleNoteModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SimpleNoteModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || '';
    
    // Khởi tạo nội dung từ dữ liệu đầu vào
    this.noteContent = this.data;
  }

  /**
   * Xử lý khi người dùng nhấn nút Hủy
   * Đóng modal mà không emit giá trị
   */
  onCancel(): void {
    this.close(undefined);
  }

  /**
   * Xử lý khi người dùng nhấn nút Lưu
   * Đóng modal và emit giá trị ghi chú mới
   */
  onSave(): void {
    this.close(this.noteContent);
  }
  
  /**
   * Đóng modal
   */
  private close(result: string | undefined): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
