<div class="new-section-container">
  <!-- Nút tạo section mới -->
  <button 
    mat-raised-button 
    color="primary"
    class="new-section-btn"
    (click)="onCreateSection()"
    [disabled]="isCreating()"
    type="button">
    
    <!-- Icon -->
    <mat-icon class="btn-icon">add</mat-icon>
    
    <!-- Text -->
    <span class="btn-text">
      {{ 'DYNAMIC_LAYOUT_BUILDER.NEW_FIELDS.NEW_SECTION' | translate }}
    </span>
    
    <!-- Loading indicator -->
    <mat-icon 
      *ngIf="isCreating()" 
      class="loading-icon">
      refresh
    </mat-icon>
  </button>
  
  <!-- <PERSON><PERSON> tả -->
  <p class="new-section-description">
    {{ 'DYNAMIC_LAYOUT_BUILDER.NEW_SECTION.DESCRIPTION' | translate }}
  </p>
</div>
