import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

import { Province, District, Ward, Place } from 'salehub_shared_contracts';
import { provinceList as mockProvinceList, districtList as mockDistrictList, wardList as mockWardList } from '@/mock/shared/vn_adminstrative_divisions.mock';
import { normalizeVietnameseStr } from '@shared/utils';

@Component({
  selector: 'app-address-manual-selector',
  templateUrl: './address-manual-selector.component.html',
  styleUrls: ['./address-manual-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatInputModule,
    ScrollingModule,
    TranslateModule,
    MatIconModule,
    MatButtonModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AddressManualSelectorComponent implements OnInit {
  @Input() defaultValue?: Place;
  @Output() selectedPlace = new EventEmitter<Place>();
  @Output() changingPlace = new EventEmitter<boolean>();

  // Signals cho dữ liệu
  provinceList = signal<Province[]>([]);
  districtList = signal<District[]>([]);
  wardList = signal<Ward[]>([]);

  // Form controls
  addressForm = new FormGroup({
    streetAddress: new FormControl('', [Validators.required]),
    province: new FormControl<Province | null>(null, [Validators.required]),
    district: new FormControl<District | null>({ value: null, disabled: true }, [Validators.required]),
    ward: new FormControl<Ward | null>({ value: null, disabled: true }, [Validators.required])
  });

  // Filtered lists cho autocomplete
  filteredProvinces = signal<Province[]>([]);
  filteredDistricts = signal<District[]>([]);
  filteredWards = signal<Ward[]>([]);

  timeoutEmitSelectedPlace!: any;

  constructor() {
    // Load dữ liệu từ mock
    this.loadAdministrativeDivisions();

    // Subscribe to form changes để emit changingPlace
    this.addressForm.valueChanges.subscribe(() => {
      this.changingPlace.emit(true);
    });

    // Subscribe to province changes
    this.addressForm.get('province')?.valueChanges.subscribe(value => {
      if (value) {
        // Enable district control nếu chưa enabled
        const districtControl = this.addressForm.get('district');
        if (districtControl?.disabled) {
          districtControl.enable();
        }

        // Load districts mới
        const districtsOfProvince = mockDistrictList[value.code] || [];
        this.districtList.set(districtsOfProvince);
        this.filteredDistricts.set(districtsOfProvince);

        // Clear district và ward value
        this.addressForm.patchValue({
          district: null,
          ward: null
        }, { emitEvent: false });

        // Disable ward cho đến khi chọn district mới
        this.addressForm.get('ward')?.disable();
      }
      this.emitSelectedPlaceIfValid();
    });

    // Subscribe to district changes
    this.addressForm.get('district')?.valueChanges.subscribe(value => {
      if (value) {
        // Enable ward control nếu chưa enabled
        const wardControl = this.addressForm.get('ward');
        if (wardControl?.disabled) {
          wardControl.enable();
        }

        // Load wards mới
        const wardsOfDistrict = mockWardList[value.code] || [];
        this.wardList.set(wardsOfDistrict);
        this.filteredWards.set(wardsOfDistrict);

        // Clear ward value
        this.addressForm.patchValue({
          ward: null
        }, { emitEvent: false });
      }
      this.emitSelectedPlaceIfValid();
    });

    // Subscribe to ward changes
    this.addressForm.get('ward')?.valueChanges.subscribe(() => {
      this.emitSelectedPlaceIfValid();
    });

    // Subscribe to streetAddress changes
    this.addressForm.get('streetAddress')?.valueChanges.subscribe(() => {
      this.emitSelectedPlaceIfValid();
    });
  }

  ngOnInit() {
    this.loadAdministrativeDivisions();

    // Set default values nếu có
    if (this.defaultValue) {
      const { streetAddress, province, district, ward } = this.defaultValue;

      if (streetAddress) {
        this.addressForm.get('streetAddress')?.setValue(streetAddress);
      }

      if (province) {
        // Set province và load districts
        this.addressForm.get('province')?.setValue(province);
        const districtsOfProvince = mockDistrictList[province.code] || [];
        this.districtList.set(districtsOfProvince);
        this.filteredDistricts.set(districtsOfProvince);

        // Enable district control
        this.addressForm.get('district')?.enable();

        if (district) {
          // Set district và load wards
          this.addressForm.get('district')?.setValue(district);
          const wardsOfDistrict = mockWardList[district.code] || [];
          this.wardList.set(wardsOfDistrict);
          this.filteredWards.set(wardsOfDistrict);

          // Enable ward control
          this.addressForm.get('ward')?.enable();

          if (ward) {
            this.addressForm.get('ward')?.setValue(ward);
          }
        }
      }
    }
  }

  /**
   * Emit selectedPlace nếu form hợp lệ và đầy đủ
   */
  private emitSelectedPlaceIfValid() {
    if(this.timeoutEmitSelectedPlace) {
      clearTimeout(this.timeoutEmitSelectedPlace);
    }
    this.timeoutEmitSelectedPlace = setTimeout(() => {
      if (this.addressForm.valid) {
        const formValue = this.addressForm.value;
        const place: Place = {
          streetAddress: formValue.streetAddress || '',
          province: formValue.province || undefined,
          district: formValue.district || undefined,
          ward: formValue.ward || undefined
        };
        this.selectedPlace.emit(place);
      }
    });
  }

  /**
   * Load dữ liệu đơn vị hành chính
   */
  private loadAdministrativeDivisions(): void {
    this.provinceList.set(mockProvinceList);
    this.filteredProvinces.set(mockProvinceList);
  }

  /**
   * Filter provinces theo input
   */
  filterProvinces(value: string): void {
    const searchValue = this.normalizeText(value);
    this.filteredProvinces.set(
      this.provinceList().filter(province =>
        province.asciiName.includes(searchValue)
      )
    );
  }

  /**
   * Filter districts theo input
   */
  filterDistricts(value: string): void {
    const searchValue = this.normalizeText(value);
    this.filteredDistricts.set(
      this.districtList().filter(district =>
        district.asciiName.includes(searchValue)
      )
    );
  }

  /**
   * Filter wards theo input
   */
  filterWards(value: string): void {
    const searchValue = this.normalizeText(value);
    this.filteredWards.set(
      this.wardList().filter(ward =>
        ward.asciiName.includes(searchValue)
      )
    );
  }

  /**
   * Chuẩn hóa text để tìm kiếm
   */
  private normalizeText(text: string): string {
    return normalizeVietnameseStr(text.toLowerCase().trim()) as string;
  }

  /**
   * Display function cho autocomplete
   */
  displayFn(item: Province | District | Ward | null): string {
    return item?.name || '';
  }
}
