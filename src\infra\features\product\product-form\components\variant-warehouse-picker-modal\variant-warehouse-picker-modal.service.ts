import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import {
  VariantWarehousePickerModalComponent,
  VariantWarehousePickerModalData,
  VariantWarehousePickerModalResult
} from './variant-warehouse-picker-modal.component';
import { Warehouse, WarehouseLocation } from '@mock/product_form';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';

/**
 * Service để mở modal chọn kho hàng cho sản phẩm biến thể
 */
@Injectable({
  providedIn: 'root'
})
export class VariantWarehousePickerModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal chọn kho hàng
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<VariantWarehousePickerModalResult> Kết quả từ modal
   */
  async open(data: VariantWarehousePickerModalData): Promise<VariantWarehousePickerModalResult | undefined> {
    try {
      const modalConfig = {
        data,
        width: '800px',
        maxWidth: '95vw'
      };

      const result = await this.responsiveModalService.open<
        VariantWarehousePickerModalComponent,
        VariantWarehousePickerModalData,
        VariantWarehousePickerModalResult
      >(VariantWarehousePickerModalComponent, modalConfig);

      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal chọn kho hàng:', error);
      return undefined;
    }
  }
}
