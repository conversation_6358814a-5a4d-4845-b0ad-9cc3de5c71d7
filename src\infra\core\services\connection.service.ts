import { Injectable } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { PageMessageService } from './page_message.service';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class ConnectionCheckService {
  private _isOnline = window.navigator.onLine;

  constructor(pageMessage: PageMessageService) {
    if(!this._isOnline) {
      pageMessage.error('Không có kết nối mạng.');
    }

    window.addEventListener('offline', (event) => {
      // console.log('offline');

      if(this._isOnline) {
        pageMessage.error('Đã mất kết nối mạng.');
      }

      this._isOnline = false;
    });

    window.addEventListener('online', (event) => {
      // console.log('online');

      if(!this._isOnline) {
        pageMessage.success('Đã có kết nối mạng trở lại.');
      }

      this._isOnline = true;
    });
  }

  isOnline() {
    return this._isOnline;
  }
}
export type RequestOptions = {
  useLoader?: boolean,
  body?: any;
  method?: string;
  headers?: HttpHeaders | {
    [header: string]: string | string[];
  };
};
