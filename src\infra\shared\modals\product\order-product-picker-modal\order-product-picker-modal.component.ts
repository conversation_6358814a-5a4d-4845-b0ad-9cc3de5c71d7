import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { OrderProductPickerComponent } from '../../../components/product-selection/order-product-picker/order-product-picker.component';
import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { OrderProductPickerModalData } from './models/order-product-picker-modal.model';

/**
 * Component OrderProductPickerModal
 * Modal cho phép người dùng chọn sản phẩm để thêm vào đơn hàng
 */
@Component({
  selector: 'app-order-product-picker-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    TranslateModule,
    OrderProductPickerComponent
  ],
  template: `
    <mat-dialog-content>
      <app-product-selection
        [list]="data.list"
        [data]="data.data || []"
        [searchTerm]="data.searchTerm || ''"
        [settings]="data.settings || { allowSellWhenOutOfStock: true }"
        [selectedWarehouseId]="data.selectedWarehouseId ?? null"
        [warehouseList]="data.warehouseList || []"
        [categoryList]="data.categoryList || []"
        [brandList]="data.brandList || []"
        (addSelectedItems)="onAddItems($event)"
        (resetSelectionEvent)="onResetSelection()">
      </app-product-selection>
    </mat-dialog-content>
  `,
  styles: `
    mat-dialog-content {
      flex: 1;
      overflow: auto;
      padding: 0;
      margin: 0;
      max-height: none;
    }
  `
})
export class OrderProductPickerModalComponent implements OnInit {
  // Mặc định cho cài đặt bán khi hết hàng
  private defaultSettings = { allowSellWhenOutOfStock: true };
  
  // Dữ liệu từ inject
  data: OrderProductPickerModalData;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: OrderProductPickerModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: OrderProductPickerModalData,
    @Optional() private dialogRef?: MatDialogRef<OrderProductPickerModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<OrderProductPickerModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || { list: [] };
    
    // Đảm bảo các giá trị mặc định
    this.data.settings = this.data.settings || this.defaultSettings;
    this.data.data = this.data.data || [];
    this.data.searchTerm = this.data.searchTerm || '';
    this.data.selectedWarehouseId = this.data.selectedWarehouseId ?? null;
    this.data.warehouseList = this.data.warehouseList || [];
    this.data.categoryList = this.data.categoryList || [];
    this.data.brandList = this.data.brandList || [];
  }

  ngOnInit(): void {
    // Đảm bảo rằng các trường bắt buộc có giá trị
    if (!this.data.list) {
      console.error('OrderProductPickerModalComponent: list là trường bắt buộc');
      this.close(null);
    }
  }

  /**
   * Xử lý khi đã chọn xong sản phẩm và nhấn thêm vào đơn
   */
  onAddItems(items: OrderItemBaseDetails[]): void {
    this.close(items);
  }

  /**
   * Xử lý khi reset lựa chọn sản phẩm
   */
  onResetSelection(): void {
    // Có thể xử lý bất kỳ logic nào khi reset selection
  }
  
  /**
   * Đóng modal
   */
  private close(result: OrderItemBaseDetails[] | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
