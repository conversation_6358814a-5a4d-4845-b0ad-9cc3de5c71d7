{"FIELD_FILTERS": {"TITLE": "<PERSON><PERSON> l<PERSON>c trường", "NO_FIELDS": "<PERSON><PERSON><PERSON><PERSON> có trường nào để lọc", "CLEAR_ALL": "<PERSON><PERSON><PERSON> tất cả", "APPLY_FILTERS": "<PERSON><PERSON> d<PERSON> bộ lọc", "APPLY": "<PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>", "RESET_FILTERS": "Đặt lại bộ lọc", "OPERATORS": {"IS": "là", "ISNT": "không phải là", "CONTAINS": "ch<PERSON><PERSON>", "DOESNT_CONTAIN": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a", "STARTS_WITH": "b<PERSON>t đầu bằng", "ENDS_WITH": "kết thúc bằng", "IS_EMPTY": "tr<PERSON><PERSON>", "IS_NOT_EMPTY": "kh<PERSON>ng trống", "EQUALS": "bằng", "NOT_EQUALS": "không bằng", "LESS_THAN": "nhỏ hơn", "LESS_THAN_OR_EQUAL": "nhỏ hơn hoặc bằng", "GREATER_THAN": "l<PERSON><PERSON> h<PERSON>n", "GREATER_THAN_OR_EQUAL": "lớn hơn hoặc bằng", "BETWEEN": "trong khoảng", "NOT_BETWEEN": "kh<PERSON>ng trong khoảng", "AGE_IN": "tuổi trong", "DUE_IN": "<PERSON><PERSON><PERSON> hạn trong", "PREVIOUS": "tr<PERSON><PERSON><PERSON> đ<PERSON>y", "NEXT": "ti<PERSON><PERSON> theo", "ON": "v<PERSON>o ng<PERSON>y", "BEFORE": "tr<PERSON><PERSON><PERSON>", "AFTER": "sau", "TODAY": "hôm nay", "TOMORROW": "ng<PERSON>y mai", "TILL_YESTERDAY": "<PERSON><PERSON><PERSON> hôm qua", "STARTING_TOMORROW": "b<PERSON>t đ<PERSON>u từ ngày mai", "YESTERDAY": "hôm qua", "THIS_WEEK": "tu<PERSON><PERSON> n<PERSON>y", "THIS_MONTH": "th<PERSON>g n<PERSON>y", "PREVIOUS_WEEK": "tu<PERSON><PERSON> tr<PERSON><PERSON>", "PREVIOUS_MONTH": "th<PERSON><PERSON> t<PERSON>", "THIS_YEAR": "năm nay", "CURRENT_FY": "năm tài ch<PERSON>h hiện tại", "CURRENT_FQ": "quý tài ch<PERSON>h hiện tại", "PREVIOUS_YEAR": "năm tr<PERSON>", "PREVIOUS_FY": "năm tài ch<PERSON>h trước", "PREVIOUS_FQ": "quý tài ch<PERSON>h trước", "NEXT_YEAR": "năm sau", "NEXT_FQ": "quý tài ch<PERSON>h sau", "IS_NOT": "không phải là"}, "TIME_UNITS": {"DAYS": "ng<PERSON>y", "WEEKS": "tuần", "MONTHS": "th<PERSON>g", "YEARS": "năm"}, "CHECKBOX_VALUES": {"SELECTED": "<PERSON><PERSON><PERSON><PERSON>", "NOT_SELECTED": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ch<PERSON>n"}, "PLACEHOLDERS": {"ENTER_VALUE": "<PERSON><PERSON><PERSON><PERSON> giá trị", "ENTER_MIN_VALUE": "<PERSON><PERSON><PERSON><PERSON> giá trị tối thiểu", "ENTER_MAX_VALUE": "<PERSON><PERSON><PERSON><PERSON> giá trị tối đa", "SELECT_DATE": "<PERSON><PERSON><PERSON>", "SELECT_START_DATE": "<PERSON><PERSON><PERSON> ng<PERSON> b<PERSON><PERSON> đầu", "SELECT_END_DATE": "<PERSON><PERSON><PERSON> ng<PERSON>y kết thúc", "START_DATE": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "END_DATE": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "MIN_VALUE": "<PERSON><PERSON><PERSON> trị tối thiểu", "MAX_VALUE": "<PERSON><PERSON><PERSON> trị tối đa", "ENTER_NUMBER": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SELECT_VALUES": "<PERSON><PERSON><PERSON> giá trị", "SELECT_OPERATOR": "<PERSON><PERSON><PERSON> to<PERSON> tử", "SELECT_TIME_UNIT": "<PERSON><PERSON><PERSON> đơn vị thời gian", "SELECT_CHECKBOX_VALUE": "<PERSON><PERSON><PERSON> giá trị", "ENTER_EXACT_VALUE": "<PERSON><PERSON><PERSON><PERSON> giá trị ch<PERSON>h xác", "ENTER_VALUE_TO_EXCLUDE": "<PERSON><PERSON><PERSON><PERSON> giá trị cần loại trừ", "ENTER_TEXT_TO_SEARCH": "<PERSON><PERSON><PERSON><PERSON> văn bản cần tìm", "ENTER_TEXT_TO_EXCLUDE": "<PERSON><PERSON><PERSON><PERSON> văn bản cần lo<PERSON>i trừ", "ENTER_STARTING_TEXT": "<PERSON><PERSON><PERSON><PERSON> văn bản bắt đầu", "ENTER_ENDING_TEXT": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>n bản kết thúc"}, "LABELS": {"AND": "và", "FROM": "từ", "TO": "<PERSON><PERSON><PERSON>", "VALUE": "<PERSON><PERSON><PERSON> trị", "MIN_VALUE": "<PERSON><PERSON><PERSON> trị tối thiểu", "MAX_VALUE": "<PERSON><PERSON><PERSON> trị tối đa", "TIME_VALUE": "<PERSON><PERSON><PERSON> trị thời gian", "TIME_UNIT": "Đơn vị thời gian", "OPERATOR": "<PERSON><PERSON> tử", "DATE": "<PERSON><PERSON><PERSON>", "START_DATE": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "END_DATE": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "CHECKBOX_STATE": "Trạng thái checkbox"}, "VALIDATION": {"REQUIRED": "Trư<PERSON>ng này là bắ<PERSON> buộc", "INVALID_NUMBER": "<PERSON><PERSON> lòng nh<PERSON>p số hợp lệ", "INVALID_DATE": "<PERSON><PERSON> lòng nh<PERSON>p ng<PERSON>y hợp lệ", "MIN_GREATER_THAN_MAX": "Giá trị tối thiểu không thể lớn hơn giá trị tối đa", "REQUIRED_VALUE": "<PERSON><PERSON> lòng nhập giá trị", "REQUIRED_NUMBER": "<PERSON><PERSON> lòng nhập số", "INVALID_EMAIL": "<PERSON><PERSON> h<PERSON> l<PERSON>", "INVALID_URL": "URL không hợp lệ", "BOTH_VALUES_REQUIRED": "<PERSON><PERSON><PERSON> <PERSON>h<PERSON>p cả hai giá trị", "MIN_LESS_THAN_MAX": "Giá trị tối thiểu phải nhỏ hơn giá trị tối đa", "DATE_REQUIRED": "<PERSON><PERSON> lòng chọn ng<PERSON>y", "BOTH_DATES_REQUIRED": "<PERSON><PERSON><PERSON> chọn cả hai ngày", "START_DATE_BEFORE_END_DATE": "<PERSON><PERSON><PERSON> b<PERSON>t đầu phải trước ngày kết thúc", "POSITIVE_NUMBER_REQUIRED": "<PERSON><PERSON> lòng nhập số dương", "SELECT_AT_LEAST_ONE": "<PERSON><PERSON> lòng chọn ít nhất một mục"}, "MESSAGES": {"NO_FILTERS_ACTIVE": "<PERSON><PERSON><PERSON><PERSON> có bộ lọc nào đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>t", "FILTERS_ACTIVE": "{{count}} bộ lọc đang hoạt động", "VALIDATION_FAILED": "<PERSON><PERSON> lòng kiểm tra lại các bộ lọc", "NO_INPUT_REQUIRED": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>n nhập giá trị", "NO_OPTIONS_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tùy chọn nào", "SELECTED_COUNT": "<PERSON><PERSON> chọn {{count}} mục"}, "EXPLANATIONS": {"CHECKBOX_SELECTED": "<PERSON><PERSON>n thị các bản ghi có checkbox đ<PERSON><PERSON><PERSON> chọn", "CHECKBOX_NOT_SELECTED": "<PERSON><PERSON>n thị các bản ghi có checkbox chưa đ<PERSON><PERSON><PERSON> chọn"}}}