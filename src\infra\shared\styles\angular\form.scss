/**
 * Form field
 */

.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: var(--tw-warning-active-light) !important;
}
/* Override fix lỗi gạch dọc trong form fields */
.mdc-notched-outline__notch {
  border-right: none !important;
  border-left: none !important;
}
/** disable bottom trong form field */
.mat-form-field-no-subscript {
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}

.mat-mdc-button,
.mat-mdc-raised-button {
  display: inline-block;
  position: relative;

  margin: 0 0 0.875em 0; /* --button-margin-bottom không định nghĩa */
  border: 1px solid currentColor !important; /* --button-border-color không định nghĩa */
  box-shadow: 0 0.375em 0 currentColor; /* --button-box-shadow không định nghĩa */
  -webkit-user-select: none;
  user-select: none;
  background: #fff !important; /* --button-background không định nghĩa */
  cursor: pointer;
  padding: calc((4em - (1em * 1.5) - (0.125em * 2) - 0.375em) / 2) calc(1em * 1.5); /* --button-padding không định nghĩa */
  vertical-align: middle;
  text-align: center;
  text-decoration: none;
  color: #183153; /* --button-color không định nghĩa */
  font-weight: 600 !important; /* --button-font-weight không định nghĩa */
  box-shadow: 0 0.375em 0 #183153 !important;
  color: #183153 !important;


  transform: translate(0, 0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
  font-size: 16px !important;

  &:hover {
    transition-property: transform;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .15s;

    transform: translate(0, 0) rotate(0) skewX(0) skewY(0) scaleX(0.98) scaleY(0.98);
  }
  &:active {
    box-shadow: 0 0 1rem 0 rgba(140, 152, 164, .25) !important;
    transform: translateY(5px);
  }

  &.mat-primary  {
    background: #ffd43b !important; /* --button-background không định nghĩa */
  }
}




[type=button]:not(:disabled), [type=reset]:not(:disabled), [type=submit]:not(:disabled), button:not(:disabled) {
  outline: none;
}

button {
  background: none;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}


.mat-mdc-form-field {
  &.disabled {
    opacity: .5;
    pointer-events: none;
  }
}

// fix ios
button[matsuffix] {
  color: #333;
  padding: 0;
}

.mat-button-toggle {
  &.has-error {
    border: 1px solid #f10;
    color: #f10;
  }
  &.success {
    border: 1px solid #0dd715;
    color: #19ad1f !important;
    background: none !important;
  }
}
.input-datetime {
  width: 100%;
  border: 1px solid #aaa;
  border-radius: 5px;
  padding: 15px 0;
  position: relative;

  &.has-error {
    border: 1px solid #f10;
  }

  label {
    position: absolute;
    background-color: #fff;
    top: -10px;
    left: 10px;
    padding: 0 5px;
    font-size: 12px;
  }

  input {
     // fix ios
    background: none !important;
    color: #333;

    border: 0;
    width: 100%;
    font-size: 18px;
    padding: 5px 15px;
    outline: none;
  }

  // fix ios
  input::-webkit-date-and-time-value {
    text-align: left;
  }
}
