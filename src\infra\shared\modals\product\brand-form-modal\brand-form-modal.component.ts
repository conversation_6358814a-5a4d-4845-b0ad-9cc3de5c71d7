import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ProductBrand } from '@mock/product_form';

@Component({
  selector: 'app-brand-form-modal',
  templateUrl: './brand-form-modal.component.html',
  styleUrls: ['./brand-form-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule
  ]
})
export class BrandFormModalComponent implements OnInit {
  brandForm: FormGroup;
  dialogTitle = 'Thêm thương hiệu mới';
  data: { brand?: ProductBrand };

  constructor(
    private fb: FormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: { brand?: ProductBrand },
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: { brand?: ProductBrand },
    @Optional() private dialogRef?: MatDialogRef<BrandFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<BrandFormModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {};

    this.brandForm = this.fb.group({
      _id: ['', []],
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      country: ['', []],
      description: ['', []],
      website: ['', []],
      isActive: [true, []]
    });
  }

  ngOnInit(): void {
    if (this.data && this.data.brand) {
      this.dialogTitle = 'Chỉnh sửa thương hiệu';
      this.brandForm.patchValue(this.data.brand);
    }
  }

  onNoClick(): void {
    this.close();
  }

  close(result?: any): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  onSubmit(): void {
    if (this.brandForm.valid) {
      const formValue = this.brandForm.value;

      // Tạo ID mới nếu là thêm mới
      if (!formValue._id) {
        formValue._id = `brand_${Date.now().toString()}`;
      }

      this.close(formValue);
    } else {
      // Đánh dấu tất cả các trường là đã chạm để hiển thị lỗi
      Object.keys(this.brandForm.controls).forEach(key => {
        const control = this.brandForm.get(key);
        control?.markAsTouched();
      });
    }
  }
}
