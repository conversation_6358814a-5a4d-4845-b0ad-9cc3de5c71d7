import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { 
  TaxFormModalComponent, 
  TaxFormModalData, 
  TaxFormModalResult 
} from './tax-form-modal.component';
import { TaxInfo } from 'salehub_shared_contracts/entities/ims/inventory/goods_receipt';

/**
 * Service để mở modal thêm/sửa thuế
 */
@Injectable({
  providedIn: 'root'
})
export class TaxFormModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal thêm/sửa thuế
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<TaxInfo | undefined> Kết quả từ modal
   */
  async open(data: TaxFormModalData = {}): Promise<TaxFormModalResult> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '95vw'
      };
      
      const result = await this.responsiveModalService.open<
        TaxFormModalComponent,
        TaxFormModalData,
        TaxFormModalResult
      >(TaxFormModalComponent, modalConfig);
      
      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal thuế:', error);
      return undefined;
    }
  }
}
